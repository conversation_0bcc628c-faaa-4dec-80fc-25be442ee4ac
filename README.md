# Project setup

Requires Node JS 14. If using nvm, point the node js installation accordingly.

npm install
Run the setup script to copy the latest Unvired SDK wrapper.
$ ./setup.sh // Mac
$ ./setup-windows // Windows

<code>
$ ionic cordova platform add browser # One time setup
$ ionic cordova run browser
</code>

# Updating UnviredSDK

In order to update UnviredSDK to the latest version, you can do the following steps:

```
$ ionic cordova plugin rm cordova-plugin-unvired-sdk
$ ionic cordova plugin add cordova-plugin-unvired-sdk
$ ./setup.sh (Mac) OR ./setup-windows.bat (Windows)
```

After you update the plugin, verify the same by

``` 
$ ionic cordova plugin ls
cordova-plugin-unvired-sdk <latest_version_number> "UnviredSDK"
```

This process updates an entry onto the package.json file. This change needs to be committed to the source control. 

Other users working on this project will have to update their source code and also update the plugin locally as described in the steps above.