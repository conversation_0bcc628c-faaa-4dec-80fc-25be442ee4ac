{"ios": {"debug": {"codeSignIdentity": "iPhone Developer", "developmentTeam": "FZU567HQX9", "packageType": "development", "automaticProvisioning": true}, "release": {"codeSignIdentity": "iPhone Developer", "developmentTeam": "FZU567HQX9", "packageType": "app-store", "automaticProvisioning": true, "authenticationKeyPath": "/Volumes/Build/Jenkins/build/workspace/App_Unvired_Work_Permit_iOS/Code_Signing/AuthKey_7AKTPF48U7.p8", "authenticationKeyID": "7AKTPF48U7", "authenticationKeyIssuerID": "69a6de84-8c72-47e3-e053-5b8c7c11a4d1"}}, "android": {"debug": {"keystore": "Code_Signing/inspections-key.keystore", "storePassword": "Unvired123*", "alias": "carmeuse_inpections_key", "password": "Unvired123*", "keystoreType": "", "packageType": "apk"}, "release": {"keystore": "Code_Signing/inspections-key.keystore", "storePassword": "Unvired123*", "alias": "carmeuse_inpections_key", "password": "Unvired123*", "keystoreType": "", "packageType": "bundle"}}}