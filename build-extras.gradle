android {
    configurations {

        all*.exclude group: 'com.squareup.okhttp3', module: 'okhttp'
        all*.exclude group: 'com.google.guava', module: 'listenablefuture'
        all*.exclude group: 'com.google.guava', module: 'guava'

    }

    packagingOptions {
        pickFirst 'META-INF/LICENSE.txt'
        pickFirst 'META-INF/LICENSE'
        pickFirst 'META-INF/NOTICE.txt'
        pickFirst 'META-INF/NOTICE'
        pickFirst 'META-INF/DEPENDENCIES'
        pickFirst 'META-INF/ASL2.0'
        pickFirst 'META-INF/*.kotlin_module'
    }
}

dependencies {
    constraints {
        implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0") {
            because("kotlin-stdlib-jdk7 is now a part of kotlin-stdlib")
        }
        implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0") {
            because("kotlin-stdlib-jdk8 is now a part of kotlin-stdlib")
        }
    }
}
