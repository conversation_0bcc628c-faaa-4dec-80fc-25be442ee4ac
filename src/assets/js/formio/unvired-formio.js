
var x;
var tempadata = '';
var submitResolve;
var submitReject;
var id = '';
var form = {};
var browserPlatform;
var formSubmissionData; x
var approveEvent = false;
var rejectEvent = false;
var returneEvent = false;
var originalDataStr;
var attributeJson;


function loadForm(form, submissionData, formReadOnlyFlag, translator, userInfo, company, isAndroid, iosPlatform, isBrowserPlatform, type, formAttrs, loggedInUserIds, userData, permitUserApprovalType, stakeholderDataForm) {
    translator = "";
    userInfo = ""
    company = "";
    isAndroid = false;
    iosPlatform = false;
    isBrowserPlatform = true;
    type = "";
    formAttrs = [];
    loggedInUserIds ="";

    window.platform = {};
    window.formId = form.formId;
    window.platform.isAndroid = isAndroid;
    window.platform.iosPlatform = iosPlatform;
    window.platform.submissionData = submissionData;

    localStorage.removeItem('flatten');
    localStorage.removeItem('renderMode');

    let flatten = (type && type == "print") ? true : false;
    localStorage.setItem('flatten', flatten);
    localStorage.setItem('renderMode', 'form');
    attributeJson = formAttrs;
    // form = '{"formId":"31BDE8FC7E1E4885A9AE2FFC8203D877","title":"Save And Complete Test","description":"Save And Complete Test","display":"form","type":"form","name":"saveandcompletetest","path":"saveandcompletetest","tags":"Demo,UNVIRED","avatar":"description","formtype":"form","usage":"masterdata","localExecute":false,"components":[{"html":"<p>This is a form for testing Save and Complete Message:</p><ol><li>Save<ol><li>There are 2 direct Mandatory fields</li><li>If the Checkbox AddField is checked, a new field is shown which is made mandatory</li></ol></li><li>Complete<ol><li>Depends on the mandatory fields above</li><li>If Name field (not mandatory) is filled in only then it can be completed as it controls the complete check</li></ol></li></ol>","label":"Help","refreshOnChange":false,"key":"help","type":"content","input":false,"tableView":false,"hidden":false},{"label":"Text Field","tableView":true,"validate":{"required":true},"key":"textField","type":"textfield","input":true,"hidden":false},{"label":"Number","mask":false,"tableView":false,"delimiter":false,"requireDecimal":false,"inputFormat":"plain","validate":{"required":true},"key":"number","type":"number","input":true,"hidden":false},{"label":"Name","placeholder":"Print your name to complete form","tableView":true,"case":"uppercase","key":"name","type":"textfield","input":true,"hidden":false},{"label":"Complete Control","hidden":true,"tableView":false,"defaultValue":false,"clearOnHide":false,"calculateValue":"value = data.name.length > 0;","key":"completeControl","type":"checkbox","input":true},{"label":"Add Field","tableView":false,"key":"addField","type":"checkbox","input":true,"defaultValue":false,"hidden":false},{"label":"New Field","tableView":true,"redrawOn":"addField","clearOnHide":false,"key":"newField","logic":[{"name":"Hide","trigger":{"type":"simple","simple":{"show":true,"when":"addField","eq":"false"}},"actions":[{"name":"Hide Field","type":"property","property":{"label":"Hidden","value":"hidden","type":"boolean"},"state":true},{"name":"Not needed","type":"property","property":{"label":"Required","value":"validate.required","type":"boolean"},"state":false}]},{"name":"Show Mandatory","trigger":{"type":"simple","simple":{"show":true,"when":"addField","eq":"true"}},"actions":[{"name":"Show It","type":"property","property":{"label":"Hidden","value":"hidden","type":"boolean"},"state":false},{"name":"Need It","type":"property","property":{"label":"Required","value":"validate.required","type":"boolean"},"state":true}]}],"type":"textfield","input":true,"hidden":false},{"input":true,"tableView":false,"label":"Submit","key":"submit","clearOnHide":false,"spellcheck":false,"type":"button","rows":0,"wysiwyg":false,"lockKey":true,"hidden":false}]}';
    //  form = JSON.parse(form);

    if (userInfo) {
        window.form = {};
        window.form.company = company;

        window.form.user = userInfo.USERNAME
        window.form.firstName = userInfo.FNAME
        window.form.lastName = userInfo.LNAME
        window.form.email = userInfo.EMAIL
        window.form.role = userInfo.ROLE
        window.form.phoneNo = userInfo.PHONE_NO
        window.form.isAndroid = isAndroid
        window.formId = form.formId;
        window.form.teams = JSON.stringify(loggedInUserIds);
    }

    if (formReadOnlyFlag) {
        let obj = JSON.stringify(form);
        form = JSON.parse(obj.replace(/\"type\":\"barcodeselect\"/g, '\"type\":\"select\"'));
    }

    if (flatten) {
        form = setFileImageSize(form, "type", "file")
        let obj = JSON.stringify(form);
        form = JSON.parse(obj.replace(/\"type\":\"datetime\"/g, '\"type\":\"textarea\"'));
    }
    window.form.permitUserRoleData = userData;
    window.form.permitUserApprovalType = permitUserApprovalType;
    window.form.stakeholderDataForm = stakeholderDataForm;
    // let apprType = [{'APPR_TYPE':'CONFINED'}];
    // window.form.permitUserApprovalType = apprType;
    approveEvent = false;
    rejectEvent = false;
    returneEvent = false;
    Formio.icons = 'fontawesome';
    localStorage.setItem('language','en');
    Formio.createForm(
        document.getElementById('formio'),
        form, {
        readOnly: false,
        flatten: (type && type == "print") ? true : false,
        noAlerts: true,
        language: localStorage.getItem('language'),
        i18n: translator
    }
    )
        .then(form => {

            if (flatten) {
                form.triggerRedraw();
            }
            /* Extract the HTML file of form - 
              document.getElementById('formio').innerHTML
           */
            formObj = form;
            browserPlatform = isBrowserPlatform;
            formSubmissionData = submissionData;
            beforeRender(form, submissionData)

        })
        .catch(err => console.log('Error while fetching form: ', err));
}

function getLocation() {
    console.log("formiojs - getLocation called");
    return new Promise((resolve, reject) => {
        let eventCustom = new CustomEvent('getLocationFromApp', {
            detail: {controlId: id}
        });
        document.dispatchEvent(eventCustom);
        resolve('aa');
    });
}
function setLocation(location, id) {
console.log("id = "+ id)
    let eventCustom = new CustomEvent('setLocationFromApp', {
        detail: { data: location,
            componentID: id }
    });
    document.dispatchEvent(eventCustom);
}

function beforeRender(form, submissionData) {
    window.platform.submissionData = submissionData;
    form.submission = {
        data: submissionData
    }

    form.nosubmit = true;

    x = {
        "submission": '',
        "error": ''
    };
    $('[name="data[saveDraft]"]').hide();
    $('[name="data[submit]"]').hide();

    // Triggers when submit button clicks.
    form.on('submit', function (submission) {

        $('[name="data[unvSubmit]"]').hide();
        x = {
            "submission": submission,
            "error": '',
            "tempData": ''
        };

        // If no error in a form, then it calls for submit so calculate percenatge is defaulted to 100
        let calc = calcCompletionPer(form);
        let eventCustom = new CustomEvent('BtnAction', {
            detail: {
                calculationPercentage: calc,
                errObj: form.errors,
                tempData: x
            }
        });
        document.dispatchEvent(eventCustom);
        submitResolve(x);
    });

    //  Hiding wizard submit buttons.
    form.on('render', function (submission) {
        if ($('.btn-wizard-nav-next').length > 0) { } else {
            $('.btn-wizard-nav-submit').hide();
            $('[name="data[unvSubmit]"]').hide();
            $('[name="data[submit]"]').hide();
        }
    });

    document.addEventListener('sdcDoneEvent', function (event) {
        let eventData = event.detail;
        if (eventData.data.error) {
            let eventCustom = new CustomEvent('displaySDCWorkflowError', {
                detail: {
                    errorMsg: eventData.data.error
                }
            });
            document.dispatchEvent(eventCustom);
        }
        if (eventData && eventData.data && !eventData.data.error) {
            beforeRender(form, eventData.data)
        }
    });
    // }, {
    //     once: true
    // });


    form.formReady.then(() => {
        console.log("formReady called")
        // Dispatching event that the form loading is complete. This is the time to load CSS.
        let openEve = new CustomEvent('onFormOpen', { detail: { "fieldName": "", "event": "OPEN", "timestamp": new Date().getTime(), "additionalData": "" } });
        document.dispatchEvent(openEve);
    });

    form.on('start', function (data) {
    console.log("start called")
    });


    // Triggers when next button clicks in wizard.
    form.on('nextPage', function (submission) {
        x = {
            "submission": submission.submission,
            "error": ''
        };
        $('.btn-wizard-nav-submit').hide();
        $('[name="data[unvSubmit]"]').hide();
        submitResolve(x);
    });

    // Triggers when error occurs while filling form.
    form.on('error', function (error) {
        $('.btn-wizard-nav-next').prop('disabled', false);
        $('.btn-wizard-nav-submit').hide();
        $('[name="data[unvSubmit]"]').hide();
        $('.button-icon-right').css("display", "none");
        if (error && error[0].component && error[0].component.validate && error[0].component.validate.required == true) {
            x = {
                "submission": '',
                "error": 'validation error',
                "tempData": tempadata
            };
        } else {
            x = {
                "submission": '',
                "error": error
            };
        }
        submitReject(x);

        // Error Event 
        let errEve = new CustomEvent('onError', { detail: { "fieldName": "", "event": "ERRORS", "timestamp": new Date().getTime(), "additionalData": error.map(function (a) { return JSON.stringify(a.component.label); }) } });
        document.dispatchEvent(errEve);
        // Calculate completion percentage
        let calc = calcCompletionPer(form);
        let eventCustom = new CustomEvent('BtnAction', {
            detail: {
                calculationPercentage: calc,
                errObj: form.errors,
                tempData: x
            }
        });
        document.dispatchEvent(eventCustom);
    });

    // Triggers when change occurs while filling form.
    form.on('change', function (submission) {
        if (submission && submission.changed && submission.changed.instance && submission.changed.instance.parent && submission.changed.instance.parent.parent && submission.changed.instance.parent.parent.type) {
            if (submission.changed.instance.parent.parent.type === 'form') {
                if (originalDataStr && originalDataStr.form) {
                    originalDataStr.form.data = submission.data;
                } else {
                    originalDataStr = submission.data;
                }
            }else{
                originalDataStr = submission.data;
            }
        } else {
            if (submission && submission.changed) {
                originalDataStr = submission.data
            } else {
                if (submission && submission.changed === undefined && submission.data) {
                    originalDataStr = submission.data;
                    localStorage.setItem("initialFormData", JSON.stringify(submission.data));
                    let eventCustom = new CustomEvent('FormRenderingComplete', {});
                    document.dispatchEvent(eventCustom);
                }
            }
        }
        tempadata = {
            data: originalDataStr
        };

        x = {
            "submission": '',
            "error": '',
            "tempData": tempadata
        };

        // call change event only if the data is changed 
        if (submission.changed && submission.changed.flags.fromSubmission == undefined) {
            let changeEve = new CustomEvent('onChange', { detail: { "fieldName": submission.changed.component.key, "event": "CHANGE", "timestamp": new Date().getTime(), "additionalData": "" } });
            document.dispatchEvent(changeEve);
        }
        // Calculate completion percentage
        let cal = calcCompletionPer(form);
        let eventCustom = new CustomEvent('BtnAction', {
            detail: {
                calculationPercentage: cal,
                errObj: form.errors,
                tempData: x
            }
        });
        document.dispatchEvent(eventCustom);
    });

    form.on('focus', function (data) {
        let focusEve = new CustomEvent('onFocus', { detail: { "fieldName": data.component.key, "timestamp": new Date().getTime(), "event": 'START', "additionalData": "" } });
        document.dispatchEvent(focusEve);
    });

    form.on('blur', function (data) {
        let blurEve = new CustomEvent('onBlur', { detail: { "fieldName": data.component.key, "timestamp": new Date().getTime(), "event": 'END', "additionalData": "" } });
        document.dispatchEvent(blurEve);
    })

    document.addEventListener('barcodeChangeEvent', (e) => {
        form.triggerChange();
    }, false);

    // Handlling focus on signature-pad
    let signaturePad = document.querySelector('.signature-pad-canvas');
    if (signaturePad !== undefined && signaturePad !== null) {
        signaturePad.addEventListener('touchstart', () => {
            document.activeElement.blur();
        }, false);
    }

    form.on("ApproveEvent", function (data) {
        if (approveEvent === false) {
            approveEvent = true;
            submitFormViaActionButton('approved');
        }
        setTimeout(() => {
            approveEvent = false;
        }, 1000);

    });

    form.on("RejectEvent", function (data) {
        if (rejectEvent === false) {
            rejectEvent = true;
            submitFormViaActionButton('rejected');
        }
        setTimeout(() => {
            rejectEvent = false;
        }, 1000);
    });

    form.on("ReturnRequestorEvent", function (data) {
        if (returneEvent === false) {
            returneEvent = true;
            submitFormViaActionButton('returned');
        }
        setTimeout(() => {
            returneEvent = false;
        }, 1000);
    });

    function submitFormViaActionButton(action) {
        let eventCustom = new CustomEvent('submitFormViaActionButton', { detail: { "action": action } });
        document.dispatchEvent(eventCustom);
    }
}


// Form submit button function.
function submit() {
    return new Promise((resolve, reject) => {
        submitResolve = resolve;
        submitReject = reject;
        // $('[name="data[submit]"]').click();
        // document.querySelectorAll('[name="data[submit]"]')[0].click();
        formObj.submit();
    })
}


// Wizard submit button function.
function wizardSubmit() {
    if ($('.btn-wizard-nav-next').length > 0) {
        return new Promise((resolve, reject) => {
            submitResolve = resolve;
            submitReject = reject;
            $('.btn-wizard-nav-next').click();
        })
    } else {
        return new Promise((resolve, reject) => {
            submitResolve = resolve;
            submitReject = reject;
            $('[name="data[unvSubmit]"]').click();
        })
    }
}

// Returns change event submission data.
function returnTempData() {
    return new Promise((resolve, reject) => {
        // if(flag){
        // setTimeout(function () {
        //     resolve(x);
        // }, 500);
        //  }else{
        //   resolve(x);
        //  }
        resolve(x);
    })
}

// Returns last change submission data.
function returnLastTempData() {
    return new Promise((resolve, reject) => {
        resolve(x);
    })
}

// Calculate percentage for mandatory fields on form change, submit and errors
let mandatory = 0;
let filledMandatory = 0;
let compFlag = false;
let componentsType = false;
let nestedForm = false;
let nestedParentComponents = false;
let nestedFormPath;
function calcCompletionPer(form) {
    let completionPercentage = 0;
    var formComponent;
    if (form.component && !form.component.hidden && !form.component.disabled) {
        if (!form.components) {
            formComponent = form.formObj ? form.formObj.components : [];
            nestedForm = true;
            nestedFormPath = form.path;
        } else {
            nestedForm = false;
            nestedFormPath = "";
            mandatory = 0;
            filledMandatory = 0;
            formComponent = form.components;
        }
    }
    FormioUtils.eachComponent(formComponent, (e) => {
        compFlag = false;
        componentsType = false;
        nestedParentComponents = false;
        if ((e.component && !e.component.hidden && !e.component.disabled && e.visible) || (e && e.hidden !== undefined && !e.hidden && e.disabled !== undefined && !e.disabled && e.visible)) {
            if (!nestedForm && (e.type == "columns" || e.type == "fieldset" || e.type == "panel" || e.type == "table" || e.type == "tabs" || e.type == "well")) {
                componentsType = true;
            }
            if (nestedForm && (e.type == "columns" || e.type == "fieldset" || e.type == "panel" || e.type == "table" || e.type == "tabs" || e.type == "well")) {
                nestedParentComponents = true;
            }
            if (e.parent && e.parent.type && e.parent.type == "form") { // first nested form and then the acutal form
                nestedForm = false;
            }
            if (e.component && !e.component.hidden) {
                if (e.type == "form") { // Support Nested component
                    calcCompletionPer(e);
                } else if (e && e.type == "smarttable") { // smart table 
                    componentsCalculation("smartTable", e, form, nestedForm, nestedFormPath);
                } else if (e && e.type !== "components" && !componentsType && !nestedParentComponents && e.type !== "datamap" && e.type !== "container" && e.type == "editgrid" && e.component.components.length > 0) { // edit grid to calculate mandatory fields
                    componentsCalculation("editGrid", e, form, nestedForm, nestedFormPath);
                } else if (e && e.type !== "components" && !componentsType && !nestedParentComponents && e.type !== "datamap" && e.type !== "container" && e.components && e.components.length > 0) {
                    componentsCalculation("dataGrid", e, form, nestedForm, nestedFormPath);
                } else if (e && e.type !== "components" && !componentsType && !nestedParentComponents && (e.parent === undefined || (e.parent && e.parent.type !== "components" && e.parent.type !== "editgrid")) && (e.type !== "container" || e.key == "address") && (nestedForm || (e.component && e.component.validate))) {
                    componentsCalculation("otherComp", e, form, nestedForm, nestedFormPath);
                } else if (e && e.type === "components" || componentsType) { // To support components inside Layout component
                    if (e.components && e.components.length > 0) {
                    e.components.forEach(comp => {
                        let compLs;
                        if (nestedForm) {
                            compLs = comp;
                        } else {
                            compLs = comp.component
                        }
                        if (!compLs.hidden) {
                            if (compLs && compLs.type == "smarttable") {
                                componentsCalculation("smartTable", comp, form, nestedForm, nestedFormPath);
                            } else if (compLs && compLs.type !== "datamap" && compLs.type !== "container" && compLs.type == "editgrid" && compLs.components.length > 0) { // edit grid to calculate mandatory fields
                                componentsCalculation("editGrid", comp, form, nestedForm, nestedFormPath);
                            } else if (compLs && compLs.type !== "datamap" && compLs.type !== "container" && compLs && compLs.length > 0) {
                                componentsCalculation("dataGrid", comp, form, nestedForm, nestedFormPath);
                            } else if (compLs && (nestedForm || (comp.parent && comp.parent.type !== "editgrid")) && (compLs.type !== "container" || compLs.key == "address") && (nestedForm || (compLs && compLs.validate))) {
                                componentsCalculation("otherComp", comp, form, nestedForm, nestedFormPath);
                            }
                        }
                    })
                }
            }
        }
        }
    }, true);

    // let completeFld = false;
    //   let clientCompField = attributeJson ? attributeJson.filter(e => { return e.key.trim() == "client-complete-check-field" }) : [];
    //   if (clientCompField.length > 0 && tempadata && tempadata.data && clientCompField[0].value !== "") {
    //     completeFld = tempadata.data[clientCompField[0].value];
    //     mandatory = mandatory + 1;
    //     if(completeFld){
    //         mandatory = mandatory - 1;
    //     }
    //   }

    console.log('mandatory fields ', mandatory);
    console.log('filledMandatory fields ', filledMandatory);
    if (mandatory !== 0) {
        completionPercentage = ((filledMandatory / mandatory) * 100);
    } else {
        completionPercentage = 100;
    }
    return completionPercentage;
}

function componentsCalculation(type, e, form, nestedForm, nestedFormPath) {
    let comp;
    let formPath;
    if (nestedForm && form.data[nestedFormPath] && form.data[nestedFormPath].data) {
        comp = e;
        formPath = form.data[nestedFormPath].data;
    } else {
        comp = e.component;
        formPath = form.data;
    }
    switch (type) {
        case "smartTable":
            if (comp && comp.validate && comp.validate.required && (!comp.hidden && !comp.disabled)) {
                mandatory = mandatory + 1;
                if (formPath[comp.key] && formPath[comp.key].length > 0) {
                    filledMandatory = filledMandatory + 1;
                }
            }
            break;
        case "editGrid":
            if (comp && comp.validate && comp.validate.required && (!comp.hidden && !comp.disabled)) {
                mandatory = mandatory + 1;
                if (formPath[e.path].length > 0) {
                    filledMandatory = filledMandatory + 1;
                }
            } else {
                comp.components.forEach((ele, i) => {
                    if (ele && ele.validate && (!ele.hidden && !ele.disabled)) {
                        if (ele.validate.required) {
                            mandatory = mandatory + 1;
                            if (e.type == "editgrid" && formPath[e.path].length > 0) {
                                if (formPath[e.path][i][ele.key]) {
                                    filledMandatory = filledMandatory + 1;
                                }
                            }
                        }
                    }
                })
            }
            break;
        case "dataGrid":
            e.components.forEach(ele => {   // datagrid
                if (ele.type == "container") {     /// container indide DG
                    ele.components.forEach(e => {
                        if (e.component && e.component.validate && e.component.validate.required && (!e.component.hidden && !e.component.disabled)) {
                            mandatory = mandatory + 1;
                            if (formPath[e.parent.path][e.key]) {
                                filledMandatory = filledMandatory + 1;
                            }
                        } else if (e.type == "components") {  // layout component inside container -> DG
                            e.components.forEach(cmp => {
                                if (cmp.component && cmp.component.validate && cmp.component.validate.required && (!cmp.component.hidden && !cmp.component.disabled)) {
                                    mandatory = mandatory + 1;
                                    if (formPath && e.parent && e.parent.parent && e.parent.parent.path && formPath[e.parent.parent.path] && e.rowIndex && formPath[e.parent.parent.path][e.rowIndex] && e.parent.path && formPath[e.parent.parent.path][e.rowIndex][(e.parent.path).split('.')[1]][cmp.key]) {
                                        filledMandatory = filledMandatory + 1;
                                    }
                                }
                            })
                        }
                    })
                } else if (ele.component && ele.component.validate && (!ele.component.hidden && !ele.component.disabled)) {
                    if (ele.component.validate.required) {
                        mandatory = mandatory + 1;
                        if (e.type == "datagrid" && formPath[e.path][ele.rowIndex][ele.component.key]) { // data grid
                            filledMandatory = filledMandatory + 1;
                        } else if (e.type == "editgrid" && formPath[e.path].length > 0) {   // edit grid
                            if (formPath[e.path][ele.rowIndex][ele.component.key]) {
                                filledMandatory = filledMandatory + 1;
                            }
                        }
                    }
                }
            })
            break;
        case "otherComp":
            if (e.type == "selectboxes" || e.type == "file" || e.type == "datamap" || e.key == "address" || e.type == "tree" || e.type == "survey" || e.type == "select") {
                compFlag = true;
            }else{
                compFlag = false;
            }
            if (comp && comp.validate && comp.validate.required && (!comp.hidden && !comp.disabled)) {
                mandatory = mandatory + 1;
                if (e.type == "selectboxes" && Object.values(formPath[comp.key]).length > 0 && Object.values(formPath[e.path]).some(k => k)) {  // select box component
                    filledMandatory = filledMandatory + 1;
                } else if ((e.type == "datamap" || e.key == "address" || e.type == "tree") && Object.keys(formPath[comp.key]).length > 0) {
                    filledMandatory = filledMandatory + 1;
                } else if (e.type == "survey" && Object.keys(formPath[comp.key]).length == comp.questions.length) {   // survey component
                    filledMandatory = filledMandatory + 1;
                } else if ((e.type == "file" || e.type == "select") && String(formPath[comp.key]).length > 0) {    // for file component
                    filledMandatory = filledMandatory + 1;
                } else if (!compFlag) {
                    if (e.parent && e.parent.type == "container" && formPath[e.parent.path][comp.key]) {
                        filledMandatory = filledMandatory + 1;
                    } else if (formPath[comp.key] || (e.path && e.path.split('.')[1] && formPath[e.path.split('.')[0]] && formPath[e.path.split('.')[0]][e.path.split('.')[1]])) {  // satisfy a condition if its inside a container
                        filledMandatory = filledMandatory + 1;
                    }
                }
            } else if (e && (comp && !comp.hidden && !comp.disabled) && comp && comp.type == "day" && Object.keys(comp.fields).length > 0) {  // for day component
                if (Object.values(comp.fields).some(k => k.required)) {
                    mandatory = mandatory + 1;
                    if (formPath[comp.key] !== comp.defaultValue) {
                        filledMandatory = filledMandatory + 1;
                    }
                }
            } else if (e.parent && (!comp.hidden && !comp.disabled) && e.parent.type == "container" && e.parent.component.validate && e.parent.component.validate.required) {
                mandatory = mandatory + 1;
                if (formPath[e.parent.path][comp.key]) {
                    filledMandatory = filledMandatory + 1;
                }
            }
            break;
    }
}

function loadAnnotateForm(form, submissionData, annotateJson, users, readMode, vobArr) {
    localStorage.setItem('flatten', 'true');
    localStorage.setItem('renderMode', 'html');
    
    let obj = JSON.stringify(form);
    form = JSON.parse(obj.replace(/\"required\":true/g, '\"required\":false'));

    Formio.createForm(document.getElementById('annotateForm'), form,
        { renderMode: "html", flatten: true, readOnly: true, language: localStorage.getItem('language') }).then(function (form) {
            form.submission = { data: submissionData };
            form.nosubmit = true;
            form.on('change', () => {
                // Srinidhi 25 July 2022: Commenting out the redraw logic since the annotations were going away.
                // form.redraw();
                // form.triggerRedraw();
            })
            r = Recogito.init({
                content: document.getElementById('annotateForm'),
                // Srinidhi 25 July 2022: Commented out the readonly mode to true since we want to add new annotations.
                // readOnly: readMode, 
                locale: localStorage.getItem('language'),
                widgets: [
                    { widget: recogito.CommentsMention, userSuggestions: users, editable: 'MINE_ONLY' },
                    { widget: 'TAG', vocabulary: vobArr },
                ],
                mode: 'html'
            });

            r.setAuthInfo({
                id: window.form.email,
                displayName: window.form.firstName
            });

            // Add an event handler  
            r.on('createAnnotation', function (annotation) {

            });

            r.on('updateAnnotation', function (annotation) {

            });

            // Select annotate listener to make a css change for mobile view
            r.on('selectAnnotation', function (annotation) {
                // create a custom event for a mobile to set a custom css
                let eventCustom = new CustomEvent('annotateCss', { detail: true });
                document.dispatchEvent(eventCustom);
            })

            form.formReady.then(() => {
                setTimeout(function () {
                    r.setAnnotations(annotateJson);
                }, 500);
                let customEve = new CustomEvent('laodAnnotationComplete', {});
                document.dispatchEvent(customEve);
            });

        });
}

// Form submit button function.
function annotationSubmit() {
    return new Promise((resolve, reject) => {
        resolve(r.getAnnotations());
    })
}

function setFileImageSize(obj, key, val) {
    var objects = [];
    for (var i in obj) {
        if (typeof obj[i] == 'object') {
            objects = objects.concat(setFileImageSize(obj[i], key, val));
        } else if (i == key && obj[key] == val) {
            obj.imageSize = "100%";
        }
    }
    return obj;
}