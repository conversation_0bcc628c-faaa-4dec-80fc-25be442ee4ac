{"name": "PERMIT", "description": "Work Permit", "version": "1.0", "AGENT": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "AGENT_HEADER": {"description": "", "className": "com.unvired.permit.be.AGENT_HEADER", "header": true, "field": [{"name": "AGENT_ID", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_INTERNAL", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONTACT", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "PHONE", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "EMAIL", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ADDRESS", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_ACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "AGENT_FACILITY": {"className": "com.unvired.permit.be.AGENT_FACILITY", "field": [{"name": "AGENT_ID", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "FACILITY_ID", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "AGENT_USER": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "AGENT_USER_HEADER": {"description": "", "className": "com.unvired.permit.be.AGENT_USER_HEADER", "header": true, "field": [{"name": "AGENT_ID", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "USER_ID", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "APPROVAL_TYPE": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "APPROVAL_TYPE_HEADER": {"description": "", "className": "com.unvired.permit.be.APPROVAL_TYPE_HEADER", "header": true, "field": [{"name": "APPR_TYPE", "description": "", "isGid": true, "length": "10", "mandatory": true, "sqlType": "TEXT"}, {"name": "SCOPE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "CHART": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "CHART_HEADER": {"className": "com.unvired.permit.be.CHART_HEADER", "header": true, "field": [{"name": "CHART_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "CHART_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TITLE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "HAS_SERIES", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "CHART_AXIS": {"className": "com.unvired.permit.be.CHART_AXIS", "field": [{"name": "CHART_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "CATEGORY", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "VALUE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "CHART_SERIES": {"className": "com.unvired.permit.be.CHART_SERIES", "field": [{"name": "CHART_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "SERIES", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "CATEGORY", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "VALUE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "DIVISION": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "DIVISION_HEADER": {"description": "", "className": "com.unvired.permit.be.DIVISION_HEADER", "header": true, "field": [{"name": "FACILITY_ID", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "DIVISION_ID", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "DOCUMENT": {"description": "", "attachments": true, "onConflict": "SERVER_WINS", "save": true, "DOCUMENT_HEADER": {"description": "", "className": "com.unvired.permit.be.DOCUMENT_HEADER", "header": true, "field": [{"name": "DOC_ID", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "FOLDER_ID", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TITLE", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "FILE_NAME", "description": "", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "MIME_TYPE", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "DOC_TYPE", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "THUMBNAIL", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CHANGED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHANGED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "DOCUMENT_ATTACHMENT": {"description": "Attachment", "className": "com.unvired.permit.be.DOCUMENT_ATTACHMENT", "attachment": true, "field": [{"name": "UID", "description": "UID", "isGid": true, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "FILE_NAME", "description": "File Name", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "MIME_TYPE", "description": "Mime Type", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL", "description": "Download URL", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTERNAL_URL", "description": "External or Internal URL", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL_REQUIRES_AUTH", "description": "External URL Requires Authentication", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCAL_PATH", "description": "Path to the file on the device", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "NO_CACHE", "description": "Do not cache", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SERVER_TIMESTAMP", "description": "Server timestamp", "isGid": false, "length": "20", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TAG1", "description": "Tag 1", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG2", "description": "Tag 2", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG3", "description": "Tag 3", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG4", "description": "Tag 4", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG5", "description": "Tag 5", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "ATTACHMENT_STATUS", "description": "Status", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "AUTO_DOWNLOAD", "description": "Auto Download Flag", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PARAM", "description": "Name of the param", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "MESSAGE", "description": "Message from User", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}]}}, "FACILITY": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "FACILITY_HEADER": {"description": "", "className": "com.unvired.permit.be.FACILITY_HEADER", "header": true, "field": [{"name": "FACILITY_ID", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "BOUNDARY", "description": "", "isGid": false, "length": "4064", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "FORM": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "FORM_HEADER": {"description": "", "className": "com.unvired.permit.be.FORM_HEADER", "header": true, "field": [{"name": "FORM_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "FORM_VERSION", "isGid": true, "length": "0", "mandatory": true, "sqlType": "INTEGER"}, {"name": "FORM_NAME", "isGid": false, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "FORM_TITLE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "FORM_DESC", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TEMPLATE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CATEGORY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "AVATAR", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ATTRIBUTES", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "FORM_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_GET_FORM_TEMPLATE": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INPUT_GET_FORM_TEMPLATE_HEADER": {"description": "", "className": "com.unvired.permit.be.INPUT_GET_FORM_TEMPLATE_HEADER", "header": true, "field": [{"name": "CATEGORY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "FORM_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "FORM_VERSION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "WITH_DATA", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "PERMIT": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "PERMIT_HEADER": {"className": "com.unvired.permit.be.PERMIT_HEADER", "header": true, "field": [{"name": "PERMIT_NO", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "PERMIT_TYPE", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "FACILITY_ID", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "DIVISION_ID", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "AGENT_ID_INT", "description": "", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "AGENT_ID_EXT", "description": "", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "JOB_NO", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "STATUS", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "REQUESTED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "REQUESTED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "SOURCE", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "PERMIT_DATE", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "EXPIRY_DATE", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "IS_EXTENDED", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTENSION_DATE", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "COMMENTS", "description": "", "isGid": false, "length": "1000", "mandatory": false, "sqlType": "TEXT"}, {"name": "APPROVAL_SUMMARY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "PERMIT_STAKEHOLDER": {"description": "", "className": "com.unvired.permit.be.PERMIT_STAKEHOLDER", "field": [{"name": "PERMIT_NO", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "ROW_ID", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "SEQ_NO", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "ROLE", "description": "", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "APPR_TYPE", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "APPROVAL", "description": "O Open\nA Approved\nR Rejected", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PROCESSED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CONTEXT", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "AGENT_ID", "description": "", "isGid": false, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "USER_ID", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMMENT", "description": "", "isGid": false, "length": "1000", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_ACTIVE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "PERMIT_FORM": {"className": "com.unvired.permit.be.PERMIT_FORM", "field": [{"name": "PERMIT_NO", "description": "", "isGid": true, "length": "50", "mandatory": true, "sqlType": "TEXT"}, {"name": "FORM_GUID", "description": "", "isGid": true, "length": "36", "mandatory": true, "sqlType": "TEXT"}, {"name": "FORM_ID", "description": "", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "FORM_NAME", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "FORM_VERSION", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "INTEGER"}, {"name": "FORM_TITLE", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "DATA", "description": "", "isGid": false, "length": "2147483647", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHANGED_BY", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHANGED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "PARTIAL_FLAG", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMPLETED", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTERNAL_URL", "description": "", "isGid": false, "length": "1000", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "PERMIT_DOC": {"className": "com.unvired.permit.be.PERMIT_DOC", "field": [{"name": "PERMIT_NO", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "DOC_ID", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "DOC_TYPE", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "PERMIT_LOG": {"className": "com.unvired.permit.be.PERMIT_LOG", "field": [{"name": "PERMIT_NO", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "LOG_NO", "description": "", "isGid": true, "length": "10", "mandatory": true, "sqlType": "INTEGER"}, {"name": "ACTION", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "PERMIT_STATUS", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "APPR_TYPE", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "APPROVAL", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CREATED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMMENT", "description": "", "isGid": false, "length": "1000", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "PERMIT_QUERY_CTX": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "PERMIT_QUERY_CTX_HEADER": {"description": "Description", "className": "com.unvired.permit.be.PERMIT_QUERY_CTX_HEADER", "header": true, "field": [{"name": "PERMIT_NO", "description": "", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "PERMIT_TYPE", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "FACILITY_ID", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "DIVISION_ID", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "AGENT_ID", "description": "", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "JOB_NO", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "STATUS", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "REQUESTED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "REQUESTED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "SOURCE", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "PERMIT_DATE", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "EXPIRY_DATE", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "IS_EXTENDED", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_EXPIRED", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "USER_ID", "description": "", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTENSION_DATE", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "START_DATE", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "END_DATE", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "REC_OFFSET", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "REC_LIMIT", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "PERMIT_TYPE": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "PERMIT_TYPE_HEADER": {"description": "", "className": "com.unvired.permit.be.PERMIT_TYPE_HEADER", "header": true, "field": [{"name": "PERMIT_TYPE", "description": "", "isGid": true, "length": "10", "mandatory": true, "sqlType": "TEXT"}, {"name": "FORM_ID", "description": "", "isGid": false, "length": "36", "mandatory": false, "sqlType": "TEXT"}, {"name": "FORM_NAME", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "PREFIX", "description": "", "isGid": false, "length": "3", "mandatory": false, "sqlType": "TEXT"}, {"name": "COLOR", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "INTEGER"}, {"name": "ICON", "description": "", "isGid": false, "length": "65535", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "PERMIT_TYPE_APPROVAL": {"className": "com.unvired.permit.be.PERMIT_TYPE_APPROVAL", "field": [{"name": "PERMIT_TYPE", "description": "", "isGid": true, "length": "10", "mandatory": true, "sqlType": "TEXT"}, {"name": "APPR_TYPE", "description": "", "isGid": true, "length": "10", "mandatory": true, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "ROLE": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "ROLE_HEADER": {"description": "", "className": "com.unvired.permit.be.ROLE_HEADER", "header": true, "field": [{"name": "ROLE_NAME", "description": "", "isGid": true, "length": "20", "mandatory": true, "sqlType": "TEXT"}, {"name": "IS_INTERNAL", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONFIGURATION", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "USER_MGMT", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "AGENT_MGMT", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PROC_MGMT", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "FACILITY_MGMT", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "REQUEST", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "REVIEW", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "APPROVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "ISSUE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTEND", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXECUTE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CLOSE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CANCEL", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "REPORT", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "SKILL": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "SKILL_HEADER": {"description": "", "className": "com.unvired.permit.be.SKILL_HEADER", "header": true, "field": [{"name": "SKILL_TYPE", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "100", "mandatory": true, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "STRUCTURE": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "STRUCTURE_HEADER": {"description": "", "className": "com.unvired.permit.be.STRUCTURE_HEADER", "header": true, "field": [{"name": "FACILITY_ID", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "DIVISION_ID", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "TAG", "description": "", "isGid": true, "length": "100", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "description": "", "isGid": false, "length": "1000", "mandatory": false, "sqlType": "TEXT"}, {"name": "CATEGORY", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "STRUCT_TYPE", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "STATUS", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "LATITUDE", "description": "", "isGid": false, "length": "12", "mandatory": false, "sqlType": "REAL"}, {"name": "LONGITUDE", "description": "", "isGid": false, "length": "12", "mandatory": false, "sqlType": "REAL"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "STRUCTURE_DOC": {"className": "com.unvired.permit.be.STRUCTURE_DOC", "field": [{"name": "FACILITY_ID", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "DIVISION_ID", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "TAG", "description": "", "isGid": true, "length": "40", "mandatory": true, "sqlType": "TEXT"}, {"name": "DOC_ID", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "DOC_TYPE", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "THUMBNAIL", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "STRUCTURE_CAT": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "STRUCTURE_CAT_HEADER": {"description": "", "className": "com.unvired.permit.be.STRUCTURE_CAT_HEADER", "header": true, "field": [{"name": "CATEGORY", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "STRUCTURE_STATUS": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "STRUCTURE_STATUS_HEADER": {"description": "", "className": "com.unvired.permit.be.STRUCTURE_STATUS_HEADER", "header": true, "field": [{"name": "STATUS", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "IS_ACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "STRUCTURE_TYPE": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "STRUCTURE_TYPE_HEADER": {"description": "", "className": "com.unvired.permit.be.STRUCTURE_TYPE_HEADER", "header": true, "field": [{"name": "STRUCT_TYPE", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "USER": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "USER_HEADER": {"description": "", "className": "com.unvired.permit.be.USER_HEADER", "header": true, "field": [{"name": "USER_ID", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "FIRST_NAME", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "LAST_NAME", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "EMAIL", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "PHONE", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "ROLE_NAME", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "CERTIFICATE_NO", "description": "", "isGid": false, "length": "45", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_INTERNAL", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "USER_APPROVAL_TYPE": {"className": "com.unvired.permit.be.USER_APPROVAL_TYPE", "field": [{"name": "USER_ID", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "APPR_TYPE", "description": "", "isGid": true, "length": "10", "mandatory": true, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "USER_SKILL": {"className": "com.unvired.permit.be.USER_SKILL", "field": [{"name": "USER_ID", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "SKILL_TYPE", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "RATING", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "INTEGER"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "USER_DOC": {"className": "com.unvired.permit.be.USER_DOC", "field": [{"name": "USER_ID", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "DOC_ID", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "DOC_CTX", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "USER_CONTEXT": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "USER_CONTEXT_HEADER": {"className": "com.unvired.permit.be.USER_CONTEXT_HEADER", "header": true, "field": [{"name": "USER_ID", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "FIRST_NAME", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "LAST_NAME", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "EMAIL", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "PHONE", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "CURRENT_FACILITY", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "CURRENT_FACILITY_DESC", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "USER_ROLE": {"className": "com.unvired.permit.be.USER_ROLE", "field": [{"name": "USER_ID", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "ROLE_NAME", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_INTERNAL", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONFIGURATION", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "USER_MGMT", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "AGENT_MGMT", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PROC_MGMT", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "FACILITY_MGMT", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "REQUEST", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "REVIEW", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "APPROVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "ISSUE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTEND", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXECUTE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CLOSE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CANCEL", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "REPORT", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}, "USER_FACILITY": {"className": "com.unvired.permit.be.USER_FACILITY", "field": [{"name": "USER_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "FACILITY_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "USER_DIVISION": {"description": "", "className": "com.unvired.permit.be.USER_DIVISION", "field": [{"name": "USER_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "FACILITY_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "DIVISION_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "USER_AGENT": {"className": "com.unvired.permit.be.USER_AGENT", "field": [{"name": "USER_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "AGENT_ID", "description": "", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_INTERNAL", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONTACT", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "PHONE", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "EMAIL", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ADDRESS", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_ACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "Index": []}