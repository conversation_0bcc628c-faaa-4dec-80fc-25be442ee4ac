<Application description="Work Permit" name="PERMIT" namespace="UNVIRED" package="com.unvired.permit" version="1.0">
    <BusinessEntity attachments="false" description="" name="AGENT" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.AGENT_HEADER" description="" name="AGENT_HEADER">
            <Field description="" isGid="true" length="32" mandatory="true" name="AGENT_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="40" mandatory="false" name="NAME" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="IS_INTERNAL" sqlType="TEXT"/>
            <Field description="" isGid="false" length="40" mandatory="false" name="CONTACT" sqlType="TEXT"/>
            <Field description="" isGid="false" length="20" mandatory="false" name="PHONE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="50" mandatory="false" name="EMAIL" sqlType="TEXT"/>
            <Field description="" isGid="false" length="100" mandatory="false" name="ADDRESS" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="IS_ACTIVE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </header>
        <item className="com.unvired.permit.be.AGENT_FACILITY" description="" name="AGENT_FACILITY">
            <Field description="" isGid="true" length="32" mandatory="true" name="AGENT_ID" sqlType="TEXT"/>
            <Field description="" isGid="true" length="5" mandatory="true" name="FACILITY_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </item>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="" name="AGENT_USER" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.AGENT_USER_HEADER" description="" name="AGENT_USER_HEADER">
            <Field description="" isGid="true" length="32" mandatory="true" name="AGENT_ID" sqlType="TEXT"/>
            <Field description="" isGid="true" length="32" mandatory="true" name="USER_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="" name="APPROVAL_TYPE" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.APPROVAL_TYPE_HEADER" description="" name="APPROVAL_TYPE_HEADER">
            <Field description="" isGid="true" length="10" mandatory="true" name="APPR_TYPE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="SCOPE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="100" mandatory="false" name="DESCRIPTION" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="" name="CHART" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.CHART_HEADER" description="" name="CHART_HEADER">
            <Field description="" isGid="true" length="0" mandatory="true" name="CHART_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="CHART_TYPE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="TITLE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="HAS_SERIES" sqlType="TEXT"/>
        </header>
        <item className="com.unvired.permit.be.CHART_AXIS" description="" name="CHART_AXIS">
            <Field description="" isGid="true" length="0" mandatory="true" name="CHART_ID" sqlType="TEXT"/>
            <Field description="" isGid="true" length="0" mandatory="true" name="CATEGORY" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="true" name="VALUE" sqlType="TEXT"/>
        </item>
        <item className="com.unvired.permit.be.CHART_SERIES" description="" name="CHART_SERIES">
            <Field description="" isGid="true" length="0" mandatory="true" name="CHART_ID" sqlType="TEXT"/>
            <Field description="" isGid="true" length="0" mandatory="true" name="SERIES" sqlType="TEXT"/>
            <Field description="" isGid="true" length="0" mandatory="true" name="CATEGORY" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="VALUE" sqlType="TEXT"/>
        </item>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="" name="DIVISION" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.DIVISION_HEADER" description="" name="DIVISION_HEADER">
            <Field description="" isGid="true" length="5" mandatory="true" name="FACILITY_ID" sqlType="TEXT"/>
            <Field description="" isGid="true" length="5" mandatory="true" name="DIVISION_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="40" mandatory="false" name="NAME" sqlType="TEXT"/>
            <Field description="" isGid="false" length="9" mandatory="false" name="LATITUDE" sqlType="REAL"/>
            <Field description="" isGid="false" length="9" mandatory="false" name="LONGITUDE" sqlType="REAL"/>
            <Field description="" isGid="false" length="65535" mandatory="false" name="BOUNDARY" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="true" description="" name="DOCUMENT" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.DOCUMENT_HEADER" description="" name="DOCUMENT_HEADER">
            <Field description="" isGid="true" length="32" mandatory="true" name="DOC_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="19" mandatory="false" name="FOLDER_ID" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="100" mandatory="false" name="TITLE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1024" mandatory="false" name="FILE_NAME" sqlType="TEXT"/>
            <Field description="" isGid="false" length="40" mandatory="false" name="MIME_TYPE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="10" mandatory="false" name="DOC_TYPE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="THUMBNAIL" sqlType="TEXT"/>
            <Field description="" isGid="false" length="50" mandatory="false" name="CREATED_BY" sqlType="TEXT"/>
            <Field description="" isGid="false" length="19" mandatory="false" name="CREATED_ON" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="50" mandatory="false" name="CHANGED_BY" sqlType="TEXT"/>
            <Field description="" isGid="false" length="19" mandatory="false" name="CHANGED_ON" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="INACTIVE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </header>
        <item className="com.unvired.permit.be.DOCUMENT_ATTACHMENT" description="Attachment" name="DOCUMENT_ATTACHMENT">
            <Field description="UID" isGid="true" length="32" mandatory="false" name="UID" sqlType="TEXT"/>
            <Field description="File Name" isGid="false" length="255" mandatory="false" name="FILE_NAME" sqlType="TEXT"/>
            <Field description="Mime Type" isGid="false" length="20" mandatory="false" name="MIME_TYPE" sqlType="TEXT"/>
            <Field description="Download URL" isGid="false" length="255" mandatory="false" name="URL" sqlType="TEXT"/>
            <Field description="External or Internal URL" isGid="false" length="1" mandatory="false" name="EXTERNAL_URL" sqlType="TEXT"/>
            <Field description="External URL Requires Authentication" isGid="false" length="1" mandatory="false" name="URL_REQUIRES_AUTH" sqlType="TEXT"/>
            <Field description="Path to the file on the device" isGid="false" length="255" mandatory="false" name="LOCAL_PATH" sqlType="TEXT"/>
            <Field description="Do not cache" isGid="false" length="1" mandatory="false" name="NO_CACHE" sqlType="TEXT"/>
            <Field description="Server timestamp" isGid="false" length="20" mandatory="false" name="SERVER_TIMESTAMP" sqlType="INTEGER"/>
            <Field description="Tag 1" isGid="false" length="100" mandatory="false" name="TAG1" sqlType="TEXT"/>
            <Field description="Tag 2" isGid="false" length="100" mandatory="false" name="TAG2" sqlType="TEXT"/>
            <Field description="Tag 3" isGid="false" length="100" mandatory="false" name="TAG3" sqlType="TEXT"/>
            <Field description="Tag 4" isGid="false" length="100" mandatory="false" name="TAG4" sqlType="TEXT"/>
            <Field description="Tag 5" isGid="false" length="100" mandatory="false" name="TAG5" sqlType="TEXT"/>
            <Field description="Status" isGid="false" length="32" mandatory="false" name="ATTACHMENT_STATUS" sqlType="TEXT"/>
            <Field description="Auto Download Flag" isGid="false" length="1" mandatory="false" name="AUTO_DOWNLOAD" sqlType="TEXT"/>
            <Field description="Name of the param" isGid="false" length="100" mandatory="false" name="PARAM" sqlType="TEXT"/>
            <Field description="Message from User" isGid="false" length="100" mandatory="false" name="MESSAGE" sqlType="TEXT"/>
        </item>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="" name="FACILITY" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.FACILITY_HEADER" description="" name="FACILITY_HEADER">
            <Field description="" isGid="true" length="5" mandatory="true" name="FACILITY_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="40" mandatory="false" name="NAME" sqlType="TEXT"/>
            <Field description="" isGid="false" length="9" mandatory="false" name="LATITUDE" sqlType="REAL"/>
            <Field description="" isGid="false" length="9" mandatory="false" name="LONGITUDE" sqlType="REAL"/>
            <Field description="" isGid="false" length="65535" mandatory="false" name="BOUNDARY" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="" name="FORM" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.FORM_HEADER" description="" name="FORM_HEADER">
            <Field description="" isGid="true" length="0" mandatory="true" name="FORM_ID" sqlType="TEXT"/>
            <Field description="" isGid="true" length="0" mandatory="true" name="FORM_VERSION" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="0" mandatory="true" name="FORM_NAME" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="FORM_TITLE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="FORM_DESC" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="TEMPLATE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="CATEGORY" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="AVATAR" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="ATTRIBUTES" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="FORM_TYPE" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="" name="INPUT_GET_FORM_TEMPLATE" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.INPUT_GET_FORM_TEMPLATE_HEADER" description="" name="INPUT_GET_FORM_TEMPLATE_HEADER">
            <Field description="" isGid="false" length="0" mandatory="false" name="CATEGORY" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="FORM_NAME" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="FORM_VERSION" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="WITH_DATA" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="" name="PERMIT" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.PERMIT_HEADER" description="" name="PERMIT_HEADER">
            <Field description="" isGid="true" length="32" mandatory="true" name="PERMIT_NO" sqlType="TEXT"/>
            <Field description="" isGid="false" length="10" mandatory="false" name="PERMIT_TYPE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="40" mandatory="false" name="DESCRIPTION" sqlType="TEXT"/>
            <Field description="" isGid="false" length="5" mandatory="false" name="FACILITY_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="5" mandatory="false" name="DIVISION_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="40" mandatory="false" name="TAG" sqlType="TEXT"/>
            <Field description="" isGid="false" length="32" mandatory="false" name="AGENT_ID_INT" sqlType="TEXT"/>
            <Field description="" isGid="false" length="32" mandatory="false" name="AGENT_ID_EXT" sqlType="TEXT"/>
            <Field description="" isGid="false" length="20" mandatory="false" name="JOB_NO" sqlType="TEXT"/>
            <Field description="" isGid="false" length="20" mandatory="false" name="STATUS" sqlType="TEXT"/>
            <Field description="" isGid="false" length="50" mandatory="false" name="REQUESTED_BY" sqlType="TEXT"/>
            <Field description="" isGid="false" length="19" mandatory="false" name="REQUESTED_ON" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="10" mandatory="false" name="SOURCE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="19" mandatory="false" name="PERMIT_DATE" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="19" mandatory="false" name="EXPIRY_DATE" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="IS_EXTENDED" sqlType="TEXT"/>
            <Field description="" isGid="false" length="19" mandatory="false" name="EXTENSION_DATE" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="1000" mandatory="false" name="COMMENTS" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="APPROVAL_SUMMARY" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </header>
        <item className="com.unvired.permit.be.PERMIT_STAKEHOLDER" description="" name="PERMIT_STAKEHOLDER">
            <Field description="" isGid="true" length="32" mandatory="true" name="PERMIT_NO" sqlType="TEXT"/>
            <Field description="" isGid="true" length="32" mandatory="true" name="ROW_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="19" mandatory="false" name="SEQ_NO" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="32" mandatory="false" name="ROLE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="10" mandatory="false" name="APPR_TYPE" sqlType="TEXT"/>
            <Field description="O-Open A-Approved R-Rejected" isGid="false" length="1" mandatory="false" name="APPROVAL" sqlType="TEXT"/>
            <Field description="" isGid="false" length="19" mandatory="false" name="PROCESSED_ON" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="50" mandatory="false" name="CONTEXT" sqlType="TEXT"/>
            <Field description="" isGid="false" length="32" mandatory="false" name="AGENT_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="50" mandatory="false" name="USER_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1000" mandatory="false" name="COMMENT" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="IS_ACTIVE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </item>
        <item className="com.unvired.permit.be.PERMIT_FORM" description="" name="PERMIT_FORM">
            <Field description="" isGid="true" length="50" mandatory="true" name="PERMIT_NO" sqlType="TEXT"/>
            <Field description="" isGid="true" length="36" mandatory="true" name="FORM_GUID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="32" mandatory="false" name="FORM_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="100" mandatory="false" name="FORM_NAME" sqlType="TEXT"/>
            <Field description="" isGid="false" length="10" mandatory="false" name="FORM_VERSION" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="50" mandatory="false" name="FORM_TITLE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="2147483647" mandatory="false" name="DATA" sqlType="TEXT"/>
            <Field description="" isGid="false" length="100" mandatory="false" name="CHANGED_BY" sqlType="TEXT"/>
            <Field description="" isGid="false" length="19" mandatory="false" name="CHANGED_ON" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="PARTIAL_FLAG" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="COMPLETED" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1000" mandatory="false" name="EXTERNAL_URL" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </item>
        <item className="com.unvired.permit.be.PERMIT_DOC" description="" name="PERMIT_DOC">
            <Field description="" isGid="true" length="32" mandatory="true" name="PERMIT_NO" sqlType="TEXT"/>
            <Field description="" isGid="true" length="32" mandatory="true" name="DOC_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="5" mandatory="false" name="DOC_TYPE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </item>
        <item className="com.unvired.permit.be.PERMIT_LOG" description="" name="PERMIT_LOG">
            <Field description="" isGid="true" length="32" mandatory="true" name="PERMIT_NO" sqlType="TEXT"/>
            <Field description="" isGid="true" length="10" mandatory="true" name="LOG_NO" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="10" mandatory="false" name="ACTION" sqlType="TEXT"/>
            <Field description="" isGid="false" length="20" mandatory="false" name="PERMIT_STATUS" sqlType="TEXT"/>
            <Field description="" isGid="false" length="10" mandatory="false" name="APPR_TYPE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="APPROVAL" sqlType="TEXT"/>
            <Field description="" isGid="false" length="19" mandatory="false" name="CREATED_ON" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="50" mandatory="false" name="CREATED_BY" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1000" mandatory="false" name="COMMENT" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </item>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="" name="PERMIT_QUERY_CTX" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.PERMIT_QUERY_CTX_HEADER" description="" name="PERMIT_QUERY_CTX_HEADER">
            <Field description="" isGid="false" length="32" mandatory="false" name="PERMIT_NO" sqlType="TEXT"/>
            <Field description="" isGid="false" length="10" mandatory="false" name="PERMIT_TYPE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="40" mandatory="false" name="DESCRIPTION" sqlType="TEXT"/>
            <Field description="" isGid="false" length="5" mandatory="false" name="FACILITY_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="40" mandatory="false" name="DIVISION_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="40" mandatory="false" name="TAG" sqlType="TEXT"/>
            <Field description="" isGid="false" length="32" mandatory="false" name="AGENT_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="20" mandatory="false" name="JOB_NO" sqlType="TEXT"/>
            <Field description="" isGid="false" length="20" mandatory="false" name="STATUS" sqlType="TEXT"/>
            <Field description="" isGid="false" length="50" mandatory="false" name="REQUESTED_BY" sqlType="TEXT"/>
            <Field description="" isGid="false" length="19" mandatory="false" name="REQUESTED_ON" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="10" mandatory="false" name="SOURCE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="19" mandatory="false" name="PERMIT_DATE" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="19" mandatory="false" name="EXPIRY_DATE" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="IS_EXTENDED" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="IS_EXPIRED" sqlType="TEXT"/>
            <Field description="" isGid="false" length="32" mandatory="false" name="USER_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="19" mandatory="false" name="EXTENSION_DATE" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="19" mandatory="false" name="START_DATE" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="19" mandatory="false" name="END_DATE" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="REC_OFFSET" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="REC_LIMIT" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="" name="PERMIT_TYPE" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.PERMIT_TYPE_HEADER" description="" name="PERMIT_TYPE_HEADER">
            <Field description="" isGid="true" length="10" mandatory="true" name="PERMIT_TYPE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="36" mandatory="false" name="FORM_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="100" mandatory="false" name="FORM_NAME" sqlType="TEXT"/>
            <Field description="" isGid="false" length="40" mandatory="false" name="DESCRIPTION" sqlType="TEXT"/>
            <Field description="" isGid="false" length="3" mandatory="false" name="PREFIX" sqlType="TEXT"/>
            <Field description="" isGid="false" length="10" mandatory="false" name="COLOR" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="65535" mandatory="false" name="ICON" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </header>
        <item className="com.unvired.permit.be.PERMIT_TYPE_APPROVAL" description="" name="PERMIT_TYPE_APPROVAL">
            <Field description="" isGid="true" length="10" mandatory="true" name="PERMIT_TYPE" sqlType="TEXT"/>
            <Field description="" isGid="true" length="10" mandatory="true" name="APPR_TYPE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </item>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="" name="ROLE" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.ROLE_HEADER" description="" name="ROLE_HEADER">
            <Field description="" isGid="true" length="20" mandatory="true" name="ROLE_NAME" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="IS_INTERNAL" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="CONFIGURATION" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="USER_MGMT" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="AGENT_MGMT" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="PROC_MGMT" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="FACILITY_MGMT" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="REQUEST" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="REVIEW" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="APPROVE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="ISSUE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="EXTEND" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="EXECUTE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="CLOSE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="CANCEL" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="REPORT" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="" name="SKILL" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.SKILL_HEADER" description="" name="SKILL_HEADER">
            <Field description="" isGid="true" length="5" mandatory="true" name="SKILL_TYPE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="100" mandatory="false" name="DESCRIPTION" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="" name="STRUCTURE" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.STRUCTURE_HEADER" description="" name="STRUCTURE_HEADER">
            <Field description="" isGid="true" length="5" mandatory="true" name="FACILITY_ID" sqlType="TEXT"/>
            <Field description="" isGid="true" length="5" mandatory="true" name="DIVISION_ID" sqlType="TEXT"/>
            <Field description="" isGid="true" length="100" mandatory="true" name="TAG" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1000" mandatory="false" name="NAME" sqlType="TEXT"/>
            <Field description="" isGid="false" length="5" mandatory="false" name="CATEGORY" sqlType="TEXT"/>
            <Field description="" isGid="false" length="5" mandatory="false" name="STRUCT_TYPE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="5" mandatory="false" name="STATUS" sqlType="TEXT"/>
            <Field description="" isGid="false" length="12" mandatory="false" name="LATITUDE" sqlType="REAL"/>
            <Field description="" isGid="false" length="12" mandatory="false" name="LONGITUDE" sqlType="REAL"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </header>
        <item className="com.unvired.permit.be.STRUCTURE_DOC" description="" name="STRUCTURE_DOC">
            <Field description="" isGid="true" length="5" mandatory="true" name="FACILITY_ID" sqlType="TEXT"/>
            <Field description="" isGid="true" length="5" mandatory="true" name="DIVISION_ID" sqlType="TEXT"/>
            <Field description="" isGid="true" length="40" mandatory="true" name="TAG" sqlType="TEXT"/>
            <Field description="" isGid="true" length="32" mandatory="true" name="DOC_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="5" mandatory="false" name="DOC_TYPE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="THUMBNAIL" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </item>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="" name="STRUCTURE_CAT" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.STRUCTURE_CAT_HEADER" description="" name="STRUCTURE_CAT_HEADER">
            <Field description="" isGid="true" length="5" mandatory="true" name="CATEGORY" sqlType="TEXT"/>
            <Field description="" isGid="false" length="40" mandatory="false" name="DESCRIPTION" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="" name="STRUCTURE_STATUS" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.STRUCTURE_STATUS_HEADER" description="" name="STRUCTURE_STATUS_HEADER">
            <Field description="" isGid="true" length="5" mandatory="true" name="STATUS" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="IS_ACTIVE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="100" mandatory="false" name="DESCRIPTION" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="" name="STRUCTURE_TYPE" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.STRUCTURE_TYPE_HEADER" description="" name="STRUCTURE_TYPE_HEADER">
            <Field description="" isGid="true" length="5" mandatory="true" name="STRUCT_TYPE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="40" mandatory="false" name="DESCRIPTION" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="" name="USER" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.USER_HEADER" description="" name="USER_HEADER">
            <Field description="" isGid="true" length="32" mandatory="true" name="USER_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="40" mandatory="false" name="FIRST_NAME" sqlType="TEXT"/>
            <Field description="" isGid="false" length="40" mandatory="false" name="LAST_NAME" sqlType="TEXT"/>
            <Field description="" isGid="false" length="50" mandatory="false" name="EMAIL" sqlType="TEXT"/>
            <Field description="" isGid="false" length="20" mandatory="false" name="PHONE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="20" mandatory="false" name="ROLE_NAME" sqlType="TEXT"/>
            <Field description="" isGid="false" length="45" mandatory="false" name="CERTIFICATE_NO" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="IS_INTERNAL" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </header>
        <item className="com.unvired.permit.be.USER_APPROVAL_TYPE" description="" name="USER_APPROVAL_TYPE">
            <Field description="" isGid="true" length="32" mandatory="true" name="USER_ID" sqlType="TEXT"/>
            <Field description="" isGid="true" length="10" mandatory="true" name="APPR_TYPE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </item>
        <item className="com.unvired.permit.be.USER_SKILL" description="" name="USER_SKILL">
            <Field description="" isGid="true" length="32" mandatory="true" name="USER_ID" sqlType="TEXT"/>
            <Field description="" isGid="true" length="5" mandatory="true" name="SKILL_TYPE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="10" mandatory="false" name="RATING" sqlType="INTEGER"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </item>
        <item className="com.unvired.permit.be.USER_DOC" description="" name="USER_DOC">
            <Field description="" isGid="true" length="32" mandatory="true" name="USER_ID" sqlType="TEXT"/>
            <Field description="" isGid="true" length="32" mandatory="true" name="DOC_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="200" mandatory="false" name="DOC_CTX" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="P_MODE" sqlType="TEXT"/>
        </item>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="" name="USER_CONTEXT" onConflict="SERVER_WINS" save="true">
        <header className="com.unvired.permit.be.USER_CONTEXT_HEADER" description="" name="USER_CONTEXT_HEADER">
            <Field description="" isGid="true" length="32" mandatory="true" name="USER_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="40" mandatory="false" name="FIRST_NAME" sqlType="TEXT"/>
            <Field description="" isGid="false" length="40" mandatory="false" name="LAST_NAME" sqlType="TEXT"/>
            <Field description="" isGid="false" length="50" mandatory="false" name="EMAIL" sqlType="TEXT"/>
            <Field description="" isGid="false" length="20" mandatory="false" name="PHONE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="20" mandatory="false" name="CURRENT_FACILITY" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="CURRENT_FACILITY_DESC" sqlType="TEXT"/>
        </header>
        <item className="com.unvired.permit.be.USER_ROLE" description="" name="USER_ROLE">
            <Field description="" isGid="true" length="32" mandatory="true" name="USER_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="20" mandatory="false" name="ROLE_NAME" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="IS_INTERNAL" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="CONFIGURATION" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="USER_MGMT" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="AGENT_MGMT" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="PROC_MGMT" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="FACILITY_MGMT" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="REQUEST" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="REVIEW" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="APPROVE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="ISSUE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="EXTEND" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="EXECUTE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="CLOSE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="CANCEL" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="REPORT" sqlType="TEXT"/>
        </item>
        <item className="com.unvired.permit.be.USER_FACILITY" description="" name="USER_FACILITY">
            <Field description="" isGid="true" length="0" mandatory="true" name="USER_ID" sqlType="TEXT"/>
            <Field description="" isGid="true" length="0" mandatory="true" name="FACILITY_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="NAME" sqlType="TEXT"/>
        </item>
        <item className="com.unvired.permit.be.USER_DIVISION" description="" name="USER_DIVISION">
            <Field description="" isGid="true" length="0" mandatory="true" name="USER_ID" sqlType="TEXT"/>
            <Field description="" isGid="true" length="0" mandatory="true" name="FACILITY_ID" sqlType="TEXT"/>
            <Field description="" isGid="true" length="0" mandatory="true" name="DIVISION_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="NAME" sqlType="TEXT"/>
        </item>
        <item className="com.unvired.permit.be.USER_AGENT" description="" name="USER_AGENT">
            <Field description="" isGid="true" length="0" mandatory="true" name="USER_ID" sqlType="TEXT"/>
            <Field description="" isGid="true" length="32" mandatory="true" name="AGENT_ID" sqlType="TEXT"/>
            <Field description="" isGid="false" length="40" mandatory="false" name="NAME" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="IS_INTERNAL" sqlType="TEXT"/>
            <Field description="" isGid="false" length="40" mandatory="false" name="CONTACT" sqlType="TEXT"/>
            <Field description="" isGid="false" length="20" mandatory="false" name="PHONE" sqlType="TEXT"/>
            <Field description="" isGid="false" length="50" mandatory="false" name="EMAIL" sqlType="TEXT"/>
            <Field description="" isGid="false" length="100" mandatory="false" name="ADDRESS" sqlType="TEXT"/>
            <Field description="" isGid="false" length="1" mandatory="false" name="IS_ACTIVE" sqlType="TEXT"/>
        </item>
    </BusinessEntity>
</Application>
