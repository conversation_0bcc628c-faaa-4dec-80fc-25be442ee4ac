// Ionic Variables and Theming. For more info, please see:
// http://ionicframework.com/docs/theming/

/** Ionic CSS Variables **/
:root {
  --ion-color-primary: #3445a7;
  --ion-color-primary-rgb: 52, 69, 167;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #2e3c93;
  --ion-color-primary-tint: #4858b0;

  --ion-color-secondary: #a855f7;
  --ion-color-secondary-rgb: 168, 85, 247;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #944bd9;
  --ion-color-secondary-tint: #b166f8;

  --ion-color-tertiary: #7044ff;
  --ion-color-tertiary-rgb: 112, 68, 255;
  --ion-color-tertiary-contrast: #ffffff;
  --ion-color-tertiary-contrast-rgb: 255, 255, 255;
  --ion-color-tertiary-shade: #633ce0;
  --ion-color-tertiary-tint: #7e57ff;

  --ion-color-success: #10dc60;
  --ion-color-success-rgb: 16, 220, 96;
  --ion-color-success-contrast: #ffffff;
  --ion-color-success-contrast-rgb: 255, 255, 255;
  --ion-color-success-shade: #0ec254;
  --ion-color-success-tint: #28e070;
  --ion-color-success-dark: #065825;

  --ion-color-warning: #ffce00;
  --ion-color-warning-rgb: 255, 206, 0;
  --ion-color-warning-contrast: #ffffff;
  --ion-color-warning-contrast-rgb: 255, 255, 255;
  --ion-color-warning-shade: #e0b500;
  --ion-color-warning-tint: #ffd31a;

  --ion-color-danger: #f04141;
  --ion-color-danger-rgb: 245, 61, 61;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #d33939;
  --ion-color-danger-tint: #f25454;

  --ion-color-dark: #222428;
  --ion-color-dark-rgb: 34, 34, 34;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb: 255, 255, 255;
  --ion-color-dark-shade: #1e2023;
  --ion-color-dark-tint: #383a3e;

  --ion-color-medium: #989aa2;
  --ion-color-medium-rgb: 152, 154, 162;
  --ion-color-medium-contrast: #ffffff;
  --ion-color-medium-contrast-rgb: 255, 255, 255;
  --ion-color-medium-shade: #86888f;
  --ion-color-medium-tint: #a2a4ab;

  --ion-color-light: #f4f5f8;
  --ion-color-light-rgb: 244, 244, 244;
  --ion-color-light-contrast: #000000;
  --ion-color-light-contrast-rgb: 0, 0, 0;
  --ion-color-light-shade: #d7d8da;
  --ion-color-light-tint: #f5f6f9;

  // custom variables for theme
  --ion-color-dark-mode: #23252a;
  --ion-heading-color: #2586c7;
  --ion-no-task-bg-color: #f5f5f5;
  --ion-forms-category-bg-color: #ebebeb;
  --ion-forms-category-color: #050505;
  --ion-searchbar-bg-color: #ffffff;
  --ion-placeholder-color: #4552a9;
  --ion-segment-bg: none;
  --ion-segment-color: rgba(var(--ion-text-color-rgb,0,0,0),0.6);
  --ion-expand-btn-hover-bg: #e7e8ea;
  --ion-input-bg-color: #fff;
  --ion-input-color: #4d545a;
  --ion-input-border-color: #ced4da;
  --ion-form-desc-txt-color: #7da21f;
  --ion-sort-list-header-bg: #efefef;
  --ion-sort-list-header-color: #010101;
  --ion-footer-bg-color: #ffff;
  --ion-button-grid-color: #e4e4e4;
  --ion-text-color: #2a2a2a;
  --ion-color-header-base-color: #fff;
  --ion-serach-text-color: #000000;
  --ion-checkbox-check: #2586c7;
  --ion-alert-bg: #f9f9f9;
  --ion-data-selected-bg: #dedede;
  --ion-data-selected-bg-hover: #dedede; 
  --ion-segment-checked-color: #2586c7;
  --ion-skel-text-bg: #e0e0e0;
  --ion-background-color: #ffff;
  --ion-settings-text: rgba(var(--ion-text-color-rgb,0,0,0),.4);
  --ion-cancel-color-ios: #ffff;
  --ion-segment-checked-color-ios: #ffff;
  --ion-button-assign-to-me: #383a3e;
  --ion-search-icons-color: #2a378c;
  --ion-form-duedate-color: #0d0b8d;
  --ion-input-comment-color: #818181;
  --ion-allow-read-mode-color: #2586c7;
  --ion-conflict-screen-header: #696b79;
  --ion-pdf-button-bg-hover: #e7e8ea;

  --ion-custom-border-color: #ececec;
  --ion-custom-badge-color: #2586c7;
  --ion-custom-checkbox-border: #c5c5c5;

  --ion-custom-timeline-vertical-line: #e3e3e3;
  --ion-custom-timeline-panel-border: #d4d4d4;
  --ion-custom-timeline-panel-background: #f4f5f8;
  --ion-custom-timeline-panel-box-shadow: #0000002d;
  --ion-custom-timeline-panel-time-text-color: #7d7d7d;
  --ion-custom-timeline-panel-pseudo-before-border: #cccccc;
  --ion-custom-timeline-panel-pseudo-after-border: #cccccc;
  --ion-custom-timeline-panel-pseudo-border-bottom: #00000000;    // transparent color code
  --ion-custom-timeline-panel-pseudo-border-top: #00000000;       // transparent color code
  --ion-custom-user-status-table-header-background: #bbecff;
  --ion-custom-user-status-table-border: #bbecff;

  --ion-custom-alert-updated-at-text-color: #c2c2c2;
}

html, body, ion-app, .camera-preview-ion-content{
  background-color: transparent !important;
  --background: transparent !important;
  background: transparent !important;
}
