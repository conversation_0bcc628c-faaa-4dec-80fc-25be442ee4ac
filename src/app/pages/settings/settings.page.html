<ion-header [translucent]="true">
  <ion-toolbar class="main-toolbar">
    <ion-buttons slot="start">
      <ion-back-button class="header-btn"></ion-back-button>
    </ion-buttons>
    <ion-title class="main-title" (click)="increaseCounter()">Settings</ion-title>
  </ion-toolbar>
</ion-header>

<div class="mobile-content-wrapper">
  <ion-card>
    <ion-progress-bar [hidden]="!processbar" type="indeterminate"></ion-progress-bar>
    <ion-item lines="none" [ngStyle]="{'border-left': connection ? '4px solid #008000' : '4px solid indianred'}">
      <ion-buttons slot="start" (click)="connect()">
        <ion-button>
          <i class="fal fa-bolt" [ngStyle]="{'color': connection ? '#008000' : 'indianred'}"
            slot="icon-only" style="font-size: 23px;"></i>
        </ion-button>
      </ion-buttons>
      <ion-label>
        <h4>&nbsp;{{userDetails.username}}</h4>
        <p>&nbsp;{{userDetails.userid}}</p>
        <p>&nbsp;{{userDetails.email}}</p>
        <p>&nbsp;{{userDetails.url}}</p>
      </ion-label>
    </ion-item>
  </ion-card>

  <ion-card *ngIf="counter===3" (click)="exportDb()" class="indicateClickable">
    <ion-item lines="none">
      <ion-buttons slot="start">
        <ion-button>
          <i slot="icon-only" class="fal fa-database iconWithinButton"></i>
        </ion-button>
      </ion-buttons>
      <ion-label>
        <h4 style="padding-bottom:8px">{{"Export Database" | translate}}</h4>
      </ion-label>
    </ion-item>
  </ion-card>

  <ion-card *ngIf="devicePlatform!=='browser'">
    <ion-item lines="none">
      <ion-buttons slot="start" [hidden]="syncState" (click)="sync()">
        <ion-button>
          <i style="color:rgba(33, 98, 158, 0.863);font-size: 23px;" class="fal fa-sync" slot="icon-only"></i>
        </ion-button>
      </ion-buttons>
      <ion-buttons slot="start" [hidden]="!syncState" (click)="sync()">
        <ion-button>
          <i style="color:rgba(33, 98, 158, 0.863);font-size: 23px;" class="fal fa-sync fa-spin" slot="icon-only"></i>
        </ion-button>
      </ion-buttons>
      <ion-label>
        <h4 style="padding-bottom:8px">{{ "Sync Status" | translate }}</h4>
        <p>{{syncStatus}}</p>
      </ion-label>
      <ion-buttons>
        <ion-button (click)="dataSender()" *ngIf="dataSenderFlag">
          <i class="fal fa-cloud-upload" style="color:rgba(30, 77, 163, 0.966);font-size: 23px;"></i>
        </ion-button>
        <ion-button (click)="getMessage()" *ngIf="getMessageFlag">
          <i class="fal fa-cloud-download" style="color:rgba(30, 77, 163, 0.966);font-size: 23px;"></i>
        </ion-button>
      </ion-buttons>
      <img *ngIf="loadingGifFlag" style="height:43px;width:43px;" src="assets/load.gif">
    </ion-item>
  </ion-card>

  <ion-card>
    <ion-item lines="none">
      <ion-buttons slot="start" (click)="info()">
        <ion-button [ngClass]="connection ? 'connectionsuccess' : 'connectionerror'">
          <i class="fal fa-exclamation-triangle" slot="icon-only" style="color:rgb(253, 126, 21);font-size: 23px;"></i>
        </ion-button>
      </ion-buttons>
      <ion-label>
        <h4 style="padding-bottom:8px">{{ "Errors, Warnings and Info" | translate }}</h4>
        <p>{{ "Errors: " | translate }}{{infoErrorCount}} &nbsp;&nbsp;{{ "Others: " | translate }}{{infoOtherCount}}
        </p>
      </ion-label>
    </ion-item>
  </ion-card>

  <ion-card>
    <ion-item lines="none">
      <ion-buttons slot="start" (click)="logs()">
        <ion-button>
          <i class="fal fa-file-alt" style="color:rgb(133, 135, 138);font-size: 23px;" slot="icon-only"></i>
        </ion-button>
      </ion-buttons>
      <ion-label>
        <h4 style="padding-bottom:8px">&nbsp;{{ "Logs" | translate }}</h4>
        <p>&nbsp;{{ "Level" | translate }} &nbsp;&nbsp;
          <select [(ngModel)]="loglevel" (change)="setLogLevel($event)"
            style="border:1px solid gray;width:90px;height:24px;border-radius: 7px;padding-left:3px;padding-top:1px;padding-bottom:3px;outline-color: rgba(0, 0, 0, 0);">
            <option *ngFor="let level of logLevel" [value]="level.value">
              {{level.viewValue}}
            </option>
          </select>
        </p>
      </ion-label>
      <ion-buttons>
        <ion-button (click)="upload()">
          <i class="fal fa-cloud-upload" style="color:rgba(30, 77, 163, 0.966);font-size: 23px;"></i>
        </ion-button>
        <ion-button (click)="email()">
          <i class="fal fa-envelope" style="color:rgba(224, 131, 9, 0.952);font-size: 23px;"></i>
        </ion-button>
      </ion-buttons>

    </ion-item>
  </ion-card>

  <ion-card *ngIf="devicePlatform!=='browser'">
    <ion-item lines="none">
      <ion-buttons slot="start" (click)="pushNotification()">
        <ion-button>
          <i class="fal fa-bell" slot="icon-only" style="color:rgba(33, 98, 158, 0.863);font-size: 23px;"></i>
        </ion-button>
      </ion-buttons>
      <ion-label>
        <h4 style="padding-bottom:8px">{{ "Push Notification" | translate }}</h4>
        <p>{{ "Tap on the icon to test notification" | translate }}</p>
      </ion-label>
    </ion-item>
  </ion-card>

  <ion-card>
    <ion-item lines="none">
      <ion-buttons slot="start" (click)="trash()">
        <ion-button>
          <i class="fal fa-trash-alt" slot="icon-only" style="color:rgba(179, 43, 43, 0.863);font-size: 23px;"></i>
        </ion-button>
      </ion-buttons>
      <ion-label>
        <h4 style="padding-bottom:8px">{{ "Advanced" | translate }}</h4>
        <p>{{ "Tap on the icon to reset the app" | translate }}</p>
      </ion-label>
    </ion-item>
  </ion-card>

  <ion-card>
    <ion-item lines="none">
      <ion-buttons slot="start">
        <ion-button>
          <i class="fal fa-info-circle" slot="icon-only" style="color:rgba(30, 77, 163, 0.966);font-size: 23px;"></i>
        </ion-button>
      </ion-buttons>
      <ion-label>
        <h4 style="padding-bottom:8px">{{ "Version" | translate }}</h4>
        <p>{{version }} - {{release}} </p>
      </ion-label>
    </ion-item>
  </ion-card>
</div>