// Header Styles - Match mobile home page
.main-toolbar {
    --background: #ffffff;
    --color: #333333;
    --border-width: 0 0 1px 0;
    --border-color: #e0e0e0;
    --min-height: 56px;
}

.main-title {
    font-size: 20px;
    font-weight: 600;
    color: #333333;
}

.header-btn {
    --color: #666666;
    --background: transparent;
    --border-radius: 50%;
    --padding-start: 8px;
    --padding-end: 8px;

    &:hover {
        --background: #f0f0f0;
    }
}

ion-toolbar {
  --color: white;
}

.dbExport {
  margin-top: 2%;
  cursor: pointer;
  // display: flex;
  // align-items: center;
  // justify-content: center;
  // height: 100%;
}

.iconWithinButton {
  font-size: x-large;
}

// Mobile Content Wrapper - Enable scrolling
.mobile-content-wrapper {
    height: calc(100vh - 56px); // Full height minus header
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    background: #f4f4f4;
    position: relative;
    padding: 16px;
}

.indicateClickable {
  cursor: pointer;
}

// Fix content alignment for mobile settings page - minimal overrides
:host {
    // Override global empty-data-info styles that cause bottom alignment
    .empty-data-info {
        padding-top: 40px !important; // Override global 25vh
        position: static !important;
        transform: none !important;
    }
}
