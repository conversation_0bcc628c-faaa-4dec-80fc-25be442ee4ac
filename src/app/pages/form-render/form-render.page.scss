.test {
    .toast-message {
      text-align: center;
    }
  }
  
  ion-header {
    background: var(--ion-color-primary);
  }
  
  .header-grid {
    --ion-grid-padding: 0 !important;
  
    .header-column {
      --ion-grid-column-padding: 0 !important;
      color: var(--ion-color-light);
  
      .header-column-div {
        text-align: center !important;
        padding-bottom: 5px !important;
        font-weight: bold;
        color: #fff;
      }
    }
  }
  
  ion-checkbox {
    --checkmark-color: var(--ion-checkbox-check);
    --background-checked: var(--ion-color-light);
    --border-color-checked: var(--ion-color-light);
  }
  
  .header-text {
    color: #fff;
    --color: white !important;
    --ion-color-base-rgb: var(--ion-color-header-base-color, #fff) !important;
  }
  
  .header-text-style {
    font-weight: bold; 
    border: 1px solid white; 
    border-bottom-left-radius: 6px; 
    border-top-left-radius: 6px; 
    margin: 0;  
  }
  
  .form-control {
    background-color: var(--ion-input-bg-color) !important;
    color: var(--ion-input-color) !important;
    border: 1px solid var(--ion-input-border-color) !important;
  }
  
  .header-toolbar {
    --background: var(--ion-color-primary);
    --color: var(--ion-color-primary-contrast);
  
    ion-icon {
      color: var(--ion-color-primary-contrast);
    }
  }
  
  .alertCount {
    --color: #303f9f;
    --background: #ffffff;
    transform: translate(5px, 0px);
  }
  
  .notMobileViewAlertCount {
    --color: #303f9f;
    --background: #ffffff;
    transform: translate(-20px, -10px);
  }
  
  .mobileViewAlertCount {
    --color: #303f9f;
    --background: #ffffff;
    zoom: 0.8;
    transform: translate(-8px, 0px);
  }
  
  ion-content {
    --background: var(--ion-color-light);
  }
  
  .blur-content {
    filter: blur(10px);
  }
  
  .display-middle {
    position: absolute;
    top: 160px;
  }
  
  #splitBtn ion-icon {
    zoom: 1.2;
  }
  
  #gaugeArea {
    border: 1px solid grey;
    background: white;
    position: fixed;
    bottom: 0;
    right: 15px;
  }
  
  #gaugeClose {
    background-color: transparent;
    --background: transparent;
    --color: black;
    right: 10px;
    position: fixed;
    bottom: 91px;
  }
  
  #badge {
    height: 100%;
    padding-left: 8px;
    padding-top: 8px;
    border-radius: 10px;
  }
  
  .sdc-spinner {
    display: none;
    pointer-events: all;
    z-index: 99999;
    border: none;
    margin-top: 80px;
    padding: 0px;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    cursor: wait;
    position: fixed;
  }
  
  .sk-chase {
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    animation: sk-chase 2.5s infinite linear both;
  }
  
  .sk-chase-dot {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    animation: sk-chase-dot 2.0s infinite ease-in-out both;
  }
  
  .sk-chase-dot:before {
    content: '';
    display: block;
    width: 25%;
    height: 25%;
    background-color: #303f9f;
    border-radius: 100%;
    animation: sk-chase-dot-before 2.0s infinite ease-in-out both;
  }
  
  .sk-chase-dot:nth-child(1) {
    animation-delay: -1.1s;
  }
  
  .sk-chase-dot:nth-child(2) {
    animation-delay: -1.0s;
  }
  
  .sk-chase-dot:nth-child(3) {
    animation-delay: -0.9s;
  }
  
  .sk-chase-dot:nth-child(4) {
    animation-delay: -0.8s;
  }
  
  .sk-chase-dot:nth-child(5) {
    animation-delay: -0.7s;
  }
  
  .sk-chase-dot:nth-child(6) {
    animation-delay: -0.6s;
  }
  
  .sk-chase-dot:nth-child(1):before {
    animation-delay: -1.1s;
  }
  
  .sk-chase-dot:nth-child(2):before {
    animation-delay: -1.0s;
  }
  
  .sk-chase-dot:nth-child(3):before {
    animation-delay: -0.9s;
  }
  
  .sk-chase-dot:nth-child(4):before {
    animation-delay: -0.8s;
  }
  
  .sk-chase-dot:nth-child(5):before {
    animation-delay: -0.7s;
  }
  
  .sk-chase-dot:nth-child(6):before {
    animation-delay: -0.6s;
  }
  
  @keyframes sk-chase {
    100% {
      transform: rotate(360deg);
    }
  }
  
  @keyframes sk-chase-dot {
  
    80%,
    100% {
      transform: rotate(360deg);
    }
  }
  
  @keyframes sk-chase-dot-before {
    50% {
      transform: scale(0.4);
    }
  
    100%,
    0% {
      transform: scale(1.0);
    }
  }
  
  .alertDiv {
    position: -webkit-sticky;
    position: -moz-sticky;
    position: -ms-sticky;
    position: -o-sticky;
    position: sticky;
    z-index: 1;
    top: 0;
  }
  ion-title.ios {
    width: 75%;
  }
  #formio{
    padding: 5px !important;
  }
  