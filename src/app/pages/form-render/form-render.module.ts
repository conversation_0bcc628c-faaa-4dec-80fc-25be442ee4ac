import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { FormRenderPageRoutingModule } from './form-render-routing.module';

import { FormRenderPage } from './form-render.page';
import { ZXingScannerModule } from '@zxing/ngx-scanner';
import { BarcodeScanner } from '@awesome-cordova-plugins/barcode-scanner/ngx';
import { TranslateModule } from '@ngx-translate/core';
import { Geolocation } from '@awesome-cordova-plugins/geolocation/ngx';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { Network } from '@awesome-cordova-plugins/network/ngx';
import { NgxScannerComponent } from 'src/app/components/ngx-scanner/ngx-scanner.component';
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TranslateModule,
    ZXingScannerModule,
    FormRenderPageRoutingModule
  ],
  declarations: [FormRenderPage],
  providers: [BarcodeScanner, InAppBrowser, Network]
})
export class FormRenderPageModule {}
