
<!-- For Both FORMS and FORMSET FORMS -->
<ion-header *ngIf="!setBarcode">
  <ion-toolbar class="app-header" [ngClass]="{'hybridToolbar': isAndroid}">
    <ion-title>{{ formDescription }}</ion-title>
    <ion-buttons slot="start">
      <ion-button (click)='goBackToLastLocation()'>
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>

    <!-- Save and action button for all platforms -->
    <!-- Hiding the Gauge button for now -->
    <ion-buttons slot="end">
      <!-- <ion-button (click)="enableGaugeChart()"> -->
      <ion-button *ngIf="!isdisableFormCompletion">
        <ion-badge id="badge" color="light">{{calculatedPercentage}}%</ion-badge>
      </ion-button>
      <ion-button class="header-text header-text-style" (click)="renderBtnClicked(buttonType, task)" size="small" *ngIf="buttonType && buttonType.length > 0">{{ buttonType }}</ion-button>
      <ion-button *ngIf="!isdisableFormCompletion" slot="icon-only" size="small" (click)="presentPopover($event)"
        style="border: 1px solid white; border-bottom-right-radius: 6px; border-top-right-radius: 6px; margin: 1px;">
        <ion-icon name="caret-down-outline"></ion-icon>
      </ion-button>
      <ion-button  *ngIf="isdisableFormCompletion" slot="icon-only" size="small" (click)="presentPopover($event)"
      style="border: 1px solid white; border-radius: 6px; margin: 1px;">
      <ion-icon name="caret-down-outline"></ion-icon>
    </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-header *ngIf="setBarcode">
  <ion-toolbar class="header-toolbar">
    <ion-title>{{ formDescription }}</ion-title>
    <ion-buttons slot="start">
      <ion-button (click)='destroyScannerComponent()'>
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<!-- Form UI -->
<ion-content class="ion-padding" id="render-content" [ngClass]="{'blur-content': setBarcode}">
  <div class="sdc-spinner">
    <div class="sk-chase">
      <div class="sk-chase-dot"></div>
      <div class="sk-chase-dot"></div>
      <div class="sk-chase-dot"></div>
      <div class="sk-chase-dot"></div>
      <div class="sk-chase-dot"></div>
      <div class="sk-chase-dot"></div>
    </div>
  </div>

  <!-- Display alerts if there are unread alerts and review comments -->
  <div class="alertDiv" *ngIf="renderType != 'print'">
    <div *ngIf="showUnreadAlerts" (click)="openAlertModal()" class="alert alert-primary alert-dismissible fade show"
      role="alert">
      <span>{{ "There are " | translate}}<strong>{{ "Alerts" | translate }}</strong>{{" to check." | translate }}</span>
      <button type="button" class="close" style="outline: none;" (click)="dismissAlert()" data-dismiss="alert"
        aria-label="Close">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div *ngIf="showReviewComments" (click)="checkReviewOrComments()"
      class="alert alert-primary alert-dismissible fade show" role="alert">
      <span>{{ "There are " | translate}}<strong>{{ " Comments " | translate }}
        </strong>{{" to check." | translate }}</span>
      <button type="button" class="close" style="outline: none;" (click)="dismissReview()" data-dismiss="alert"
        aria-label="Close">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
  </div>

  <div id="formio"></div>
  <div *ngIf="showChart" id="gaugeArea"></div>
  <ion-button fill="clear" *ngIf="showChart" id="gaugeClose" (click)="dismissGauge()">X</ion-button>
</ion-content>

<!-- Barcode UI -->
<ion-content *ngIf="setBarcode" [ngClass]="{'display-middle': setBarcode}">
  <!-- <app-ngx-scanner></app-ngx-scanner> -->
</ion-content>