import { Component, <PERSON><PERSON>one, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NotificationListenerType, ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { <PERSON><PERSON><PERSON><PERSON>roller, ModalController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { AddPermitRoleComponent } from 'src/app/components/add-permit-role/add-permit-role.component';
import { CreatePermitComponent } from 'src/app/components/create-permit/create-permit.component';
import { PermitDetailsComponent } from 'src/app/components/permit-details/permit-details.component';
import { FACILITY_HEADER, PERMIT_HEADER, PERMIT_LOG, PERMIT_STAKEHOLDER, USER_CONTEXT_HEADER, USER_ROLE } from 'src/app/data-models/data_classes';
import { AppSpecificUtilityService } from 'src/app/services/app-specific-utility.service';
import { AttachmentsService } from 'src/app/services/attachments.service';
import { DataService } from 'src/app/services/data.service';
import { AppConstants, PermitStatus, PermitStatusProgress, PermitUserRole } from 'src/app/shared/app-constants';

@Component({
  selector: 'app-mobile-home',
  templateUrl: './mobile-home.page.html',
  styleUrls: ['./mobile-home.page.scss'],
})
export class MobileHomePage implements OnInit {
  public userFacility: string = '';
  public presentingElement = null;
  public facilitiesList: FACILITY_HEADER[] = [];
  public segmentValue: string = 'Assigned'; // Default to My Permits
  public selectedStatuses: string[] = ['Assigned']; // Default to My Permits
  public permitFilteredList: any[] = [];
  public permitList: any[] = [];
  public searchTerm: string = '';
  public userObject: any;
  public userRole: USER_ROLE;
  public devicePlatform = 'browser';
  public comment: string = '';
  public isAdmin: boolean = false;
  public isDissableCreatePermit: boolean = false;
  private subscriptions = new Subscription();
  public isShowLoadMoreButton: boolean = true;
  public isLoading: boolean = false;
  public isInitialLoad: boolean = true;
  public shimmerItems = Array(5).fill(0); // Array for shimmer skeleton items

  constructor(
    public alertController: AlertController,
    public dataService: DataService,
    public unviredSDK: UnviredCordovaSDK,
    public modalController: ModalController,
    public translate: TranslateService,
    public attachmentService: AttachmentsService,
    private ngZone: NgZone,
    private route: Router,
    public appSpecificUtility: AppSpecificUtilityService
  ) {
    let subs = this.unviredSDK
      .registerNotifListener()
      .subscribe(async (result) => {
        if (result?.type == NotificationListenerType.dataReceived) {
          this.ngZone.run(async () => {
            await this.fetchStatusBasedPermits(this.segmentValue);
          });
        } else if (result?.type == NotificationListenerType.dataSend) {
          this.ngZone.run(async () => {
            await this.fetchStatusBasedPermits(this.segmentValue);
          });
        } else if (result?.type == NotificationListenerType.dataChanged) {
          this.ngZone.run(async () => {
            await this.fetchStatusBasedPermits(this.segmentValue);
          });
        }
        else if (NotificationListenerType.JWTTokenReceived) {
          localStorage.setItem('PERMIT_token', result.data);
        }
      });

    this.subscriptions.add(subs);
  }


  ngOnInit() {
    this.presentingElement = document.querySelector('.ion-page');
  }

  async ionViewDidEnter() {
    this.devicePlatform = this.dataService.getDevicePlatform();
    await this.serverCall();
    await this.getUserRoles()
    this.isAdmin = await this.findAdminUser();
    await this.findRequestUserRole();
    await this.fetchPermitsWithMultipleStatuses();
  }

  ngOnDestroy(): void {
    console.log('Permit list component killed');
    this.subscriptions.unsubscribe();
  }

  async findRequestUserRole() {
    let findRequestUserRoleQuery = `SELECT * FROM USER_ROLE WHERE REQUEST = 'true'`;
    let findRequestUserRoleQueryResult =
      await this.unviredSDK.dbExecuteStatement(findRequestUserRoleQuery);
    this.isDissableCreatePermit =
      findRequestUserRoleQueryResult?.data?.length > 0 ? false : true;
  }

  async logout() {
    const alert = await this.alertController.create({
      message: `Are you sure you want to logout?`,
      mode: 'ios',
      buttons: [
        {
          text: 'Cancel',
          cssClass: 'modal-button-cancel',
          handler: (blah) => { },
        },
        {
          text: 'Logout',
          handler: async (data) => {
            this.dataService.logoutToInitialPage();
          },
        },
      ],
    });
    await alert.present();
  }

  //Make server call
  async serverCall() {
    this.dataService.isStartCustomization = false;
    // Set loading state instead of showing loader
    this.isLoading = true;
    this.isInitialLoad = true;

    // Download Customization
    await this.dataService.getCustomization();
    await this.dataService.getUserContext();
    await this.getFormTempaltes();
    let userContextResult = await this.dataService.getData(
      'USER_CONTEXT_HEADER'
    );
    await this.getUserAgentsFromServer();
    await this.getAllFacilitiesFromDb();
    this.userObject = userContextResult[0];
    this.userFacility = userContextResult[0]?.CURRENT_FACILITY ? userContextResult[0]?.CURRENT_FACILITY : ''
    let customData = {
      STRUCTURE: [
        {
          STRUCTURE_HEADER: {
            FACILITY_ID: this.userFacility,
            DIVISION_ID: '',
            TAG: '',
            NAME: '',
            CATEGORY: '',
            STRUCT_TYPE: '',
            STATUS: '',
            P_MODE: '',
          },
        },
      ],
    };
    await this.dataService.getAllUserData()
    await this.dataService.getFacility();
    await this.dataService.getAgents();
    await this.dataService.getStructures(customData);
    await this.dataService.downloadPermits(AppConstants.PERMITS_LIMIT, 0, true);

    // Clear loading state
    this.isLoading = false;
    this.isInitialLoad = false;
  }



  async displayAlert(messageReceived: any) {
    const alert = await this.alertController.create({
      message: messageReceived,
      mode: 'ios',
      buttons: [
        {
          text: 'Okay',
        },
      ],
    });
    await alert.present();
  }

  async getUserAgentsFromServer() {
    let userAgent = await this.dataService.getData('AGENT_HEADER');
    for await (let element of userAgent) {
      let customData = {
        AGENT_USER: [
          {
            AGENT_USER_HEADER: {
              AGENT_ID: element?.AGENT_ID,
            },
          },
        ],
      };
      await this.dataService.getAgentUser(customData);
    }
  }

  async getAllFacilitiesFromDb() {
    // this.facilitiesList = await this.dataService.getData(
    //   'FACILITY_HEADER'
    // );
    let result: any = await this.dataService.getData('USER_FACILITY', null, 'NAME');
    if (result.length > 0) { this.facilitiesList = result }
  }

  async onFacilityChange(facility: FACILITY_HEADER) {
    this.userFacility = facility.FACILITY_ID;
    await this.updateSelectedFacility(facility);
    await this.getUserAgentsFromServer();
    await this.getAllFacilitiesFromDb();
    await this.dataService.getAllUserData()
    await this.unviredSDK.dbExecuteStatement('DELETE FROM PERMIT_HEADER');
    this.permitFilteredList = [];
    this.permitList = [];
    await this.dataService.downloadPermits(null, null, true);
    let customData = { STRUCTURE: [{ STRUCTURE_HEADER: { FACILITY_ID: this.userFacility } }] };
    await this.dataService.getStructures(customData);
    this.segmentValue = 'Assigned';
    this.selectedStatuses = ['Assigned'];
    await this.fetchPermitsWithMultipleStatuses();
    await this.modalController.dismiss();
  }

  async refreshPermit() {
    const alert = await this.alertController.create({
      cssClass: 'my-custom-class',
      header: 'Confirm',
      mode: 'ios',
      backdropDismiss: false,
      message: `${this.translate.instant(
        'This process will download the latest permits from the server. Any local changes that you may have will get overwritten. Are you sure you want to refresh permits'
      )}?`,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => { },
        },
        {
          text: 'Refresh Permits',
          handler: async () => {
            this.isShowLoadMoreButton = true;
            await this.dataService.getAllUserData()
            await this.downloadPermits(AppConstants.PERMITS_LIMIT, 0, true);
          },
        },
      ],
    });

    await alert.present();
  }

  async downloadPermits(limit: number, offset: number, autoSave: boolean) {
    // Set loading state instead of showing loader
    this.isLoading = true;
    this.permitList = [];
    this.permitFilteredList = [];
    let deleteInspectionsQuery = `DELETE FROM PERMIT_HEADER`;
    await this.unviredSDK.dbExecuteStatement(deleteInspectionsQuery);
    let deletestakeHoldersQuery = `DELETE FROM PERMIT_STAKEHOLDER`;
    await this.unviredSDK.dbExecuteStatement(deletestakeHoldersQuery);
    let permitsResponse: any = await this.dataService.downloadPermits(
      limit,
      offset,
      autoSave
    );
    // Clear loading state
    this.isLoading = false;
    if (permitsResponse.type == ResultType.success) {
      if (permitsResponse?.data?.InfoMessage?.length > 0) {
        let msg = this.dataService.handleInfoMessage(permitsResponse);
        this.showAlert(this.translate.instant('Alert'), msg);
      } else {
        await this.fetchPermitsWithMultipleStatuses();
      }
    } else {
      this.dataService.displayErrorMessageDialog(permitsResponse.error);
    }
  }

  //Pull to refresh permits
  async doRefresh(event: any) {
    this.unviredSDK.getMessages();
    // Reset search bar visibility when refreshing
    this.resetSearchBarVisibility();
    setTimeout(() => {
      event.target.complete();
    }, 2000);
  }

  async segmentChange(event: any) {
    this.fetchStatusBasedPermits(event?.detail?.value);
  }

  /**
   * Handle multi-select status filter change
   * @param event The select change event
   */
  async onStatusFilterChange(event: any) {
    this.selectedStatuses = event.detail.value || [];
    await this.fetchPermitsWithMultipleStatuses();
  }

  /**
   * Fetch permits based on multiple selected statuses
   */
  async fetchPermitsWithMultipleStatuses() {
    this.permitList = [];
    this.permitFilteredList = [];

    if (this.selectedStatuses.length === 0) {
      return;
    }

    // If "All" is selected, fetch all permits
    if (this.selectedStatuses.includes('All')) {
      await this.fetchStatusBasedPermits('All');
      return;
    }

    let allPermits: any[] = [];

    for (const status of this.selectedStatuses) {
      let fetchPermitsQuery = '';

      switch (status) {
        case 'Assigned':
          fetchPermitsQuery = `SELECT DISTINCT ph.* FROM PERMIT_HEADER ph JOIN PERMIT_STAKEHOLDER s ON ph.PERMIT_NO = s.PERMIT_NO WHERE s.USER_ID = '${this.userObject?.USER_ID}'`;
          break;
        default:
          fetchPermitsQuery = `SELECT * FROM PERMIT_HEADER WHERE FACILITY_ID = '${this.userFacility}' AND STATUS='${status}' ORDER BY PERMIT_NO DESC`;
          break;
      }

      try {
        const result = await this.unviredSDK.dbExecuteStatement(fetchPermitsQuery);
        if (result.type == ResultType.success && result?.data?.length > 0) {
          allPermits = [...allPermits, ...result.data];
        }
      } catch (error) {
        console.error('Error fetching permits for status:', status, error);
      }
    }

    // Remove duplicates based on PERMIT_NO
    const uniquePermits = allPermits.filter((permit, index, self) =>
      index === self.findIndex(p => p.PERMIT_NO === permit.PERMIT_NO)
    );

    if (uniquePermits.length > 0) {
      this.permitList = uniquePermits;

      // Add permit type information to each permit
      for (let element of this.permitList) {
        element.permitTypeInfo = await this.getPermitTypeInfo(element.PERMIT_TYPE);

        // Add button logic for each permit
        await this.setPermitButtonLogic(element);
      }

      this.setFilteredItems();
    }
  }

  /**
   * Set permit button logic based on status
   * @param element The permit element
   */
  async setPermitButtonLogic(element: any) {
    switch (element.STATUS) {
      case PermitStatus.IN_REVIEW:
        let fetchApproversCountQuery = `SELECT * FROM PERMIT_LOG WHERE PERMIT_NO= '${element.PERMIT_NO}' AND P_MODE = 'A' AND PERMIT_STATUS = 'APPROVED'`;
        let fetchApproversCountQueryResult = await this.unviredSDK.dbExecuteStatement(fetchApproversCountQuery);
        element.isShowDetailsButton = fetchApproversCountQueryResult?.data?.length > 0 ? false : true;

        let countQuery = `SELECT COUNT(ROLE) AS COUNT FROM PERMIT_STAKEHOLDER WHERE ROLE='APPROVE' AND PERMIT_NO= '${element.PERMIT_NO}' IN( P_MODE='A', P_MODE IS NULL)`;
        let countResult = await this.unviredSDK.dbExecuteStatement(countQuery);
        element.isShowDetailsButton = countResult?.data[0]?.COUNT > 0 ? false : true;
        break;

      case PermitStatus.APPROVED:
        let fetchIssuedCountQuery = `SELECT * FROM PERMIT_LOG WHERE PERMIT_NO= '${element.PERMIT_NO}' AND P_MODE = 'A' AND PERMIT_STATUS = 'ISSUED'`;
        let fetchIssuedCountQueryResult = await this.unviredSDK.dbExecuteStatement(fetchIssuedCountQuery);
        element.isShowDetailsButton = fetchIssuedCountQueryResult?.data?.length > 0 ? false : true;

        let issuerCountQuery = `SELECT COUNT(ROLE) AS COUNT FROM PERMIT_STAKEHOLDER WHERE ROLE='ISSUE' AND PERMIT_NO= '${element.PERMIT_NO}' IN( P_MODE='A', P_MODE IS NULL)`;
        let issuerCountQueryResult = await this.unviredSDK.dbExecuteStatement(issuerCountQuery);
        element.isShowDetailsButton = issuerCountQueryResult?.data[0]?.COUNT > 0 ? false : true;
        break;

      case PermitStatus.ISSUED:
        let resCount = await this.getExtendUserRoleCount();
        element.isShowReviseButton = resCount == 0 ? false : true;
        if (element.isShowReviseButton) {
          let fetchClosedCountQuery = `SELECT * FROM PERMIT_LOG WHERE PERMIT_NO= '${element.PERMIT_NO}' AND P_MODE = 'A' AND PERMIT_STATUS = 'CLOSED'`;
          let fetchClosedCountQueryResult = await this.unviredSDK.dbExecuteStatement(fetchClosedCountQuery);
          element.isShowDetailsButton = fetchClosedCountQueryResult?.data?.length > 0 ? false : true;
        } else {
          element.isShowDetailsButton = false;
        }

        let resClosedCount = await this.getClosedUserRoleCount();
        if (resClosedCount == 0) {
          element.isShowDetailsButton = false;
        } else {
          let closeCountQuery = `SELECT COUNT(ROLE) AS COUNT FROM PERMIT_STAKEHOLDER WHERE ROLE='CLOSE' AND PERMIT_NO= '${element.PERMIT_NO}' IN( P_MODE='A', P_MODE IS NULL)`;
          let closeCountQueryResult = await this.unviredSDK.dbExecuteStatement(closeCountQuery);
          element.isShowDetailsButton = closeCountQueryResult?.data[0]?.COUNT > 0 ? false : true;
        }
        break;

      default:
        break;
    }
  }

  async fetchStatusBasedPermits(status: string) {
    let fetchPermitsQuery = '';
    this.permitList = [];
    this.permitFilteredList = [];
    switch (status) {
      case 'All':
        fetchPermitsQuery = `SELECT * FROM PERMIT_HEADER WHERE FACILITY_ID = '${this.userFacility}' ORDER BY PERMIT_NO DESC`;
        break;
      case 'Assigned':
        fetchPermitsQuery = `SELECT DISTINCT ph.* FROM PERMIT_HEADER ph JOIN PERMIT_STAKEHOLDER s ON ph.PERMIT_NO = s.PERMIT_NO WHERE s.USER_ID = '${this.userObject?.USER_ID}'`;
        break;
      default:
        fetchPermitsQuery = `SELECT * FROM PERMIT_HEADER WHERE FACILITY_ID = '${this.userFacility}' AND STATUS='${status}' ORDER BY PERMIT_NO DESC`;
        break;
    }

    await this.unviredSDK
      .dbExecuteStatement(fetchPermitsQuery)
      .then(async (result: any) => {
        if (result.type == ResultType.success) {
          if (result?.data?.length > 0) {
            this.permitList = result.data;

            // Add permit type information to each permit
            for await (let element of this.permitList) {
              // Get permit type information
              element.permitTypeInfo = await this.getPermitTypeInfo(element.PERMIT_TYPE);

              switch (element.STATUS) {
                case PermitStatus.IN_REVIEW:
                  let fetchApproversCountQuery = `SELECT * FROM PERMIT_LOG WHERE PERMIT_NO= '${element.PERMIT_NO}' AND P_MODE = 'A' AND PERMIT_STATUS = 'APPROVED'`;
                  let fetchApproversCountQueryResult =
                    await this.unviredSDK.dbExecuteStatement(
                      fetchApproversCountQuery
                    );
                  element.isShowDetailsButton =
                    fetchApproversCountQueryResult?.data?.length > 0
                      ? false
                      : true;

                  let countQuery = `SELECT COUNT(ROLE) AS COUNT FROM PERMIT_STAKEHOLDER WHERE ROLE='APPROVE' AND PERMIT_NO= '${element.PERMIT_NO}' IN( P_MODE='A', P_MODE IS NULL)`;
                  let countResult = await this.unviredSDK.dbExecuteStatement(
                    countQuery
                  );
                  element.isShowDetailsButton =
                    countResult?.data[0]?.COUNT > 0 ? false : true;
                  break;
                case PermitStatus.APPROVED:
                  let fetchIssuedCountQuery = `SELECT * FROM PERMIT_LOG WHERE PERMIT_NO= '${element.PERMIT_NO}' AND P_MODE = 'A' AND PERMIT_STATUS = 'ISSUED'`;
                  let fetchIssuedCountQueryResult =
                    await this.unviredSDK.dbExecuteStatement(
                      fetchIssuedCountQuery
                    );
                  element.isShowDetailsButton =
                    fetchIssuedCountQueryResult?.data?.length > 0
                      ? false
                      : true;

                  let issuerCountQuery = `SELECT COUNT(ROLE) AS COUNT FROM PERMIT_STAKEHOLDER WHERE ROLE='ISSUE' AND PERMIT_NO= '${element.PERMIT_NO}' IN( P_MODE='A', P_MODE IS NULL)`;
                  let issuerCountQueryResult =
                    await this.unviredSDK.dbExecuteStatement(issuerCountQuery);
                  element.isShowDetailsButton =
                    issuerCountQueryResult?.data[0]?.COUNT > 0 ? false : true;

                  break;
                case PermitStatus.ISSUED:
                  let resCount = await this.getExtendUserRoleCount();
                  element.isShowReviseButton = resCount == 0 ? false : true;
                  if (element.isShowReviseButton) {
                    let fetchClosedCountQuery = `SELECT * FROM PERMIT_LOG WHERE PERMIT_NO= '${element.PERMIT_NO}' AND P_MODE = 'A' AND PERMIT_STATUS = 'CLOSED'`;
                    let fetchClosedCountQueryResult =
                      await this.unviredSDK.dbExecuteStatement(
                        fetchClosedCountQuery
                      );
                    element.isShowDetailsButton =
                      fetchClosedCountQueryResult?.data?.length > 0
                        ? false
                        : true;
                  } else {
                    element.isShowDetailsButton = false;
                  }

                  let resClosedCount = await this.getClosedUserRoleCount();
                  if (resClosedCount == 0) {
                    element.isShowDetailsButton = false;
                  } else {
                    let closeCountQuery = `SELECT COUNT(ROLE) AS COUNT FROM PERMIT_STAKEHOLDER WHERE ROLE='CLOSE' AND PERMIT_NO= '${element.PERMIT_NO}' IN( P_MODE='A', P_MODE IS NULL)`;
                    let closeCountQueryResult =
                      await this.unviredSDK.dbExecuteStatement(closeCountQuery);
                    element.isShowDetailsButton =
                      closeCountQueryResult?.data[0]?.COUNT > 0 ? false : true;
                  }
                  break;
                default:
                  break;
              }
            }

            this.setFilteredItems();
          }
        }
      });
  }

  setFilteredItems() {
    if (!this.searchTerm || this.searchTerm.trim() === '') {
      this.permitFilteredList = [...this.permitList];
    } else {
      let res = this.filterItems(this.searchTerm);
      this.permitFilteredList = res || [];
    }
  }

  async getExtendUserRoleCount() {
    let userRoleQuery = `SELECT DISTINCT 1 AS COUNT FROM [USER_ROLE] WHERE (EXTEND = 'true')`;
    let userRoleQueryResult = await this.unviredSDK.dbExecuteStatement(
      userRoleQuery
    );
    if (userRoleQueryResult?.data[0]?.COUNT > 0) {
      return userRoleQueryResult.data[0].COUNT;
    } else return 0;
  }

  async getClosedUserRoleCount() {
    let userRoleQuery = `SELECT DISTINCT 1 AS COUNT FROM [USER_ROLE] WHERE (CLOSE = 'true')`;
    let userRoleQueryResult = await this.unviredSDK.dbExecuteStatement(
      userRoleQuery
    );
    if (userRoleQueryResult?.data[0]?.COUNT > 0) {
      return userRoleQueryResult.data[0].COUNT;
    } else return 0;
  }

  filterItems(searchTerm) {
    if (!searchTerm || searchTerm.trim() === '') {
      return this.permitList;
    }

    searchTerm = searchTerm.trim().toLowerCase();

    return this.permitList.filter((item) => {
      return (
        (item.DESCRIPTION && item.DESCRIPTION.toLowerCase().indexOf(searchTerm) > -1) ||
        (item.PERMIT_NO && item.PERMIT_NO.toLowerCase().indexOf(searchTerm) > -1) ||
        (item.TAG && item.TAG.toLowerCase().indexOf(searchTerm) > -1) ||
        (item.PERMIT_TYPE && item.PERMIT_TYPE.toLowerCase().indexOf(searchTerm) > -1)
      );
    });
  }

  async showPermitDetails(permit: any) {
    localStorage.setItem('selectedPermit', JSON.stringify(permit));
    let userRole: string = '';
    let isShowModal: boolean = false;
    let isProcessSwitchCase: boolean = true;

    if (isProcessSwitchCase) {
      switch (permit.STATUS) {
        case PermitStatus.OPEN:
          isShowModal = true;
          break;
        case PermitStatus.IN_REVIEW:
          isShowModal = true;
          userRole = PermitUserRole.APPROVE;
          break;
        case PermitStatus.APPROVED:
          isShowModal = true;
          userRole = PermitUserRole.ISSUE;
          break;
        case PermitStatus.ISSUED:
          isShowModal = true;
          userRole = PermitUserRole.CLOSE;
          break;
        case PermitStatus.CANCELLED:
          isShowModal = true;
          userRole = PermitUserRole.CLOSE;
          break;
        case PermitStatus.CLOSED:
          isShowModal = true;
          userRole = PermitUserRole.CLOSE;
          break;
      }
    }

    // Show inspection details modal based on status
    if (isShowModal) {
      localStorage.setItem('permitDetailsFromreport', "");
      const modal = await this.modalController.create({
        component: PermitDetailsComponent,
        cssClass: 'full-screen-modal',
        id: 'permit-details',
        componentProps: {
          userRole: userRole,
          permit: permit,
          isReport: false
        },
      });
      modal.onDidDismiss().then(async (result) => {
        if (result.data) {
          console.log('modal dismiss');
          await this.fetchStatusBasedPermits(this.segmentValue);
        }
      });
      return await modal.present();
    }
  }

  // Display progress bar label names based on status.
  getLabelBasedOnStatus(status: string) {
    let label = '';
    switch (status.trim()) {
      case PermitStatus.OPEN:
        label = this.translate.instant('Open');
        break;
      case PermitStatus.IN_REVIEW:
        label = this.translate.instant('In Review');
        break;
      case PermitStatus.CANCELLED:
        label = this.translate.instant('Canceled');
        break;
      case PermitStatus.APPROVED:
        label = this.translate.instant('Approved');
        break;
      case PermitStatus.ISSUED:
        label = this.translate.instant('Issued');
        break;
      case PermitStatus.CLOSED:
        label = this.translate.instant('Closed');
        break;
      default:
        label = this.translate.instant('Open');
        break;
    }
    return label;
  }

  permitStatusProgress(status: string) {
    let percentage = 0;
    switch (status.trim()) {
      case PermitStatus.OPEN:
        percentage = PermitStatusProgress.OPEN;
        break;
      case PermitStatus.IN_REVIEW:
        percentage = PermitStatusProgress.IN_REVIEW;
        break;
      case PermitStatus.APPROVED:
        percentage = PermitStatusProgress.APPROVED;
        break;
      case PermitStatus.ISSUED:
        percentage = PermitStatusProgress.ISSUED;
        break;
      case PermitStatus.CLOSED:
        percentage = PermitStatusProgress.COMPLETED;
        break;
      case PermitStatus.CANCELLED:
        percentage = PermitStatusProgress.CANCELLED;
        break;
      default:
        percentage = PermitStatusProgress.OPEN;
        break;
    }
    return percentage;
  }

  // Apply color for progress bar based on status.
  GetProgressPercentCssClass(status: string) {
    let cssClass = '';
    switch (status.trim()) {
      case PermitStatus.OPEN:
        cssClass = 'orange';
        break;
      case PermitStatus.IN_REVIEW:
        cssClass = 'orange';
        break;
      case PermitStatus.CANCELLED:
        cssClass = 'light-Grey';
        break;
      case PermitStatus.CLOSED:
        cssClass = 'darak-Green';
        break;
      case PermitStatus.APPROVED:
        cssClass = ' light-Green';
        break;
      case PermitStatus.ISSUED:
        cssClass = 'darak-Green';
        break;
      default:
        cssClass = 'orange';
        break;
    }
    return cssClass;
  }

  isActionButtonDissabled(status: string) {
    let isDissable = false;
    switch (status) {
      case PermitStatus.OPEN:
        // isDissable = this.userRole.REVIEW == 'true' ? false : true;
        isDissable = false;
        break;
      case PermitStatus.IN_REVIEW:
        isDissable = this.userRole.APPROVE == 'true' ? false : true;
        break;
      case PermitStatus.APPROVED:
        isDissable = this.userRole.ISSUE == 'true' ? false : true;
        break;
      case PermitStatus.ISSUED:
        isDissable = this.userRole.ISSUE == 'true' ? false : true;
        break;
      case 'CLOSE':
        isDissable = this.userRole.CLOSE == 'true' ? false : true;
        break;
      case 'REVISE':
        isDissable = this.userRole.EXTEND == 'true' ? false : true;
        break;
      case PermitStatus.CANCELLED:
        isDissable = this.userRole.CANCEL == 'true' ? false : true;
        break;
    }
    return isDissable;
  }

  async getUserRoles() {
    let getUserRoleResult = await this.unviredSDK.dbExecuteStatement(
      `SELECT * FROM USER_ROLE`
    );
    if (getUserRoleResult.type == ResultType.success) {
      if (getUserRoleResult?.data?.length > 0) {
        this.userRole = getUserRoleResult.data[0];
      }
    }
  }

  async permitAction(
    status: any,
    permitHeader: any,
    isRequriedComment: boolean
  ) {
    switch (status) {
      case PermitStatus.OPEN:
        let res = await this.addPermitUserAssignPopup(permitHeader);
        if (res) {
          await this.submitPermit(status, permitHeader, res);
        }
        break;
        case PermitStatus.IN_REVIEW:
          if(this.isPermitIsExpired(permitHeader)){
            await this.showAlert(
              this.translate.instant('Alert'),
              'Permit can only be approved before expiry'
            );
          } else {
            await this.submitPermit(status, permitHeader);
          }
        break;
        case PermitStatus.REVISE:
          if(this.isPermitIsExpired(permitHeader)){
            if(permitHeader.IS_EXTENDED == true || permitHeader.IS_EXTENDED == 'true') {
              await this.showAlert(
                this.translate.instant('Alert'),
                'The permit has expired after being extended and is no longer eligible for revision.'
              );
            } else {
              await this.showAlert(
                this.translate.instant('Alert'),
                'Permit can only be revised before expiry'
              );
            }
          } else {
            await this.submitPermit(status, permitHeader);
          }
        break;
        case PermitStatus.APPROVED:
          if(this.isPermitIsExpired(permitHeader)){
            await this.showAlert(
              this.translate.instant('Alert'),
              'Permit can only be issued on the date of execution and before expiry'
            );
          } else {
            await this.submitPermit(status, permitHeader);
          }
        break;
      default:
        await this.submitPermit(status, permitHeader);
        break;
    }
  }

  async submitPermit(status: any, permitHeader: any, selectedUser?: any) {
    if (status && status != null && status != '') {
      let isProceedProcess: boolean = true;

      if (isProceedProcess) {
        // let inspectionDocUpdateQuery = `UPDATE PERMIT_DOC SET THUMBNAIL = '' WHERE OBJECT_STATUS = 1 AND P_MODE = 'A' AND PERMIT_NO = '${permitHeader.PERMIT_NO}'`;
        // await this.unviredSDK.dbExecuteStatement(inspectionDocUpdateQuery);

        // Updating inspection header table with new p-mode.
        let inspectionUpdateQuery = `UPDATE PERMIT_HEADER SET P_MODE = 'M', OBJECT_STATUS = 2 WHERE PERMIT_NO = '${permitHeader.PERMIT_NO}'`;

        // Call Async modify document api
        // if (this.devicePlatform == 'browser') {
        // } else {
        //   let fetchDocQuery = `SELECT * FROM PERMIT_DOC WHERE FID = '${permitHeader.LID}'`;
        //   let fetchDocResult: any = await this.unviredSDK.dbExecuteStatement(
        //     fetchDocQuery
        //   );
        //   if (fetchDocResult && fetchDocResult.data.length > 0) {
        //     for (let doc = 0; doc < fetchDocResult.data.length; doc++) {
        //       let fetchDocHeaderQuery = `SELECT * FROM DOCUMENT_HEADER WHERE DOC_ID = '${fetchDocResult.data[doc].DOC_ID}'`;
        //       let fetchDocHeaderResult =
        //         await this.unviredSDK.dbExecuteStatement(fetchDocHeaderQuery);
        //       if (
        //         fetchDocHeaderResult &&
        //         fetchDocHeaderResult.data.length > 0
        //       ) {
        //         await this.attachmentService.uploadAttachment(
        //           fetchDocHeaderResult.data[0],
        //           status
        //         );
        //       }
        //     }
        //   }
        // }

        let inspectionUpdateResult = await this.unviredSDK.dbExecuteStatement(
          inspectionUpdateQuery
        );
        if (inspectionUpdateResult.type == ResultType.success) {
          permitHeader.P_MODE = 'M';
          permitHeader.OBJECT_STATUS = 2;
        }

        let permitStatusLocal: string = '';
        let comment: string = '';
        let user = this.userObject?.FIRST_NAME + ' ' + this.userObject?.LAST_NAME;

        switch (status) {
          case PermitStatus.OPEN:
            permitStatusLocal = PermitStatus.IN_REVIEW;
            comment = `${user} ${this.translate.instant(
              'submit for the review process on'
            )} ${this.getCurrentTime()}`;

            let stakeHolder = new PERMIT_STAKEHOLDER();
            stakeHolder.P_MODE = 'A';
            stakeHolder.USER_ID = selectedUser.USER_ID;
            stakeHolder.ROLE = 'REVIEW';
            stakeHolder.PERMIT_NO = permitHeader.PERMIT_NO;
            stakeHolder.SYNC_STATUS = 0;
            stakeHolder.OBJECT_STATUS = 1;
            stakeHolder.LID = this.unviredSDK.guid().replace(/-/g, '');
            stakeHolder.FID = permitHeader.LID;
            stakeHolder.COMMENT = this.comment;
            stakeHolder.AGENT_ID = permitHeader.AGENT_ID_INT;
            stakeHolder.ROW_ID = await this.findMaxRowNumberFromDb(
              permitHeader.PERMIT_NO
            );

            await this.unviredSDK.dbInsert(
              'PERMIT_STAKEHOLDER',
              stakeHolder,
              false
            );

            //Setting permit log object status to 0 to send only one permit log to server.
            let permitLogUpdateQuery = `UPDATE PERMIT_LOG SET OBJECT_STATUS = 0 WHERE PERMIT_NO = '${permitHeader.PERMIT_NO}' AND PERMIT_STATUS != '${permitStatusLocal}' AND PERMIT_STATUS NOT NULL`;
            await this.unviredSDK.dbExecuteStatement(permitLogUpdateQuery);

            // Adding new permit log.
            let i = 0;
            if (i == 0) {
              i = i + 1;
              let permitLog = new PERMIT_LOG();
              permitLog.LOG_NO = await this.findMaxLogNumberFromDb(
                permitHeader.PERMIT_NO
              );

              permitLog.LID = this.unviredSDK.guid().replace(/-/g, '');
              permitLog.SYNC_STATUS = 0;
              permitLog.OBJECT_STATUS = 1;
              permitLog.CREATED_ON = new Date().getTime();
              permitLog.P_MODE = 'A';
              permitLog.PERMIT_NO = permitHeader.PERMIT_NO;
              permitLog.FID = permitHeader.LID;
              permitLog.PERMIT_STATUS = permitStatusLocal;
              permitLog.COMMENT = comment;
              permitLog.CREATED_BY = user;
              let permitLogInsertResult = await this.unviredSDK.dbInsert(
                'PERMIT_LOG',
                permitLog,
                false
              );
            }
            await this.regularSubmitPermit(permitHeader);
            break;

          case PermitStatus.REVISE:
            permitStatusLocal = PermitStatus.IN_REVIEW;
            comment = `${user} ${this.translate.instant(
              'Revise Permit for the review process on'
            )} ${this.getCurrentTime()}`;

            //Setting permit log object status to 0 to send only one permit log to server.
            let permitLogUpdateQuery1 = `UPDATE PERMIT_LOG SET OBJECT_STATUS = 0 WHERE PERMIT_NO = '${permitHeader.PERMIT_NO}' AND PERMIT_STATUS != '${permitStatusLocal}' AND PERMIT_STATUS NOT NULL`;
            await this.unviredSDK.dbExecuteStatement(permitLogUpdateQuery1);

            // Adding new permit log.
            let permitLog = new PERMIT_LOG();
            permitLog.LOG_NO = await this.findMaxLogNumberFromDb(
              permitHeader.PERMIT_NO
            );

            permitLog.LID = this.unviredSDK.guid().replace(/-/g, '');
            permitLog.SYNC_STATUS = 0;
            permitLog.OBJECT_STATUS = 1;
            permitLog.CREATED_ON = new Date().getTime();
            permitLog.P_MODE = 'A';
            permitLog.PERMIT_NO = permitHeader.PERMIT_NO;
            permitLog.FID = permitHeader.LID;
            permitLog.PERMIT_STATUS = permitStatusLocal;
            permitLog.COMMENT = comment;
            permitLog.CREATED_BY = user;
            let permitLogInsertResult = await this.unviredSDK.dbInsert(
              'PERMIT_LOG',
              permitLog,
              false
            );
            await this.regularSubmitPermit(permitHeader);
            break;

          case PermitStatus.IN_REVIEW:
            let query = `SELECT * FROM PERMIT_LOG WHERE PERMIT_NO= '${permitHeader.PERMIT_NO}' AND PERMIT_STATUS='APPROVED' AND P_MODE='A'`;
            let countResult = await this.unviredSDK.dbExecuteStatement(query);
            if (countResult && countResult.data.length == 0) {
              let user = this.userObject?.FIRST_NAME + ' ' + this.userObject?.LAST_NAME;
              let comment = `${user} send for APPROVE ${this.getCurrentTime()}`;

              let permitLog = new PERMIT_LOG();
              permitLog.LOG_NO = await this.findMaxLogNumberFromDb(
                permitHeader.PERMIT_NO
              );

              permitLog.LID = this.unviredSDK.guid().replace(/-/g, '');
              permitLog.SYNC_STATUS = 0;
              permitLog.OBJECT_STATUS = 1;
              permitLog.CREATED_ON = new Date().getTime();
              permitLog.P_MODE = 'A';
              permitLog.PERMIT_NO = permitHeader.PERMIT_NO;
              permitLog.FID = permitHeader.LID;
              permitLog.PERMIT_STATUS = 'APPROVED';
              permitLog.COMMENT = comment;
              permitLog.CREATED_BY = user;
              await this.unviredSDK.dbInsert('PERMIT_LOG', permitLog, false);

              await this.regularSubmitPermit(permitHeader);
            } else {
              await this.regularSubmitPermit(permitHeader);
            }
            break;

          case PermitStatus.APPROVED:
            let approveQuery = `SELECT * FROM PERMIT_LOG WHERE PERMIT_NO= '${permitHeader.PERMIT_NO}' AND PERMIT_STATUS='ISSUED' AND P_MODE='A'`;
            let approveQueryResult = await this.unviredSDK.dbExecuteStatement(
              approveQuery
            );
            if (approveQueryResult && approveQueryResult?.data?.length == 0) {
              let user = this.userObject?.FIRST_NAME + ' ' + this.userObject?.LAST_NAME;
              let comment = `${user} send for ISSUE ${this.getCurrentTime()}`;

              let permitLog = new PERMIT_LOG();
              permitLog.LOG_NO = await this.findMaxLogNumberFromDb(
                permitHeader.PERMIT_NO
              );

              permitLog.LID = this.unviredSDK.guid().replace(/-/g, '');
              permitLog.SYNC_STATUS = 0;
              permitLog.OBJECT_STATUS = 1;
              permitLog.CREATED_ON = new Date().getTime();
              permitLog.P_MODE = 'A';
              permitLog.PERMIT_NO = permitHeader.PERMIT_NO;
              permitLog.FID = permitHeader.LID;
              permitLog.PERMIT_STATUS = 'ISSUED';
              permitLog.COMMENT = comment;
              permitLog.CREATED_BY = user;
              await this.unviredSDK.dbInsert('PERMIT_LOG', permitLog, false);

              await this.regularSubmitPermit(permitHeader);
            } else {
              await this.regularSubmitPermit(permitHeader);
            }
            break;

          case PermitStatus.CLOSED:
            let closedQuery = `SELECT * FROM PERMIT_LOG WHERE PERMIT_NO= '${permitHeader.PERMIT_NO}' AND PERMIT_STATUS='CLOSED' AND P_MODE='A'`;
            let closedQueryResult = await this.unviredSDK.dbExecuteStatement(
              closedQuery
            );
            if (closedQueryResult && closedQueryResult?.data?.length == 0) {
              let user = this.userObject?.FIRST_NAME + ' ' + this.userObject?.LAST_NAME;
              let comment = `${user} send for CLOSED ${this.getCurrentTime()}`;

              let permitLog = new PERMIT_LOG();
              permitLog.LOG_NO = await this.findMaxLogNumberFromDb(
                permitHeader.PERMIT_NO
              );

              permitLog.LID = this.unviredSDK.guid().replace(/-/g, '');
              permitLog.SYNC_STATUS = 0;
              permitLog.OBJECT_STATUS = 1;
              permitLog.CREATED_ON = new Date().getTime();
              permitLog.P_MODE = 'A';
              permitLog.PERMIT_NO = permitHeader.PERMIT_NO;
              permitLog.FID = permitHeader.LID;
              permitLog.PERMIT_STATUS = 'CLOSED';
              permitLog.COMMENT = comment;
              permitLog.CREATED_BY = user;
              await this.unviredSDK.dbInsert('PERMIT_LOG', permitLog, false);

              await this.regularSubmitPermit(permitHeader);
            } else {
              await this.regularSubmitPermit(permitHeader);
            }
            break;
          default:
            await this.regularSubmitPermit(permitHeader);
            break;
        }
      }
    }
  }

  async findMaxRowNumberFromDb(permitNo: any) {
    let maxNumber = 1;
    let fetchMaxLogNumberQuery = `SELECT MAX(ROW_ID) as maxNumber FROM PERMIT_STAKEHOLDER WHERE PERMIT_NO = '${permitNo}'`;
    let maxLogNumberResult: any = await this.unviredSDK.dbExecuteStatement(
      fetchMaxLogNumberQuery
    );
    if (
      maxLogNumberResult &&
      maxLogNumberResult.data.length > 0 &&
      maxLogNumberResult.data[0].maxNumber
    ) {
      maxNumber = Number(maxLogNumberResult.data[0].maxNumber) + 1;
    }
    return maxNumber.toString();
  }

  async regularSubmitPermit(permitHeader: any) {
    let isAvailableButton = permitHeader.hasOwnProperty('isShowDetailsButton');
    if (isAvailableButton) {
      delete permitHeader.isShowDetailsButton;
    }
    let isShowReviseButton = permitHeader.hasOwnProperty('isShowReviseButton');
    if (isShowReviseButton) {
      delete permitHeader.isShowReviseButton;
    }
    // Remove permitTypeInfo as it's only used for display purposes and should not be sent to server
    if (permitHeader.hasOwnProperty('permitTypeInfo')) {
      delete permitHeader.permitTypeInfo;
    }

    let permitHeaderResponse: any = await this.dataService.modifyPermit(
      permitHeader
    );
    let infoMsg = this.dataService.handleInfoMessage(permitHeaderResponse);
    if (permitHeaderResponse.type == ResultType.success) {
      if (infoMsg && infoMsg?.length > 0) {
        await this.showAlert('Info', infoMsg);
      } else {
        this.ngZone.run(async () => {
          await this.fetchStatusBasedPermits(this.segmentValue);
        });
      }
    } else {
      if (
        permitHeaderResponse.message &&
        permitHeaderResponse.message.length > 0
      ) {
        await this.showAlert(
          this.translate.instant('Error'),
          permitHeaderResponse.message
        );
      } else if (
        permitHeaderResponse.error &&
        permitHeaderResponse.error.length > 0
      ) {
        await this.showAlert(
          this.translate.instant('Info'),
          permitHeaderResponse.error
        );
      } else {
        await this.showAlert(
          this.translate.instant('Error'),
          this.translate.instant(
            'Error occured while updating permit, Please try again'
          )
        );
      }
    }
  }

  async showErrorMessage(permit: PERMIT_HEADER) {
    let that = this;
    let result: any = '';
    if (permit.SYNC_STATUS == 3) {
      result = await that.unviredSDK.getInfoMessages('PERMIT', permit.LID);
      if (result.type == ResultType.success) {
        let title = that.translate.instant('Info Message');

        if (that.devicePlatform == 'android') {
          if (result.message != '' && result.message != '') {
            that.showAlert(title, result.message);
          } else {
            if (result.data.length > 0) {
              let message = '';
              for (var i = 0; i < result.data.length; i++) {
                message = message + result.data[i].MESSAGE + '<br>';
              }
              that.showAlert(title, message);
            } else {
              that.showAlert(
                title,
                that.translate.instant('Error while submitting permit')
              );
            }
          }
        } else if (that.devicePlatform == 'ios') {
          let message = '';
          for (var i = 0; i < result.data.length; i++) {
            message = message + result.data[i].MESSAGE + '<br>';
          }
          that.showAlert(title, message);
        } else {
          if (result.message !== null && result.message !== '') {
            let message = '';
            message = `${message}${result.message}`;
            that.showAlert(title, message.trim());
          } else {
            let resultData = result.data;
            if (resultData !== null && resultData.length > 0) {
              let messageArray: string[] = resultData.map((data: any) => {
                if (data.MESSAGE && data.MESSAGE.trim().length !== 0) {
                  return data.MESSAGE;
                }
              });
              if (messageArray.length > 0) {
                let messageToDisplay: string = messageArray.join(' ').trim();
                that.showAlert(title, messageToDisplay);
              }
            }
          }
        }
      } else {
        that.showAlert(that.translate.instant('Error'), JSON.stringify(result));
      }
    }
  }

  getDetailsButtonNameBasedOnStatus(status: any) {
    let title = '';
    switch (status.trim()) {
      case PermitStatus.IN_REVIEW:
        title = 'Approve';
        break;

      case PermitStatus.APPROVED:
        title = 'Issue';
        break;

      case PermitStatus.ISSUED:
        title = 'Close';
        break;
    }
    return title;
  }

  getButtonNameBasedOnStatus(status: any) {
    let title = '';
    switch (status.trim()) {
      case PermitStatus.OPEN:
        title = this.translate.instant('Submit for Review');
        break;
    }
    return title;
  }

  async addPermitUserAssignPopup(permit: PERMIT_HEADER) {
    const modal = await this.modalController.create({
      component: AddPermitRoleComponent,
      componentProps: {
        permit: permit,
      },
    });
    modal.present();

    const { data } = await modal.onWillDismiss();
    return data;
  }

  async findMaxLogNumberFromDb(PERMIT_NO: any) {
    let maxNumber = 1;
    let fetchMaxLogNumberQuery = `SELECT MAX(LOG_NO) as maxNumber FROM PERMIT_LOG WHERE PERMIT_NO = '${PERMIT_NO}'`;
    let maxLogNumberResult: any = await this.unviredSDK.dbExecuteStatement(
      fetchMaxLogNumberQuery
    );
    if (
      maxLogNumberResult &&
      maxLogNumberResult.data.length > 0 &&
      maxLogNumberResult.data[0].maxNumber
    ) {
      maxNumber = maxLogNumberResult.data[0].maxNumber + 1;
    }
    return maxNumber;
  }

  async createNewPermit() {
    const modal = await this.modalController.create({
      component: CreatePermitComponent,
      cssClass: 'full-screen-modal',
    });
    modal.present();

    const { data } = await modal.onWillDismiss();
    if (data) {
      await this.fetchStatusBasedPermits(this.segmentValue);
    }
  }

  async cancelPermit(permit: PERMIT_HEADER) {
    const alert = await this.alertController.create({
      cssClass: 'my-custom-class',
      header: 'Are sure you want to cancel the permit?',
      backdropDismiss: false,
      inputs: [
        {
          type: 'textarea',
          placeholder: 'Enter Reason',
          name: 'reason',
        },
      ],
      buttons: [
        {
          text: 'No',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => { },
        },
        {
          text: 'Yes',
          handler: async (alertData) => {
            let inspectionUpdateQuery = `UPDATE PERMIT_HEADER SET P_MODE = 'M', OBJECT_STATUS = 2 WHERE PERMIT_NO = '${permit.PERMIT_NO}'`;
            let inspectionUpdateResult =
              await this.unviredSDK.dbExecuteStatement(inspectionUpdateQuery);
            if (inspectionUpdateResult.type == ResultType.success) {
              permit.P_MODE = 'M';
              permit.OBJECT_STATUS = 2;
            }

            let user = '';
            let userContext: any = localStorage.getItem('userContext');
            if (userContext) {
              userContext = JSON.parse(userContext);
            }

            if (userContext?.USER_CONTEXT_HEADER) {
              user =
                userContext?.USER_CONTEXT_HEADER?.FIRST_NAME +
                ' ' +
                userContext?.USER_CONTEXT_HEADER?.LAST_NAME;
            }
            let comment = `${user} cancelled permit on ${this.getCurrentTime()} with a reason ${alertData.reason
              }`;

            let permitLog = new PERMIT_LOG();
            permitLog.LOG_NO = await this.findMaxLogNumberFromDb(
              permit.PERMIT_NO
            );

            permitLog.LID = this.unviredSDK.guid().replace(/-/g, '');
            permitLog.SYNC_STATUS = 0;
            permitLog.OBJECT_STATUS = 1;
            permitLog.CREATED_ON = new Date().getTime();
            permitLog.P_MODE = 'A';
            permitLog.PERMIT_NO = permit.PERMIT_NO;
            permitLog.FID = permit.LID;
            permitLog.PERMIT_STATUS = 'CANCELLED';
            permitLog.COMMENT = comment;
            permitLog.CREATED_BY = user;
            await this.unviredSDK.dbInsert('PERMIT_LOG', permitLog, false);

            await this.regularSubmitPermit(permit);
          },
        },
      ],
    });

    await alert.present();
  }

  getCurrentTime() {
    const currentDate = new Date();
    const options: any = {
      day: '2-digit',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    };
    return currentDate.toLocaleString('en-US', options);
  }

  async showAlert(title: string, message: string) {
    const alert = await this.alertController.create({
      header: title,
      message: message,
      mode: 'ios',
      buttons: [this.translate.instant('OK')],
    });
    await alert.present();
  }

  async findAdminUser() {
    let findAdminUserQuery = `SELECT * FROM USER_ROLE WHERE USER_MGMT = 'true'`;
    let findAdminUserResult = await this.unviredSDK.dbExecuteStatement(
      findAdminUserQuery
    );
    if (findAdminUserResult && findAdminUserResult.data.length > 0) {
      return true;
    } else return false;
  }

  goToSettings() {
    this.route.navigate(['/settings']);
  }

  async updateSelectedFacility(facility: FACILITY_HEADER) {
    this.userObject.CURRENT_FACILITY = facility.FACILITY_ID;
    this.userObject.CURRENT_FACILITY_DESC = facility.NAME;
    this.userObject.OBJECT_STATUS = 2;
    await this.unviredSDK.dbInsertOrUpdate(
      'USER_CONTEXT_HEADER',
      this.userObject,
      true
    );
    await this.dataService.modifyUserContext(
      this.userObject,
      true
    );
    let userContextResult = await this.dataService.getData(
      'USER_CONTEXT_HEADER'
    );
    this.userObject = userContextResult[0];
  }

  async loadMore() {
    // Set loading state instead of showing loader
    this.isLoading = true;
    let permitResponse: any = await this.dataService.downloadPermits(
      AppConstants.PERMITS_LIMIT,
      this.permitFilteredList.length,
      true
    );
    // Clear loading state
    this.isLoading = false;
    if (permitResponse.type == ResultType.success) {
      if (permitResponse?.data?.InfoMessage?.length > 0) {
        this.isShowLoadMoreButton = false;
      } else {
        this.isShowLoadMoreButton =
          permitResponse?.data?.PERMIT?.length == AppConstants.PERMITS_LIMIT ? true : false;
        await this.fetchPermitsWithMultipleStatuses();
      }
    }
  }

  isPermitIsExpired(permit: any) {
    permit.IS_EXTENDED = (permit.IS_EXTENDED == true ||  permit.IS_EXTENDED == 'true') ? 'true' : 'false';
    if ((permit.IS_EXTENDED == true || permit.IS_EXTENDED == 'true') && this.isExpired(permit.EXTENSION_DATE)) {
      return true
      // permit has expired
    } else if ((permit.IS_EXTENDED == false || permit.IS_EXTENDED == 'false') && this.isExpired(permit.EXPIRY_DATE)) {
      return true
      // permit has expired
    } else {
      return false
      // permit is valid
    }
  }

  isExpired(expiryDate: any): boolean {
    const expiry = new Date(expiryDate);
    const now = new Date();
    return now > expiry;
  }

  async getFormTempaltes() {
    let getFormsResp: any = await this.dataService.getFormTemplates();
    if (getFormsResp.type == ResultType.success) {

    } else {
      console.log("error response on get templates")
      return { type: ResultType.error, error: getFormsResp.message };
    }
  }

  /**
   * Get permit type information including icon and color
   * @param permitType The permit type code
   * @returns The permit type information
   */
  async getPermitTypeInfo(permitType: string): Promise<any> {
    if (!permitType) return null;

    try {
      const query = `SELECT * FROM PERMIT_TYPE_HEADER WHERE PERMIT_TYPE = '${permitType}'`;
      const result = await this.unviredSDK.dbExecuteStatement(query);

      if (result.type == ResultType.success && result.data && result.data.length > 0) {
        return result.data[0];
      }
      return null;
    } catch (error) {
      console.error('Error fetching permit type info:', error);
      return null;
    }
  }

  /**
   * Convert color number to hex color string
   * @param colorNum The color number
   * @returns The hex color string
   */
  getColorHex(colorNum: any): string {
    // Default color (blue)
    if (colorNum === undefined || colorNum === null) {
      return '#3880ff';
    }

    // If it's already a string that starts with #, return it
    if (typeof colorNum === 'string' && colorNum.startsWith('#')) {
      return colorNum;
    }

    // Convert number to hex
    let hexString = '';
    if (typeof colorNum === 'number') {
      hexString = colorNum.toString(16);
    } else if (typeof colorNum === 'string') {
      const num = parseInt(colorNum, 10);
      if (!isNaN(num)) {
        hexString = num.toString(16);
      }
    }

    // Ensure it has 6 digits
    hexString = hexString.padStart(6, '0');
    return '#' + hexString;
  }

  /**
   * Handle search input
   * @param event The search input event
   */
  onSearchInput(event: any) {
    this.searchTerm = event.target.value || '';
    this.setFilteredItems();
  }

  /**
   * Handle search clear
   */
  onSearchClear() {
    this.searchTerm = '';
    this.setFilteredItems();
  }

  // Scroll-based search bar collapse variables
  isSearchBarHidden = false;
  lastScrollTop = 0;
  scrollThreshold = 50; // Minimum scroll distance to trigger hide/show

  /**
   * Handle scroll events to show/hide search bar
   * @param event Scroll event
   */
  onScroll(event: any) {
    const scrollTop = event.target.scrollTop;
    const scrollDelta = scrollTop - this.lastScrollTop;

    // Only trigger if scroll distance is significant enough
    if (Math.abs(scrollDelta) > this.scrollThreshold) {
      if (scrollDelta > 0 && scrollTop > 100) {
        // Scrolling down and not at the top - hide search bar
        this.isSearchBarHidden = true;
      } else if (scrollDelta < 0) {
        // Scrolling up - show search bar
        this.isSearchBarHidden = false;
      }

      this.lastScrollTop = scrollTop;
    }
  }

  /**
   * Reset search bar visibility (show it)
   */
  resetSearchBarVisibility() {
    this.isSearchBarHidden = false;
    this.lastScrollTop = 0;
  }

  /**
   * Check if permit has any available actions
   * @param permit The permit object
   * @returns True if permit has available actions
   */
  hasAvailableActions(permit: any): boolean {
    return this.shouldShowSubmitButton(permit) ||
           this.shouldShowApproveButton(permit) ||
           this.shouldShowIssueButton(permit) ||
           this.shouldShowReviseButton(permit) ||
           this.shouldShowCloseButton(permit) ||
           this.shouldShowCancelButton(permit) ||
           permit.SYNC_STATUS !== 0; // Show section for sync status indicators
  }

  /**
   * Check if Submit for Review button should be shown
   * @param permit The permit object
   * @returns True if button should be shown
   */
  shouldShowSubmitButton(permit: any): boolean {
    return permit.STATUS === 'OPEN' &&
           permit.SYNC_STATUS === 0 &&
           !permit.isShowDetailsButton &&
           !this.isPermitIsExpired(permit);
  }

  /**
   * Check if Approve button should be shown
   * @param permit The permit object
   * @returns True if button should be shown
   */
  shouldShowApproveButton(permit: any): boolean {
    return permit.STATUS === 'IN_REVIEW' &&
           permit.SYNC_STATUS === 0 &&
           !permit.isShowDetailsButton &&
           !this.isPermitIsExpired(permit);
  }

  /**
   * Check if Issue button should be shown
   * @param permit The permit object
   * @returns True if button should be shown
   */
  shouldShowIssueButton(permit: any): boolean {
    return permit.STATUS === 'APPROVED' &&
           permit.SYNC_STATUS === 0 &&
           !permit.isShowDetailsButton &&
           !this.isPermitIsExpired(permit);
  }

  /**
   * Check if Revise button should be shown
   * @param permit The permit object
   * @returns True if button should be shown
   */
  shouldShowReviseButton(permit: any): boolean {
    return permit.STATUS === 'ISSUED' &&
           permit.SYNC_STATUS === 0 &&
           permit.isShowReviseButton &&
           !this.isPermitIsExpired(permit);
  }

  /**
   * Check if Close button should be shown
   * @param permit The permit object
   * @returns True if button should be shown
   */
  shouldShowCloseButton(permit: any): boolean {
    return permit.STATUS === 'ISSUED' &&
           permit.SYNC_STATUS === 0 &&
           !permit.isShowDetailsButton &&
           !permit.isShowReviseButton &&
           !this.isPermitIsExpired(permit);
  }

  /**
   * Check if Cancel button should be shown
   * @param permit The permit object
   * @returns True if button should be shown
   */
  shouldShowCancelButton(permit: any): boolean {
    return (permit.SYNC_STATUS === 0 || permit.SYNC_STATUS === 3) &&
           permit.STATUS !== 'CANCELLED' &&
           permit.STATUS !== 'CLOSED' &&
           this.isAdmin;
  }

  /**
   * Track by function for shimmer items to improve performance
   * @param index The index of the item
   * @param item The item (not used)
   * @returns The index for tracking
   */
  trackByIndex(index: number, item: any): number {
    return index;
  }
}
