ion-searchbar {
  margin: 0 !important;
}

.progressSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.iconWithinButton {
  margin: 7% 0 0 213% !important;
}

.fa-cog {
  color: #3880ff;
}

.orange {
  --progress-background: orange;
  color: #f59e0b;
  border: 1px solid #f59e0b;
}

.light-Green {
  --progress-background: rgb(105, 226, 105);
  color: #10b981;
  border: 1px solid #10b981;
}

.darak-Green {
  --progress-background: rgb(13, 172, 13);
  color: #047857;
  border: 1px solid #047857;
}

.light-Grey {
  --progress-background: indianred;
  color: #ef4444;
  border: 1px solid #ef4444;
}

ion-toolbar {
  padding-inline: 10px;
  padding-top: 0px;
  padding-bottom: 0px;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 0.0125em;

}

.refreshButton {
  // --border-color: #868686 !important;
  --border-width: 1px !important;
  --border-radius: 8px !important;
  font-weight: 600 !important;
}

.expired {
  cursor: pointer;
  --background: #ebd3e173;
  border: 1px solid gainsboro;
  border-radius: 8px;
 }

 .not-expired {
   cursor: pointer;
   border-radius: 8px;
   border: 1px solid gainsboro;
 }

 .orange-border {
  border-left: 3px solid orange;
}

.light-Green-border {
  border-left: 3px solid rgb(105, 226, 105);
}

.darak-Green-border {
  border-left: 3px solid rgb(13, 172, 13);
}

.light-Grey-border {
  border-left: 3px solid indianred;
}


ion-card {
  cursor: pointer;
  // border: 1px solid grey;
  border-radius: 10px;
  box-shadow: none;
  border: 1px solid #dee3ea;
}

.date-picker-modal {
  --height: auto;
  --width: 300px;
}

.date-container {
  display: flex;
  align-items: center;
}

.date-controls {
  display: flex;
  align-items: center;
  gap: 5px;
}

.date-label {
  color: white;
  margin: 0;
  padding: 5px;
  display: flex;
  align-items: center;
  height: 100%;
}

ion-button {
  --padding-start: 8px;
  --padding-end: 8px;
  margin: 0;
}

.header {
  text-align: center;
  padding: 20px;
  background-color: #f8f9fa;
}

.header h1 {
  font-size: 24px;
  margin-bottom: 10px;
}

.header h2 {
  font-size: 20px;
  margin-bottom: 20px;
}

.stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
}

.stat {
  text-align: center;
}

.stat p {
  font-size: 18px;
  font-weight: bold;
}

.stat span {
  font-size: 14px;
  color: #6c757d;
}

.search-bar {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background-color: #f8f9fa;
}

.search-bar ion-searchbar {
  flex: 1;
  margin-right: 10px;
}

ion-list {
  padding: 10px;
}

ion-item {
  --background: #ffffff;
  margin-bottom: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

ion-item ion-label {
  font-size: 16px;
}

ion-item ion-button {
  margin-left: 10px;
}

.header-section {
    background: white;
  color: #333;
  padding: 2rem;
  border-radius: 0 0 20px 20px;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.content-overlay {
  position: relative;
  z-index: 2;
  padding: 0 15px;
}

.greeting-container {
  text-align: center;
  margin-bottom: 2rem;
  h1 {
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0.5rem 0;
    color: #333;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  }
  h2 {
    font-size: 20px;
    font-weight: 400;
    margin-top: 0;
    color: #555;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  }
  h3 {
    font-size: 1.1rem;
    font-weight: 500;
    margin: 0;
    color: #555;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  margin-bottom: 10px;
}

.stat-card {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 15px;
  text-align: center;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 5px;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
}

.search-section {
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.search-container {
  display: flex;
  flex: 1;
  align-items: center;
  margin-right: 10px;
}

.search-container ion-searchbar {
  --background: #f0f0f0;
  --border-radius: 10px;
  --box-shadow: none;
  --placeholder-color: #888;
  --icon-color: #6366f1;
  --placeholder-font-weight: 400;
  --placeholder-font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  margin: 0;
  padding: 0;
}

.search-card {
  margin: 15px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 10px 15px;
  width: auto; /* Ensure consistent width with map container */
}

.search-filter-row {
  display: flex;
  align-items: center;
  gap: 10px;
  overflow-x: auto;
  scrollbar-width: none; /* For Firefox */
  -ms-overflow-style: none; /* For Internet Explorer and Edge */
}

.search-filter-row::-webkit-scrollbar {
  display: none; /* For Chrome, Safari, and Opera */
}

.search-wrapper {
  display: flex;
  align-items: center;
  background-color: #e2e8f0;
  border-radius: 10px;
  padding: 5px 10px;
  width: 130px;
  min-width: 130px;
  flex-shrink: 0;
}

.search-wrapper ion-icon {
  color: #94a3b8;
  font-size: 16px;
  margin-right: 5px;
}

.search-wrapper input {
  border: none;
  outline: none;
  background: transparent;
  font-size: 13px;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  color: #334155;
  width: 100%;
}

.filter-badges-row {
  display: flex;
  gap: 8px;
  align-items: center;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  flex: 1;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding-bottom: 4px; /* Add space for scrollbar */
  scrollbar-width: thin;
}

.filter-badges-row::-webkit-scrollbar {
  display: none;
}

.filter-badge {
  display: flex;
  align-items: center;
  background-color: #f1f5f9;
  border-radius: 20px;
  padding: 0;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.2s ease;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  flex-shrink: 0;
  height: 28px;
  line-height: 28px; /* Match height for vertical centering */
  border: 1px solid #cbd5e1;  // Adding border with same color as count circle
  min-width: initial;
}

.filter-badge:hover {
  background-color: #e2e8f0;
}

.filter-badge.selected {
  border: none;  // Remove border when selected
  background-color: #24348B;
}

.filter-badge.selected[class*="OPEN"],
.filter-badge.selected[class*="IN_REVIEW"] {
  background-color: #f59e0b;
}

.filter-badge.selected[class*="APPROVED"] {
  background-color: #10b981;
}

.filter-badge.selected[class*="ISSUED"],
.filter-badge.selected[class*="CLOSED"] {
  background-color: #047857;
}

.filter-badge.selected[class*="CANCELLED"],
.filter-badge.selected[class*="REJECTED"] {
  background-color: #ef4444;
}

.filter-badge.selected[class*="EXPIRED"] {
  background-color: #9333ea;
}

.filter-badge.selected[class*="Assigned"] {
  background-color: #24348B;
}

// Update the badge text color when selected to white for all selected badges
.filter-badge.selected .badge-text {
  color: white;
}

// Update badge count styling when selected
.filter-badge.selected .badge-count {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.filter-badge.multi-selected {
  background-color: rgba(36, 52, 139, 0.2);
  border: 1px solid #24348B; // Keep existing border for multi-selected state
}

.filter-badge.multi-selected .badge-text {
  color: #24348B;
}

.filter-badge.multi-selected .badge-count {
  background-color: rgba(36, 52, 139, 0.2);
  color: #24348B;
}

.badge-text {
  padding: 0 8px;
  font-size: 12px;
  font-weight: 500;
  color: #475569;
  white-space: nowrap;
  line-height: 28px; /* Match the height of the badge for vertical centering */
  display: flex;
  align-items: center;
}

.filter-badge.selected .badge-text {
  color: white;
}

.badge-count {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #cbd5e1;
  color: #1e293b;
  font-size: 11px;
  font-weight: 600;
  height: 100%;
  min-width: 28px; /* Minimum width for single digit */
  width: auto; /* Allow width to grow based on content */
  border-radius: 14px; /* Half of the height for pill shape */
  margin-left: -4px;
  padding: 0 6px; /* Add horizontal padding for multi-digit numbers */

  .odometer {
    height: 100%;
    line-height: 28px; /* Match the height of the badge for vertical centering */
    font-size: inherit;
    font-weight: inherit;
    color: inherit;
    margin: 0;
    padding: 0;
    min-width: 16px; /* Ensure minimum width for odometer */
    display: flex;
    align-items: center;
    justify-content: center;

    .odometer-inside {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }

    .odometer-digit {
      height: 100%;
      line-height: inherit;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// Override odometer theme defaults
.odometer.odometer-theme-minimal {
  .odometer-digit {
    color: inherit;
    width: auto; /* Allow digits to take their natural width */
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .odometer-digit .odometer-digit-inner {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .odometer-value {
    width: auto; /* Allow values to take their natural width */
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .odometer-digit-spacer {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .odometer-ribbon, .odometer-ribbon-inner {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
}

.filter-badge.selected .badge-count {
  background-color: #1e2b73;
  color: white;
}

.add-btn {
  display: flex;
  align-items: center;
  background-color: #24348B;
  border: none;
  border-radius: 8px;
  padding: 0 12px;
  height: 28px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(36, 52, 139, 0.3);
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  white-space: nowrap;
  flex-shrink: 0;
}

.add-btn:hover {
  background-color: #1e2b73;
}

.add-btn ion-icon {
  margin-right: 4px;
  font-size: 14px;
}

.search-filter-container {
  display: flex;
  flex: 1;
  align-items: center;
  gap: 10px;
}

.filter-buttons {
  display: flex;
  gap: 10px;
}

.filter-button, .sort-button {
  --color: #24348B;
  font-size: 13px;
  font-weight: 500;
  height: 36px;
}

.add-button ion-button {
  --background: #24348B;
  --border-radius: 10px;
  --box-shadow: 0 4px 10px rgba(36, 52, 139, 0.3);
  font-weight: 600;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  font-size: 14px;
  height: 36px;
}

.permits-list {
  padding: 0 15px 15px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.permit-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 15px;
  margin-bottom: 15px;
  display: flex;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
}

.status-column {
  display: flex;
  align-items: center;
  margin-right: 15px;
  min-width: 100px;
}

.status-badge {
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  background-color: transparent;
  text-align: center;
  letter-spacing: 0.3px;
  display: inline-block;
  color: white;
}

.status-badge.outline-badge {
  background-color: transparent;
}

.orange.outline-badge {
  color: #f59e0b;
  border: 1px solid #f59e0b;
}

.light-Green.outline-badge {
  color: #10b981;
  border: 1px solid #10b981;
}

.darak-Green.outline-badge {
  color: #047857;
  border: 1px solid #047857;
}

.light-Grey.outline-badge {
  color: #ef4444;
  border: 1px solid #ef4444;
}

.permit-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.permit-row {
  display: flex;
  align-items: center;
}

.info-label {
  width: 120px;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
}

.permit-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.permit-actions ion-button {
  margin: 5px;
}

.expired-date {
  color: #ef4444;
}

ion-header {
  display: none;
}

/* Background styling for the page */
:host {
  background: linear-gradient(to bottom, rgb(245, 246, 248) 0%, rgb(255, 255, 255) 100%);
  display: block;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden; /* Prevent horizontal scrolling */
  padding: 10px;
  width: 100%;
}

/* Table styling */
.permits-table {
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: 0 15px 20px; /* Increased right and left margin */
  width: calc(100% - 30px); /* Adjust width to account for margins */
  table-layout: fixed;
  position: relative;
  box-sizing: border-box;
}

/* Fix for overflow issue */
.permits-table-wrapper {
  overflow-x: auto;
  overflow-y: visible;
  padding: 0;
  margin: 0; /* Reset margins */
  box-sizing: border-box;
  width: 100%;
}

.table-header {
  display: flex;
  background-color: #f1f5f9;
  border-radius: 8px 8px 0 0;
  padding: 10px 15px;
  font-weight: 600;
  color: #475569;
  font-size: 13px;
  align-items: center;
  height: 40px;
  position: sticky;
  top: 0;
  z-index: 10;
  margin: 0;
  border-radius: 0;
}

.table-row {
  display: flex;
  background-color: white;
  border-radius: 2px;
  padding: 8px 15px;
  margin-bottom: 8px;
  box-shadow: 0 2px 0px rgba(0, 0, 0, 0.05);
  align-items: center;
  transition: all 0.2s ease;
  height: 46px;
  cursor: pointer;
  box-sizing: border-box !important; /* Include borders in height calculation */
  border-left-width: 4px; /* Consistent border width */
  transform: translateZ(0); /* Force hardware acceleration */
  backface-visibility: hidden; /* Prevent flickering */
  position: relative; /* Establish positioning context */
  border-left: none;
}

.table-row:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  transform: none; /* Remove the transform that causes movement */
  border-left-width: 4px !important; /* Keep border width consistent on hover */
}

.header-cell,
.table-cell {
  padding: 0 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Column-specific widths */
.status-cell {
  width: 200px;
  min-width: 200px;
  flex: 0 0 200px;
}

.permit-no-cell {
  width: 120px;
  min-width: 120px;
  flex: 0 0 120px;
}

.desc-cell {
  width: 120px;
  min-width: 120px;
  flex: 0 0 120px;
}

.type-cell {
  width: 140px;
  min-width: 140px;
  flex: 0 0 140px;
}

.date-cell {
  width: 160px;
  min-width: 160px;
  flex: 0 0 160px;
}

.requester-cell {
  width: 160px;
  min-width: 160px;
  flex: 0 0 160px;
}

.actions-cell {
  width: 80px;
  min-width: 80px;
  flex: 0 0 80px;
  display: flex;
  justify-content: center;
}

.status-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-badge {
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  text-align: center;
  letter-spacing: 0.3px;
  display: inline-block;
}

.expired-badge {
  background-color: rgba(239, 68, 68, 0.2);
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  color: #ef4444;
  text-align: center;
  letter-spacing: 0.3px;
  display: inline-block;
}

.table-cell {
  font-weight: 500;
  font-size: 13px;
}

.expired-date {
  color: #ef4444;
}

/* Media queries for responsive design */
@media (max-width: 1200px) {
  .permits-table {
    /* Remove overflow-x from table itself */
    margin: 0 15px 20px; /* Keep consistent margin */
  }

  .permits-table-wrapper {
    overflow-x: auto; /* Handle overflow in wrapper instead */
  }

  .table-header, .table-row {
    min-width: 1050px;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .search-section {
    flex-direction: column;
  }

  .search-filter-container {
    flex-direction: column;
    width: 100%;
  }

  .filter-buttons {
    width: 100%;
    justify-content: space-between;
  }

  .add-button {
    width: 100%;
    margin-top: 10px;
  }

  .table-header {
    display: none;
  }

  .table-row {
    flex-direction: column;
    height: auto;
    min-width: auto;
    align-items: flex-start;
    padding: 15px;
  }

  .table-cell {
    width: 100% !important;
    min-width: auto !important;
    flex: none !important;
    padding: 5px 0;
    white-space: normal;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .table-cell:before {
    content: attr(data-label);
    font-weight: 600;
    width: 40%;
    color: #64748b;
  }

  .status-cell {
    margin-bottom: 5px;
  }

  .actions-cell {
    justify-content: flex-start;
  }
}

/* Apply specific widths to control table layout */
.table-header .header-cell:nth-child(2),
.table-row .table-cell:nth-child(2) {
  width: 120px;
  flex: 0 0 120px;
}

.table-header .header-cell:nth-child(3),
.table-row .table-cell:nth-child(3) {
  flex: 2;
}

.table-header .header-cell:nth-child(4),
.table-row .table-cell:nth-child(4) {
  width: 140px;
  flex: 0 0 140px;
}

.table-header .header-cell:nth-child(5),
.table-row .table-cell:nth-child(5),
.table-header .header-cell:nth-child(6),
.table-row .table-cell:nth-child(6) {
  width: 150px;
  flex: 0 0 150px;
}

.table-header .header-cell:nth-child(7),
.table-row .table-cell:nth-child(7) {
  width: 140px;
  flex: 0 0 140px;
}

/* Media queries for responsive design */
@media (max-width: 1024px) {
  .table-header .header-cell:nth-child(4),
  .table-row .table-cell:nth-child(4),
  .table-header .header-cell:nth-child(7),
  .table-row .table-cell:nth-child(7) {
    display: none;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .search-section {
    flex-direction: column;
  }

  .search-container {
    width: 100%;
    margin-bottom: 10px;
  }

  .add-button {
    width: 100%;
  }

  .permit-details {
    grid-template-columns: 1fr;
  }

  .permit-card {
    flex-direction: column;
  }

  .status-column {
    margin-bottom: 15px;
    margin-right: 0;
  }

  .table-header {
    display: none;
  }

  .table-row {
    flex-direction: column;
    align-items: flex-start;
    padding: 15px;
  }

  .table-cell {
    width: 100% !important;
    flex: none !important;
    padding: 5px 0;
    white-space: normal;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .table-cell:before {
    content: attr(data-label);
    font-weight: 600;
    width: 40%;
    color: #64748b;
  }

  .status-cell, .actions-cell {
    width: 100%;
    justify-content: flex-start;
  }
}

// ...existing styles...

/* Search card styling to match reference */
.search-card {
  margin: 15px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 12px 20px;
}

.search-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

.search-wrapper {
  display: flex;
  align-items: center;
  background-color: #f8fafc;
  border-radius: 10px;
  padding: 6px 12px;
  width: 160px;
}

.search-wrapper ion-icon {
  color: #94a3b8;
  font-size: 16px;
  margin-right: 6px;
}

.search-wrapper input {
  border: none;
  outline: none;
  background: transparent;
  font-size: 12px;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  color: #334155;
  width: 100%;
}

.search-wrapper input::placeholder {
  color: #94a3b8;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* View toggle buttons */
.view-toggle-buttons {
  display: flex;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
  margin-right: 5px;
}

.view-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px 10px;
  background-color: #f8fafc;
  border: none;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  color: #64748b;
  transition: all 0.2s ease;
  gap: 4px;
}

.view-toggle-btn:first-child {
  border-right: 1px solid #e2e8f0;
}

.view-toggle-btn.active {
  background-color: #24348B;
  color: white;
}

.view-toggle-btn ion-icon {
  font-size: 14px;
}

/* Map view container */
.map-view-container {
  width: auto;
  min-height: 500px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin: 0 15px 15px 15px; /* Match the search card's horizontal margins */
}

.filter-btn, .sort-btn {
  display: flex;
  align-items: center;
  background-color: #f8fafc;
  border: none;
  border-radius: 8px;
  padding: 6px 10px;
  font-size: 12px;
  font-weight: 500;
  color: #24348B;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  white-space: nowrap;
}

.filter-btn:hover, .sort-btn:hover {
  background-color: #f1f5f9;
}

.filter-btn ion-icon, .sort-btn ion-icon {
  margin-right: 4px;
  font-size: 14px;
}

.add-btn {
  display: flex;
  align-items: center;
  background-color: #24348B;
  border: none;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 13px;
  font-weight: 500;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 10px rgba(36, 52, 139, 0.3);
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  white-space: nowrap;
}

.add-btn:hover {
  background-color: #1e2b73;
  box-shadow: 0 6px 15px rgba(36, 52, 139, 0.4);
}

.add-btn ion-icon {
  margin-right: 4px;
  font-size: 14px;
}

/* Update responsive styles for new search UI */
@media (max-width: 768px) {
  .search-row {
    flex-direction: column;
    align-items: stretch;
  }

  .search-wrapper {
    width: 100%;
    margin-bottom: 12px;
  }

  .action-buttons {
    flex-wrap: wrap;
  }

  .filter-btn, .sort-btn, .add-btn {
    flex: 1;
    justify-content: center;
    min-width: 120px;
  }
}

// ...rest of existing styles...

/* Filter modal styles */
.filter-modal-content {
  padding: 20px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.filter-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e2e8f0;
}

.filter-badge {
  display: flex;
  align-items: center;
  background-color: #f1f5f9;
  border-radius: 30px;
  padding: 0;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.2s ease;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  border: 1px solid #cbd5e1;  // Adding border with same color as count circle
}

.filter-badge:hover {
  background-color: #e8ebf0;
}

.filter-badge.selected {
  border: none;  // Remove border when selected
  background-color: #6366f1;
}

.badge-text {
  padding: 8px 16px 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #475569;
}

.filter-badge.selected .badge-text {
  color: white;
}

.badge-count {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #cbd5e1;
  color: #1e293b;
  font-size: 14px;
  font-weight: 600;
  height: 100%;
  padding: 8px 12px;
  border-radius: 30px;
  margin-left: -6px;
}

.filter-badge.selected .badge-count {
  background-color: #4f46e5;
  color: white;
}

.date-filter {
  display: flex;
  align-items: center;
  background-color: #f1f5f9;
  border-radius: 20px;
  padding: 0 10px;
  height: 28px;
  flex-shrink: 0;
}

.date-filter ion-icon {
  color: #94a3b8;
  font-size: 16px;
  margin-right: 5px;
}

.date-filter input {
  border: none;
  outline: none;
  background: transparent;
  font-size: 12px;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  color: #334155;
  width: 120px;
}

.permit-stats {
  padding: 16px;
  background: #ffffff;
  margin: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

ion-content {
  --background: linear-gradient(135deg, #24348B 0%, #4a5dc7 100%);
}

.dashboard-header {
  background: var(--ion-color-primary);
  padding: 20px;
  border-radius: 0 0 24px 24px;
  margin-bottom: 20px;
}

.dashboard-header {
  background: #24348B;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;

  .stacked-bar-container {
    h2 {
      color: white;
      margin-bottom: 15px;
      font-size: 1.2rem;
    }
  }

  .legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 10px;

    .legend-item {
      display: flex;
      align-items: center;
      gap: 5px;

      .color-box {
        width: 15px;
        height: 15px;
        border-radius: 3px;

        &.open { background-color: #38A169; }
        &.in-review { background-color: #ECC94B; }
        &.approved { background-color: #4299E1; }
        &.issued { background-color: #9F7AEA; }
        &.closed { background-color: #E53E3E; }
      }

      .label {
        color: white;
        font-size: 0.9rem;
      }
    }
  }
}

/* Removed redundant ion-content style as it's now in .permits-content */

// ...existing code...

/* Add a method to your TS file for consistent row styling */
.stable-row-style {
  transition: background-color 0.2s ease-in-out;
  border-left-width: 4px;
  transform: none;
  backface-visibility: hidden;
  position: relative;
}

/* Stacked Bar Chart Styles */
.stacked-bar-container {
  padding: 10px 20px 15px;
  width: 100%;
  margin-top: 10px; /* Add margin to prevent content from being hidden */
  padding-top: 20px; /* Add enough padding to prevent content from being hidden */
}

.stacked-bar {
  display: flex;
  width: 100%;
  height: 80px; /* Increased height for 3 lines */
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.bar-segment {
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
  text-align: center;
  min-width: 60px;
  position: relative;
  overflow: hidden;
  padding: 5px;
}

/* Animation for bar segments */
.animate-in {
  animation: growWidth 1s ease-out forwards, fadeIn 0.5s ease-out forwards;
  transform-origin: left;
  opacity: 0;
}

@keyframes growWidth {
  from { transform: scaleX(0); }
  to { transform: scaleX(1); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.bar-segment:hover {
  filter: brightness(1.1);
  transform: translateY(-2px);
}

.segment-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  opacity: 0;
  animation: fadeIn 0.5s ease-out forwards;
  animation-delay: 0.8s;
}

.segment-icon {
  margin-bottom: 4px;
}

.segment-icon i {
  font-size: 18px;
  color: white;
}

.segment-label {
  font-size: 11px;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  margin-bottom: 4px;
  max-width: 100%;
}

.segment-value {
  font-size: 12px;
  font-weight: bold;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

/* Make the chart responsive */
@media (max-width: 768px) {
  .stacked-bar {
    height: 90px; /* Slightly taller on medium screens */
  }

  .segment-icon i {
    font-size: 16px;
  }

  .segment-label {
    font-size: 10px;
    max-width: 90%;
  }

  .segment-value {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .stacked-bar {
    height: 100px; /* Even taller on small screens */
  }

  .bar-segment {
    padding: 2px;
    min-width: 50px;
  }

  .segment-icon i {
    font-size: 14px;
  }

  .segment-label {
    font-size: 9px;
    max-width: 85%;
  }

  .segment-value {
    font-size: 10px;
  }
}

/* Welcome container styles */
.welcome-container {
  padding: 15px 20px 5px;
  width: 100%;
}

.welcome-container h2 {
  margin: 0;
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Removed redundant host styles */

/* Removed redundant scroll styles */

// ...existing code...

/* Removed redundant scroll fix styles */

/* Removed redundant permits-list styles */

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(204, 204, 204, 0.7);
  border-radius: 3px;
}

// ...existing code...

/* Empty state styling */
.empty-state-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  width: 100%;
  padding: 2rem;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin: 20px 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.empty-state-content {
  max-width: 500px;
}

.empty-state-icon {
  font-size: 80px;
  color: #3445a7;
  margin-bottom: 1rem;
  opacity: 0.8;
}

.empty-state-container h2 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.empty-state-container p {
  font-size: 16px;
  color: #666;
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.empty-state-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.empty-state-container {
  animation: fadeIn 0.5s ease-in-out;
}

// ...existing code...

/* Shimmer loading effect */
@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

.shimmer {
  background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
  background-size: 800px 104px;
  animation: shimmer 1.5s infinite linear;
  border-radius: 4px;
}

.shimmer-container {
  padding: 16px;
  width: 100%;
}

.shimmer-stacked-bar {
  width: 100%;
  height: 50px;
  margin-bottom: 20px;
  @extend .shimmer;
  border-radius: 8px;
}

.shimmer-search-card {
  width: 100%;
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.shimmer-search-input {
  height: 36px;
  width: 280px;
  margin-bottom: 16px;
  @extend .shimmer;
  border-radius: 20px;
}

.shimmer-filters {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
  overflow-x: auto;
}

.shimmer-filter {
  height: 28px;
  width: 100px;
  flex-shrink: 0;
  @extend .shimmer;
  border-radius: 14px;
}

.shimmer-button {
  height: 36px;
  width: 140px;
  @extend .shimmer;
  border-radius: 8px;
}

.shimmer-table {
  width: 100%;
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.shimmer-header {
  height: 40px;
  width: 100%;
  margin-bottom: 16px;
  @extend .shimmer;
  opacity: 0.7;
  border-radius: 8px;
}

.shimmer-row {
  height: 46px;
  width: 100%;
  margin-bottom: 12px;
  @extend .shimmer;
  border-radius: 8px;
}

// ...existing code...

/* Status badge styling improvements */
.status-badge {
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  letter-spacing: 0.3px;
  display: inline-block;
  color: white; /* Default white text for filled badges */
}

.status-badge.outline-badge {
  background-color: transparent;
}

.orange {
  --progress-background: orange;
  background-color: #f59e0b;
  color: white;
}

.orange.outline-badge {
  color: #f59e0b;
  border: 1px solid #f59e0b;
  background-color: transparent;
}

.light-Green {
  --progress-background: rgb(105, 226, 105);
  background-color: #10b981;
  color: white;
}

.light-Green.outline-badge {
  color: #10b981;
  border: 1px solid #10b981;
  background-color: transparent;
}

.darak-Green {
  --progress-background: rgb(13, 172, 13);
  background-color: #047857;
  color: white;
}

.darak-Green.outline-badge {
  color: #047857;
  border: 1px solid #047857;
  background-color: transparent;
}

.light-Grey {
  --progress-background: indianred;
  background-color: #ef4444;
  color: white;
}

.light-Grey.outline-badge {
  color: #ef4444;
  border: 1px solid #ef4444;
  background-color: transparent;
}

/* Filter badges status-specific coloring when selected */
.filter-badge.selected.OPEN,
.filter-badge.selected.IN_REVIEW {
  background-color: #f59e0b;
}

.filter-badge.selected.APPROVED {
  background-color: #10b981;
}

.filter-badge.selected.ISSUED,
.filter-badge.selected.CLOSED {
  background-color: #047857;
}

.filter-badge.selected.CANCELLED,
.filter-badge.selected.REJECTED {
  background-color: #ef4444;
}

.filter-badge.selected.EXPIRED {
  background-color: #9333ea;
}

.filter-badge.selected.EXTENDED {
  background-color: #0ea5e9; /* Sky blue color for extended permits */
}

/* Remove colored left border from table rows */
.table-row {
  border-left: none;
  /* Other existing styles remain */
}

/* Adjust filter badge row to fit in one row */
.filter-badges-row {
  flex-wrap: nowrap;
  overflow-x: auto;
  padding-bottom: 4px; /* Add space for scrollbar */
  scrollbar-width: thin;
}

/* Make filter badges more compact */
.filter-badge {
  padding: 0;
  height: 28px;
  min-width: initial;
}

.badge-text {
  padding: 0 8px;
  font-size: 12px;
}

.badge-count {
  min-width: 24px;
  width: auto;
  padding: 0 6px; /* Slightly more padding for better appearance */
  border-radius: 14px; /* Ensure consistent border-radius */
}

// ...existing code...

/* Date display with extension icon */
.date-with-icon {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Permit type with icon styling */
.permit-type-with-icon {
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.permit-type-with-icon i {
  font-size: 16px;
  min-width: 16px; /* Ensure consistent spacing */
  flex-shrink: 0;
  margin-right: 5px;
}

.extension-icon {
  color: #3445a7;
  font-size: 16px;
  cursor: pointer;
}

/* No results message styling */
.no-results-message {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
  width: 100%;
  background-color: #f9fafb;
  border-radius: 8px;
  margin-top: 10px;
}

.no-results-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.no-results-content ion-icon {
  font-size: 48px;
  color: #94a3b8;
  margin-bottom: 16px;
}

.no-results-content p {
  color: #64748b;
  font-size: 16px;
  margin-bottom: 16px;
}

.clear-filters-btn {
  background-color: #3445a7;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.clear-filters-btn:hover {
  background-color: #2c3a8c;
}