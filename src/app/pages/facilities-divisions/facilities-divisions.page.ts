import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  <PERSON><PERSON><PERSON><PERSON>roller,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import {
  DIVISION_HEADER,
  FACILITY_HEADER,
  STRUCTURE_CAT_HEADER,
  STRUCTURE_HEADER,
  STRUCTURE_TYPE_HEADER,
} from 'src/app/data-models/data_classes';
import { DataService } from 'src/app/services/data.service';
import { ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { EditFacilityComponent } from 'src/app/components/edit-facility/edit-facility.component';
import { EditDivisionComponent } from 'src/app/components/edit-division/edit-division.component';
import { EditStructureComponent } from 'src/app/components/edit-structure/edit-structure.component';
import * as JSZip from 'jszip';
import { MasterSelectedMode } from 'src/app/shared/app-constants';
declare var require: any;
const csv2json = require('src/assets/csv2json.js');
@Component({
  selector: 'app-facilities-divisions',
  templateUrl: './facilities-divisions.page.html',
  styleUrls: ['./facilities-divisions.page.scss'],
})
export class FacilitiesDivisionsPage implements OnInit, OnDestroy {
  public dataFromAgentOrStructure: any;
  public facilities: FACILITY_HEADER;
  public divisions: DIVISION_HEADER;
  public structures: STRUCTURE_HEADER;
  public facilitiesArray: FACILITY_HEADER[] = [];
  public allFacilitiesArray: FACILITY_HEADER[] = []; // Store all facilities for filtering
  public divisionsArray: DIVISION_HEADER[] = [];
  public structuresArray: any = [];
  public structureTypeHeader: STRUCTURE_TYPE_HEADER[] = [];
  public structureCatHeader: STRUCTURE_CAT_HEADER[] = [];
  public facilitiesArrayLength: number;
  public divisionsArrayLength: number;
  public structuresArrayLength: number;
  public currentFacilitySelected: any = {};
  public currentDivisionSelected: any = {};
  public isEnabledAddingStructure: boolean = false;
  public csvData: any;
  public csvFileContentAsJson: any = [];
  public isInternal: boolean = false;
  public isUserFacilityEmpty: boolean = false;
  public tempStructuresArray: any = [];
  public searchTerm: string = '';
  public facilitySearchTerm: string = '';
  public showStructureSearch: boolean = false;
  public expandedFacilities: Set<string> = new Set();
  public expandedDivisions: Set<string> = new Set();
  public isLoadingFacilities: boolean = false;
  public isLoadingDivisions: boolean = false;
  public isLoadingStructures: boolean = false;
  public selectedStructure: any = null;
  public showActionsFacility: string = null;
  public showActionsDivision: string = null;
  public showActionsStructure: string = null;
  private subscriptions = new Subscription();

  constructor(
    public dataService: DataService,
    public modalCtrl: ModalController,
    private route: ActivatedRoute,
    private router: Router,
    private loadingController: LoadingController,
    public translate: TranslateService,
    public alertController: AlertController,
    public unviredSDK: UnviredCordovaSDK
  ) {
    let subs = this.route.queryParams.subscribe((params) => {
      if (this.router.getCurrentNavigation().extras.state) {
        this.dataFromAgentOrStructure =
          this.router.getCurrentNavigation()?.extras?.state['facility'];
      }
    });
    this.subscriptions.add(subs);
  }
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  async ngOnInit() {
    let userData = await this.dataService.getData('USER_ROLE');
    this.isInternal = userData[0].IS_INTERNAL;
  }

  async ionViewDidEnter() {
    if (this.dataFromAgentOrStructure) {
      if (Object.keys(this.dataFromAgentOrStructure).length > 0) {
        if (this.dataFromAgentOrStructure === 'facility') {
          this.structuresArray = [];
          this.tempStructuresArray = [];
          this.structuresArrayLength = null;
          this.currentDivisionSelected = {};
          // this.enableBulkUpload = false;
          this.isEnabledAddingStructure = false;
          await this.loadFacilities();
        } else if (this.dataFromAgentOrStructure === 'division') {
          await this.loadFacilities();
          await this.selectedOption('facility', this.currentFacilitySelected);
          await this.selectedOption('division', this.currentDivisionSelected);
        } else {
          this.facilitiesArray.push(this.dataFromAgentOrStructure);
          this.facilitiesArrayLength = this.facilitiesArray.length;
        }
      }
    } else {
      await this.loadFacilities();
    }
  }

  //Load Facility
  async loadFacilities() {
    let res: any;
    this.isLoadingFacilities = true;

    try {
      res = await this.dataService.getData('FACILITY_HEADER', null, 'NAME ASC');
      if (res.length > 0) {
        this.allFacilitiesArray = res; // Store all facilities
        this.facilitiesArray = [...res]; // Copy for display
        this.facilitiesArrayLength = this.facilitiesArray.length;
      } else {
        // this.isUserFacilityEmpty = true;
        await this.getFacilitiesFromServer();
      }
    } catch (error) {
      console.error('Error loading facilities:', error);
    } finally {
      this.isLoadingFacilities = false;
    }
  }

  // Filter facilities based on search term
  filterFacilities() {
    if (!this.facilitySearchTerm || this.facilitySearchTerm.trim() === '') {
      this.facilitiesArray = [...this.allFacilitiesArray];
    } else {
      const searchTerm = this.facilitySearchTerm.toLowerCase().trim();
      this.facilitiesArray = this.allFacilitiesArray.filter(facility => {
        return facility.NAME.toLowerCase().includes(searchTerm);
      });
    }
    this.facilitiesArrayLength = this.facilitiesArray.length;
  }

  async selectedOption(type: string, selectedItem) {
    this.searchTerm = '';
    // Only hide structure search when selecting a facility, not a division
    if (type === 'facilities') {
      this.showStructureSearch = false;
    }
    this.selectedStructure = null;
    // Don't hide actions here as we want to show them for the clicked item
    let res: any;
    if (type == 'facilities') {
      // Clear all expanded facilities first, then add the current one
      this.expandedFacilities.clear();
      this.expandedFacilities.add(selectedItem.FACILITY_ID);
      // Clear all expanded divisions
      this.expandedDivisions.clear();

      this.structuresArrayLength = null;
      this.currentDivisionSelected = {};
      this.divisionsArray = [];
      this.structuresArray = [];
      this.tempStructuresArray = [];
      this.divisionsArrayLength = 0;
      this.isEnabledAddingStructure = false;
      this.currentFacilitySelected = selectedItem;
      this.isLoadingDivisions = true;

      try {
        res = await this.dataService.getData(
          'DIVISION_HEADER',
          `FACILITY_ID='${selectedItem.FACILITY_ID}'`, 'NAME ASC'
        );
      // console.log("Divisions from Db", res);
      if (res.length > 0) {
        this.divisionsArray = res;
        this.divisionsArrayLength = this.divisionsArray.length;
        // console.log('divisions Array from DB', this.divisionsArray);
      } else {
        await this.getDivisionsFromServer(selectedItem);
      }
      } catch (error) {
        console.error('Error loading divisions:', error);
      } finally {
        this.isLoadingDivisions = false;
      }
    }

    if (type == 'division') {
      // Clear all expanded divisions first, then add the current one
      this.expandedDivisions.clear();
      this.expandedDivisions.add(selectedItem.DIVISION_ID);

      // this.enableBulkUpload = true;
      this.structuresArrayLength = null;
      this.structuresArray = [];
      this.tempStructuresArray = [];
      this.isEnabledAddingStructure = true;
      this.currentDivisionSelected = selectedItem;
      this.isLoadingStructures = true;

      try {
        res = await this.dataService.getData(
          'STRUCTURE_HEADER',
          `FACILITY_ID='${selectedItem.FACILITY_ID}' AND DIVISION_ID='${selectedItem.DIVISION_ID}'`
        );
      // console.log("Structures from Db", this.structures);
      if (res.length > 0) {
        this.structuresArray = res;
        this.tempStructuresArray = res;
        this.structuresArrayLength = this.structuresArray.length;
        // console.log('Structures Array', this.structuresArray);
        await this.getStructures(
          selectedItem.FACILITY_ID,
          selectedItem.DIVISION_ID
        );
        // console.log('Structures Array after getStructures() call', this.structuresArray);
      } else {
        res = await this.dataService.getData(
          'DIVISION_HEADER',
          `FACILITY_ID='${selectedItem.FACILITY_ID}'`, 'NAME ASC'
        );
        if (res.length > 0) {
          this.divisionsArray = res;
          this.divisionsArrayLength = this.divisionsArray.length;
        } else {
          await this.getDivisionsFromServer(selectedItem);
        }
        await this.getStructuresFromServer(selectedItem);
      }
      } catch (error) {
        console.error('Error loading structures:', error);
      } finally {
        this.isLoadingStructures = false;
      }
    }
  }

  //Get Facilities From Server
  async getFacilitiesFromServer() {
    this.isLoadingFacilities = true;

    try {
      await this.unviredSDK.dbDelete('FACILITY_HEADER', '');
      await this.unviredSDK.dbDelete('DIVISION_HEADER', '');
      await this.unviredSDK.dbDelete('STRUCTURE_HEADER', '');
      this.facilitiesArray = [];
      this.allFacilitiesArray = [];
      this.divisionsArray = [];
      this.structuresArray = [];
      this.tempStructuresArray = [];
      this.currentFacilitySelected = {};
      this.currentDivisionSelected = {};
      this.facilitiesArrayLength = null;
      this.divisionsArrayLength = null;
      this.structuresArrayLength = null;
      this.isEnabledAddingStructure = false;
      let res: any;
      res = await this.dataService.getFacility();
      // console.log("facilities from server", res);
      // console.log("facilities from Db", res);
      res = await this.dataService.getData('FACILITY_HEADER', null, 'NAME ASC');
      if (res.length > 0) {
        let userFacility = await this.dataService.getData('FACILITY_HEADER', null, 'NAME ASC');
        if (userFacility.length > 0) {
          let userFacilityResult = userFacility.filter((o1) =>
            res.some((o2) => o1.FACILITY_ID === o2.FACILITY_ID)
          );
          this.allFacilitiesArray = userFacilityResult;
          this.facilitiesArray = [...this.allFacilitiesArray];
          this.facilitiesArrayLength = this.facilitiesArray.length;
        } else {
          this.allFacilitiesArray = res;
          this.facilitiesArray = [...this.allFacilitiesArray];
          this.facilitiesArrayLength = this.facilitiesArray.length;
        }
      }
    } catch (error) {
      console.error('Error loading facilities from server:', error);
    } finally {
      this.isLoadingFacilities = false;
    }
  }

  //Get Divisions From Server
  async getDivisionsFromServer(selectedItem) {
    this.divisionsArray = [];
    this.structuresArray = [];
    this.tempStructuresArray = [];
    let res: any;
    let customData = {
      DIVISION: [
        {
          DIVISION_HEADER: {
            FACILITY_ID: selectedItem.FACILITY_ID,
            DIVISION_ID: '',
            NAME: '',
            P_MODE: '',
          },
        },
      ],
    };
    res = await this.dataService.getDivisions(customData);
    // console.log("Divisions from Server", res);
    res = await this.dataService.getData(
      'DIVISION_HEADER',
      `FACILITY_ID='${selectedItem.FACILITY_ID}'`, 'NAME ASC'
    );
    // console.log("Divisions from Db", res);
    if (res.length > 0) {
      this.divisionsArray = res;
      this.divisionsArrayLength = this.divisionsArray.length;
      // console.log('divisions Array from DB', this.divisionsArray);
    } else {
      this.divisionsArray = [];
      this.divisionsArrayLength = 0;
    }
  }

  //Get Divisions From Server
  async getStructuresFromServer(selectedItem, isLoaderNotRunning?) {
    this.structuresArray = [];
    this.tempStructuresArray = [];
    let res: any;
    let customData = {
      STRUCTURE: [
        {
          STRUCTURE_HEADER: {
            FACILITY_ID: selectedItem.FACILITY_ID,
            DIVISION_ID: selectedItem.DIVISION_ID,
            TAG: '',
            NAME: '',
            CATEGORY: '',
            STRUCT_TYPE: '',
            STATUS: '',
            P_MODE: '',
          },
        },
      ],
    };
    res = await this.dataService.getStructures(customData);
    res = await this.dataService.getData(
      'STRUCTURE_HEADER',
      `FACILITY_ID='${selectedItem.FACILITY_ID}' AND DIVISION_ID='${selectedItem.DIVISION_ID}'`
    );
    // console.log("Structures from Db", this.structures);
    if (res.length > 0) {
      this.structuresArray = res;
      this.tempStructuresArray = res;
      this.structuresArrayLength = this.structuresArray.length;
      this.structuresArrayLength = this.structuresArray.length;
      res = await this.dataService.getStructureTypes();
      // console.log('structure types from server', res);
      res = await this.dataService.getStructureCategories();
      // console.log('structure category from server', res);
      await this.getStructures(
        selectedItem.FACILITY_ID,
        selectedItem.DIVISION_ID
      );
      // console.log('Structures Array after getStructures() call in servercall', this.structuresArray);
    } else {
      this.structuresArray = [];
      this.tempStructuresArray = [];
      this.structuresArrayLength = 0;
    }
  }

  //Get Structures
  async getStructures(facilityId, divisionId) {
    let res = await this.dataService
      .executeQuery(`SELECT STRUCTURE_HEADER.FACILITY_ID, STRUCTURE_HEADER.DIVISION_ID, STRUCTURE_HEADER.CATEGORY, STRUCTURE_HEADER.NAME, STRUCTURE_HEADER.TAG, STRUCTURE_HEADER.STATUS,
    STRUCTURE_HEADER.STRUCT_TYPE, STRUCTURE_HEADER.LATITUDE, STRUCTURE_HEADER.LONGITUDE, STRUCTURE_TYPE_HEADER.DESCRIPTION AS TYPE_DESCRIPTION, STRUCTURE_CAT_HEADER.DESCRIPTION AS CAT_DESCRIPTION FROM STRUCTURE_HEADER LEFT
   JOIN STRUCTURE_TYPE_HEADER ON STRUCTURE_HEADER.STRUCT_TYPE= STRUCTURE_TYPE_HEADER.STRUCT_TYPE
   LEFT JOIN STRUCTURE_CAT_HEADER ON STRUCTURE_HEADER.CATEGORY=STRUCTURE_CAT_HEADER.CATEGORY WHERE STRUCTURE_HEADER.FACILITY_ID = '${facilityId}' AND STRUCTURE_HEADER.DIVISION_ID = '${divisionId}' ORDER BY STRUCTURE_HEADER.TAG ASC`);
    if (res.length > 0) {
      this.structuresArray = res;
      this.tempStructuresArray = res;
    }
    return;
  }

  //Add Facility
  async addFacility() {
    const modal = await this.modalCtrl.create({
      component: EditFacilityComponent,
      cssClass: 'addOrUpdateFacility',
      backdropDismiss: false,
      componentProps: {
        inputFacility: null,
        mode: MasterSelectedMode.new,
      },
    });
    await modal.present();
    const { data } = await modal.onDidDismiss();
    if (data) {
      console.log('getting facilities from serverrr')
      await this.getFacilitiesFromServer();
    }
  }

  async addDivision() {
    const modal = await this.modalCtrl.create({
      component: EditDivisionComponent,
      cssClass: 'addOrUpdateFacility',
      backdropDismiss: false,
      componentProps: {
        facilityId: this.currentFacilitySelected.FACILITY_ID,
        inputDivision: null,
        mode: MasterSelectedMode.new,
      },
    });
    await modal.present();
    const { data } = await modal.onDidDismiss();
    if (data) {
      await this.getDivisionsFromServer(this.currentFacilitySelected);
    }
  }

  async addOrUpdateStructure(selectedStructure?) {
    const modal = await this.modalCtrl.create({
      component: EditStructureComponent,
      cssClass: 'addOrUpdateFacility',
      backdropDismiss: false,
      componentProps: {
        facilityId: this.currentFacilitySelected.FACILITY_ID,
        divisionId: this.currentDivisionSelected.DIVISION_ID,
        inputStructure: selectedStructure || null,
      },
    });
    await modal.present();
    const { data } = await modal.onDidDismiss();
    if (data && data.success) {
      await this.getStructuresFromServer(this.currentDivisionSelected, 'No');
    }
  }

  // Display Loading dialog.
  async displayPleaseWaitLoader(messageReceived) {
    const loading = await this.loadingController.create({
      message: messageReceived,
      backdropDismiss: false,
    });
    await loading.present();
  }

  //Alert to show warning
  async alertforLoadingSections(messageReceived) {
    const alert = await this.alertController.create({
      message: this.translate.instant(messageReceived),
      buttons: [
        {
          text: this.translate.instant('Okay'),
        },
      ],
    });
    await alert.present();
  }

  //Structure bulk data upload
  async structureBulkDataUpload(files) {
    if (files.length > 0) {
      await this.displayPleaseWaitLoader(
        this.translate.instant('Please wait, your file is being uploaded')
      );
      var reader = new FileReader();
      reader.readAsText(files[0]);
      reader.onload = async (_event) => {
        this.csvData = reader.result;
        const json = csv2json(this.csvData, { parseNumbers: true });
        if (json && json.length > 0) {
          let keys = Object.keys(json[0]);
          let inspectionTypeKeys = [];
          keys.forEach((element) => {
            if (
              element != 'Category' &&
              element != 'Name' &&
              element != 'Status' &&
              element != 'Structure Type' &&
              element != 'Tag'
            ) {
              inspectionTypeKeys.push(element.replace(' Start Year', ''));
            }
          });

          json.forEach((element) => {
            if (
              element.Tag != '' &&
              element.Name != '' &&
              element.Category != '' &&
              element.Status != ''
            ) {
              let json = {
                STRUCTURE_HEADER: {
                  FACILITY_ID: this.currentFacilitySelected.FACILITY_ID,
                  DIVISION_ID: this.currentDivisionSelected.DIVISION_ID,
                  TAG: element?.Tag == undefined ? '' : element?.Tag,
                  NAME: element?.Name == undefined ? '' : element?.Name,
                  CATEGORY:
                    element?.Category == undefined ? '' : element?.Category,
                  STRUCT_TYPE:
                    element['Structure Type'] == undefined
                      ? ''
                      : element['Structure Type'],
                  STATUS: element?.Status == undefined ? '' : element?.Status,
                  P_MODE: 'A',
                },
                STRUCTURE_INSPECTION: [],
              };
              if (inspectionTypeKeys && inspectionTypeKeys.length > 0) {
                inspectionTypeKeys.forEach((element1) => {
                  let obj = {
                    FACILITY_ID: this.currentFacilitySelected.FACILITY_ID,
                    DIVISION_ID: this.currentDivisionSelected.DIVISION_ID,
                    TAG: element?.Tag == undefined ? '' : element?.Tag,
                    INSP_TYPE: element1,
                    START_YEAR:
                      element[element1 + ' Start Year'] == undefined
                        ? ''
                        : element[element1 + ' Start Year'],
                    P_MODE: 'A',
                  };
                  json.STRUCTURE_INSPECTION.push(obj);
                });
              }
              this.csvFileContentAsJson.push(json);
            }
          });
          let finalJson = {
            STRUCTURE: this.csvFileContentAsJson,
          };
          let result: any = await this.dataService.addBulkStructures(finalJson);
          await this.loadingController.dismiss();
          if (result) {
            if (result.type == ResultType.success) {
              if (result.data.InfoMessage === undefined) {
                await this.unviredSDK.dbDelete(
                  'STRUCTURE_HEADER',
                  ''
                );
                await this.getStructuresFromServer(
                  this.currentDivisionSelected,
                  'No'
                );
                await this.alertforUserCreation(
                  this.translate.instant('Structures Added Successfully')
                );
              } else if (result.data.InfoMessage.length > 0) {
                await this.unviredSDK.dbDelete(
                  'STRUCTURE_HEADER',
                  ''
                );
                await this.getStructuresFromServer(
                  this.currentDivisionSelected,
                  'No'
                );
                await this.alertforUserCreation(
                  this.translate.instant(result.message.trim())
                );
              }
            }
            if (result.type == ResultType.error) {
              if (result.data.InfoMessage.length > 0) {
                await this.alertforUserCreation(
                  this.translate.instant(result.message.trim())
                );
              }
            }
          } else {
            await this.alertforUserCreation(
              this.translate.instant('Error Adding Structures')
            );
          }
        } else return;
      };
    }
  }

  //Alert to show warning
  async alertforUserCreation(messageReceived) {
    const alert = await this.alertController.create({
      message: this.translate.instant(messageReceived),
      buttons: [
        {
          text: this.translate.instant('Okay'),
        },
      ],
    });
    await alert.present();
  }

  downloadCsv(csvFiles) {
    // Create a new instance of JSZip
    const zip = new JSZip();

    // Iterate through the CSV files and add them to the zip file
    csvFiles.forEach(({ filename, text }) => {
      zip.file(`${filename}.csv`, `${text}`);
    });

    // Generate the zip file asynchronously
    zip.generateAsync({ type: 'blob' }).then(async (blob) => {
      // Create a download link for the zip file
      const element = document.createElement('a');
      element.href = URL.createObjectURL(blob);
      element.download = 'sample_files.zip';
      element.style.display = 'none';

      // Append the link to the body and trigger the download
      document.body.appendChild(element);
      element.click();

      // Clean up: remove the link
      document.body.removeChild(element);
    });
  }


  // async scheduleInspections() {
  //   await this.scheduleInspectionsConfirm();
  // }

  async presentAlert() {
    const alert = await this.alertController.create({
      header: 'Info',
      message: 'Inspections will be generated based on configured schedules',
      buttons: ['OK'],
    });
    await alert.present();
  }

  // async scheduleInspectionsConfirm() {
  //   const alert = await this.alertController.create({
  //     header: 'Confirm',
  //     message:
  //       this.translate.instant(
  //         'Are you sure, you want to create schedule inspections'
  //       ) + '?',
  //     buttons: [
  //       {
  //         text: this.translate.instant('No'),
  //         role: 'cancel',
  //         cssClass: 'secondary',
  //         handler: (blah) => {},
  //       },
  //       {
  //         text: this.translate.instant('Yes'),
  //         handler: async () => {
  //           let result = await this.dataService.scheduleInspections();
  //           this.presentAlert();
  //         },
  //       },
  //     ],
  //   });

  //   await alert.present();
  // }

  filterStructures() {
    if (this.searchTerm.length > 0) {
      this.structuresArray = this.tempStructuresArray.filter((item) => {
        return (
          (item.TAG &&
            item.TAG.toLowerCase().indexOf(this.searchTerm.toLowerCase()) >
            -1) ||
          (item.NAME &&
            item.NAME.toLowerCase().indexOf(this.searchTerm.toLowerCase()) >
            -1) ||
          (item.TYPE_DESCRIPTION &&
            item.TYPE_DESCRIPTION.toLowerCase().indexOf(
              this.searchTerm.toLowerCase()
            ) > -1)
        );
      });
    } else {
      this.structuresArray = this.tempStructuresArray;
    }
  }

  getSelectedFcilityProperties() {
    return Object.keys(this.currentFacilitySelected).length > 0 ? true : false;
  }

  // Toggle expand/collapse for a facility
  toggleFacility(facility, event) {
    event.stopPropagation();
    this.selectedStructure = null;
    this.hideActions();
    const facilityId = facility.FACILITY_ID;

    if (this.expandedFacilities.has(facilityId)) {
      this.expandedFacilities.delete(facilityId);
      // If this was the selected facility, deselect it
      if (this.currentFacilitySelected.FACILITY_ID === facilityId) {
        this.currentFacilitySelected = {};
        this.divisionsArray = [];
        this.divisionsArrayLength = 0;
      }
    } else {
      // Clear all expanded facilities first
      this.expandedFacilities.clear();
      // Then add the current one
      this.expandedFacilities.add(facilityId);
      // Clear all expanded divisions
      this.expandedDivisions.clear();
      // Load divisions for this facility
      this.selectedOption('facilities', facility);
    }
  }

  // Toggle expand/collapse for a division
  toggleDivision(division, event) {
    event.stopPropagation();
    this.selectedStructure = null;
    this.hideActions();
    const divisionId = division.DIVISION_ID;

    if (this.expandedDivisions.has(divisionId)) {
      this.expandedDivisions.delete(divisionId);
      // Hide structure search if it was showing
      this.showStructureSearch = false;
      // If this was the selected division, deselect it
      if (this.currentDivisionSelected.DIVISION_ID === divisionId) {
        this.currentDivisionSelected = {};
        this.structuresArray = [];
        this.tempStructuresArray = [];
        this.structuresArrayLength = 0;
        this.isEnabledAddingStructure = false;
      }
    } else {
      // Clear all expanded divisions first
      this.expandedDivisions.clear();
      // Then add the current one
      this.expandedDivisions.add(divisionId);
      // Load structures for this division
      this.selectedOption('division', division);
    }
  }

  // Check if a facility is expanded
  isFacilityExpanded(facilityId) {
    return this.expandedFacilities.has(facilityId);
  }

  // Check if a division is expanded
  isDivisionExpanded(divisionId) {
    return this.expandedDivisions.has(divisionId);
  }

  // Select a structure
  selectStructure(structure) {
    this.selectedStructure = structure;
  }

  // Toggle show actions for an item
  toggleShowActions(type: string, id: string) {
    // Hide all actions first
    this.hideActions();

    // Show actions for the clicked item
    if (type === 'facility') {
      this.showActionsFacility = id;
    } else if (type === 'division') {
      this.showActionsDivision = id;
    } else if (type === 'structure') {
      this.showActionsStructure = id;
    }
  }

  // Hide all actions
  hideActions() {
    this.showActionsFacility = null;
    this.showActionsDivision = null;
    this.showActionsStructure = null;
  }

  // Toggle structure search for a specific division
  toggleStructureSearch(division) {
    // If search is already shown for this division, hide it
    if (this.showStructureSearch && this.currentDivisionSelected.DIVISION_ID === division.DIVISION_ID) {
      this.showStructureSearch = false;
    } else {
      // Otherwise, show search for this division
      this.showStructureSearch = true;
      // Make sure this division is selected and expanded
      if (this.currentDivisionSelected.DIVISION_ID !== division.DIVISION_ID) {
        this.selectedOption('division', division);
      }
      // Clear any previous search term
      this.searchTerm = '';
      // Reset filtered structures
      this.filterStructures();
    }
  }

  // Check if a facility has divisions
  hasDivisions(facilityId: string): boolean {
    // If divisions are still loading, assume there might be divisions
    if (this.isLoadingDivisions) return true;

    // If the facility is not selected/expanded yet, we need to check the database
    if (this.currentFacilitySelected.FACILITY_ID !== facilityId) {
      // For now, assume there might be divisions
      // In a real implementation, you might want to cache this information
      return true;
    }

    // If the facility is selected, check if there are divisions
    return this.divisionsArray && this.divisionsArray.length > 0;
  }

  // Check if a division has structures
  hasStructures(facilityId: string, divisionId: string): boolean {
    // If structures are still loading, assume there might be structures
    if (this.isLoadingStructures) return true;

    // If the division is not selected/expanded yet, we need to check the database
    if (this.currentDivisionSelected.DIVISION_ID !== divisionId) {
      // For now, assume there might be structures
      // In a real implementation, you might want to cache this information
      return true;
    }

    // If the division is selected, check if there are structures
    return this.structuresArray && this.structuresArray.length > 0;
  }

  async editFacility(facility) {
    const modal = await this.modalCtrl.create({
      component: EditFacilityComponent,
      cssClass: 'addOrUpdateFacility',
      backdropDismiss: false,
      componentProps: {
        inputFacility: facility,
        mode: MasterSelectedMode.edit
      },
    });
    await modal.present();
    const { data } = await modal.onDidDismiss();
    if (data) {
      await this.getFacilitiesFromServer();
    }
  }

  async editDivision(division) {
    const modal = await this.modalCtrl.create({
      component: EditDivisionComponent,
      cssClass: 'addOrUpdateFacility',
      backdropDismiss: false,
      componentProps: {
        facilityId: this.currentFacilitySelected.FACILITY_ID,
        inputDivision: division,
        mode: MasterSelectedMode.edit,
      },
    });
    await modal.present();
    const { data } = await modal.onDidDismiss();
    if (data) {
      await this.getDivisionsFromServer(this.currentFacilitySelected);
    }

  }
}
