import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { FacilitiesDivisionsPageRoutingModule } from './facilities-divisions-routing.module';

import { FacilitiesDivisionsPage } from './facilities-divisions.page';
import { TranslateModule } from '@ngx-translate/core';
import { EditFacilityComponent } from 'src/app/components/edit-facility/edit-facility.component';
import { EditDivisionComponent } from 'src/app/components/edit-division/edit-division.component';
import { EditStructureComponent } from 'src/app/components/edit-structure/edit-structure.component';
import { BoundaryPickerComponent } from 'src/app/components/boundary-picker/boundary-picker.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    FacilitiesDivisionsPageRoutingModule,
    TranslateModule,
  ],
  declarations: [
    FacilitiesDivisionsPage,
    EditFacilityComponent,
    EditDivisionComponent,
    EditStructureComponent,
    BoundaryPickerComponent,
  ],
})
export class FacilitiesDivisionsPageModule {}
