<div>
<ion-header>
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-back-button (click)="goBack()"></ion-back-button>
    </ion-buttons>
    <ion-title>{{"Structures" | translate}}</ion-title>
      <ion-buttons slot="end">
        <ion-button fill="outline" size="small" (click)="saveStructure(); $event.stopPropagation();"
          class="refreshButton">
          <ion-icon slot="start" name="save"></ion-icon>
          Save
        </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

  <!-- Page Path -->
  <ion-buttons class="pagePath">
    <ion-button (click)="goToFacility('facility')">{{facilityName | translate}}</ion-button>
    <i class="fal fa-chevron-right"></i>
    <ion-button (click)="goToFacility('division')">{{divisionName | translate}}</ion-button>
    <i class="fal fa-chevron-right"></i>
    <ion-button>{{structureData.TAG}}</ion-button>
  </ion-buttons>

  <ion-grid>
    <ion-row>
      <!-- Main left Column -->
      <ion-col class="leftColumn">
        <div class="structureDropdownPadding">
          <label class="structureDropdownLabel">Structure Tag</label>
          <input [disabled]="disableEditStructureTag" type="text" [(ngModel)]="structureData.TAG"
            class="form-control margin-bottom" id="exampleInputEmail1" aria-describedby="emailHelp"
            placeholder="Enter Structure Tag">
        </div>

        <div class="structureDropdownPadding">
          <label class="structureDropdownLabel">Structure Description</label>
          <input type="text" [(ngModel)]="structureData.NAME" class="form-control margin-bottom" id="exampleInputEmail"
            aria-describedby="emailHelp" placeholder="Enter Structure Description">
        </div>

        <div class="structureDropdownPadding">
          <ion-label class="structureDropdownLabel" position="stacked">{{ "Structure Category" | translate}}</ion-label>
          <select [(ngModel)]="selectedCategoryValue" class="form-select form-select-sm margin-bottom"
            aria-label=".form-select-sm example">
            <option *ngFor="let category of structureCategories" [ngValue]="category.CATEGORY">{{category.CATEGORY}} -
              {{category.DESCRIPTION}}</option>
          </select>
        </div>
        <div class="structureDropdownPadding">
          <ion-label class="structureDropdownLabel" position="stacked">{{ "Structure Type" | translate}}</ion-label>

          <select [(ngModel)]="selectedTypeValue" class="form-select form-select-sm margin-bottom"
            aria-label=".form-select-sm example">
            <option *ngFor="let types of structureTypes" [ngValue]="types.STRUCT_TYPE">{{types.STRUCT_TYPE}} -
              {{types.DESCRIPTION}}</option>
          </select>

        </div>
        <div class="structureDropdownPadding">
          <ion-label class="structureDropdownLabel" position="stacked">{{ "Structure Status" | translate}}</ion-label>
          <select [(ngModel)]="selectedStatusValue" class="form-select form-select-sm margin-bottom"
            aria-label=".form-select-sm example">
            <option *ngFor="let status of structureStatus" [ngValue]="status.STATUS">{{status.STATUS}} -
              {{status.DESCRIPTION}}</option>
          </select>
        </div>

        <!-- Location Fields -->
        <div class="structureDropdownPadding">
          <ion-label class="structureDropdownLabel" position="stacked">Latitude</ion-label>
          <input type="number" [(ngModel)]="structureData.LATITUDE" step="0.000001" class="form-control margin-bottom"
            placeholder="Enter Latitude">
        </div>

        <div class="structureDropdownPadding">
          <ion-label class="structureDropdownLabel" position="stacked">Longitude</ion-label>
          <input type="number" [(ngModel)]="structureData.LONGITUDE" step="0.000001" class="form-control margin-bottom"
            placeholder="Enter Longitude">
        </div>

        <!-- Map Button -->
        <div class="structureDropdownPadding">
          <ion-button class="map-button" (click)="openLocationPicker()">
            <ion-icon name="map-outline" slot="start"></ion-icon>
            Select Location on Map
          </ion-button>
        </div>
      </ion-col>

      <!-- Main Right Column -->
      <ion-col class="rightColumn">

        <ion-card (click)="imageInFullscreen()" class="image" *ngIf="showImage">
          <img [src]='uploadedImage' alt="structure image">
        </ion-card>

        <ion-button class="uploadButton" (change)="uploadFiles(structureImage.files)">
          <label for="image-input">
            {{ uploadImageTitle | translate}}
          </label>
          <input #structureImage accept="image/*" id="image-input" type="file" />
        </ion-button>
        <p class="error">{{displayError | translate}}</p>
      </ion-col>
    </ion-row>
  </ion-grid>
</div>
<ion-footer mode="ios">
  <ion-toolbar mode="ios">
    <ion-button slot="end" color="danger" mode="md" (click)="goBack()">Cancel</ion-button>
    <ion-button slot="end" color="success" mode="md" [disabled]="!structureData.TAG || !structureData.NAME || !selectedCategoryValue || !selectedTypeValue || !selectedStatusValue" (click)="saveStructure()">Save</ion-button>
  </ion-toolbar>
</ion-footer>