ion-toolbar {
  --color: white;
}

// ion-button {
//   --background: white;
//   --color: #00629b;
//   margin-right: 20px;
//   cursor: pointer;
// }

// .iconWithinButton {
//   color: white;
// }

.saveBtn {
  cursor: pointer;
}

.pagePath {
  height: 8%;
  background: #f5f7fa;
  padding: 20px;
  color: #00629b;
}

ion-button {
  text-transform: none;
}

.leftColumn {
  padding: 1%;
}

ion-card {
  margin: 1%;
}

.yearSelection {
  display: flex;
  width: 48%;
  justify-content: space-around;
  align-items: center;
  padding: 2%;
  margin: 1% 1% 4%;
}

.startYear {
  margin: 0 1% 0 10%;
}

ion-checkbox {
  margin-right: 3%;
}

.structureDropdownPadding {
  margin-left: 1%;
}

.form-select {
  padding: 1%;
}
.margin-bottom {
  margin-bottom: 2%;
}

//Main Right Column
.image {
  max-width: 80% !important;
  margin-bottom: 5%;
  cursor: pointer;
}

.rightColumn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  ion-card {
    width: 300px;
    height: 200px;
    flex: 0 0 auto;
    text-align: center;
  }
}

.rightColumn > ion-card > img {
  min-width: 100%;
  height: 200px;
  object-fit: cover;
}

.uploadButton {
  text-align: center;
  input {
    display: none;
  }
  label {
    cursor: pointer;
  }
}

.structureDropdownLabel {
  color: #00629b;
  padding-bottom: 10px;
}

.error {
  text-align: center;
  color: #d63232;
}

.map-button {
  --background: #00629b;
  --color: white;
  margin-bottom: 15px;
  width: 100%;
}

.refreshButton {
  // --border-color: #868686 !important;
  --border-width: 1px !important;
  --border-radius: 8px !important;
  font-weight: 600 !important;
}