import { Compo<PERSON>, <PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import {
  Al<PERSON><PERSON>ontroller,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { AttachmentsService } from 'src/app/services/attachments.service';
import * as moment from 'moment';
import { DomSanitizer } from '@angular/platform-browser';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Subscription } from 'rxjs';
import { DOCUMENT_ATTACHMENT, DOCUMENT_HEADER, STRUCTURE_CAT_HEADER, STRUCTURE_STATUS_HEADER, STRUCTURE_TYPE_HEADER } from 'src/app/data-models/data_classes';
import { DataService } from 'src/app/services/data.service';
import { ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { ImageFullScreenComponent } from 'src/app/components/image-full-screen/image-full-screen.component';
import { LocationPickerComponent } from 'src/app/components/location-picker/location-picker.component';

@Component({
  selector: 'app-structure',
  templateUrl: './structure.page.html',
  styleUrls: ['./structure.page.scss'],
})

export class StructurePage implements OnInit, OnDestroy {
  public yearArray: number[] = [];
  public dataFromFacilityPage: any;
  public structureData: any = {};
  public structureCategories: STRUCTURE_CAT_HEADER[] = [];
  public structureTypes: STRUCTURE_TYPE_HEADER[] = [];
  public structureStatus: STRUCTURE_STATUS_HEADER[] = [];
  public selectedCategory: STRUCTURE_CAT_HEADER;
  public selectedType: STRUCTURE_TYPE_HEADER;
  public selectedStatus: STRUCTURE_STATUS_HEADER;
  public selectedYear: number;
  public uploadImageTitle: string = 'Upload Image';
  public uploadedImage: any;
  public isUpdatingImage: boolean = false;
  public canUpdateImage: boolean = false;
  public showImage: boolean = false;
  public inspectionTypeHeader: any = [];
  public documentHeader: DOCUMENT_HEADER;
  public structureInspection: any = [];
  public disableEditStructureTag: boolean = false;
  public isUpdatingStructure: boolean = false;
  public imageJustUploaded: boolean = false;
  public currentDoc: any;
  public currentAttachment: any;
  public enableAdhocCreation: boolean = false;
  public facilityName: string = '';
  public divisionName: string = '';
  public displayError: string = '';
  public selectedLevels = [];
  public defaultSelectedLevels = [];
  public tempSelectedLevels: any = [];
  public selectedTypeValue = '';
  public selectedStatusValue = '';
  public selectedCategoryValue = '';
  private subscriptions = new Subscription()

  constructor(
    public dataService: DataService,
    private route: ActivatedRoute,
    private router: Router,
    public translate: TranslateService,
    private loadingController: LoadingController,
    private attachmentService: AttachmentsService,
    private unviredSDK: UnviredCordovaSDK,
    private alertController: AlertController,
    public modalCtrl: ModalController,
    public sanitizer: DomSanitizer,
    private httpClient: HttpClient,
    private ngZone: NgZone
  ) {
    let sub = this.route.queryParams.subscribe((params) => {
      if (this.router.getCurrentNavigation().extras.state) {
        this.dataFromFacilityPage =
          this.router.getCurrentNavigation().extras.state['structure'];
        this.facilityName =
          this.router.getCurrentNavigation().extras.state['facilityName'];
        this.divisionName =
          this.router.getCurrentNavigation().extras.state['divisionName'];
        console.log(
          'Received structure from Facilities Page',
          this.dataFromFacilityPage
        );
      }
    });
    this.subscriptions.add(sub)
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe()
  }

  async ngOnInit() {
    let res: any;
    this.selectedYear = new Date().getFullYear();
    await this.dataService.getStructureStatus();
    res = await this.dataService.getData('STRUCTURE_CAT_HEADER');
    this.structureCategories = res;
    this.selectedCategory = this.structureCategories[0];
    this.selectedCategoryValue = this.structureCategories[0].CATEGORY;
    res = await this.unviredSDK.dbExecuteStatement('SELECT * FROM STRUCTURE_TYPE_HEADER ORDER BY LENGTH(STRUCT_TYPE), STRUCT_TYPE')
    if (res.type == ResultType.success) {
      this.structureTypes = res.data
      this.selectedType = this.structureTypes[0];
      this.selectedTypeValue = this.structureTypes[0]?.STRUCT_TYPE;
    }
    res = await this.dataService.getData('STRUCTURE_STATUS_HEADER');
    this.structureStatus = res;
    this.selectedStatus = this.structureStatus[0];
    this.selectedStatusValue = this.structureStatus[0]?.STATUS;;

    if (this.dataFromFacilityPage) {
      if (Object.keys(this.dataFromFacilityPage).length > 0) {
        if (this.dataFromFacilityPage.TAG) {
          this.isUpdatingStructure = true;
          this.structureData = this.dataFromFacilityPage;
          this.disableEditStructureTag = true;
          console.log('structure data', this.structureData);
          await this.getImageThumbnail();
        } else {
          this.structureData.FACILITY_ID =
            this.dataFromFacilityPage.FACILITY_ID;
          this.structureData.DIVISION_ID =
            this.dataFromFacilityPage.DIVISION_ID;
        }
      }
    }
    if (this.isUpdatingStructure) {
      this.defaultSelectedLevels = [];
      this.tempSelectedLevels = [];
      this.selectedType = new STRUCTURE_TYPE_HEADER();
      this.selectedType.STRUCT_TYPE = this.structureData.STRUCT_TYPE;
      this.selectedType.DESCRIPTION = this.structureData.TYPE_DESCRIPTION;
      this.selectedTypeValue = this.structureData.STRUCT_TYPE;
      this.selectedCategoryValue = this.structureData.CATEGORY;
      this.selectedStatusValue = this.structureData.STATUS;
      this.uploadImageTitle = this.isUpdatingImage
        ? 'Change Image'
        : 'Upload Image';
    }
  }

  //get Image thumbnail
  async getImageThumbnail() {
    let res = await this.dataService.executeQuery(
      `SELECT * FROM STRUCTURE_DOC WHERE TAG='${this.structureData.TAG}' AND DIVISION_ID='${this.structureData.DIVISION_ID}' AND FACILITY_ID='${this.structureData.FACILITY_ID}'`
    );
    this.documentHeader = res;
    this.ngZone.run(() => {
      if (res?.length > 0) {
        if (res[0].THUMBNAIL !== ('' || null)) {
          this.uploadedImage = this.sanitizer.bypassSecurityTrustResourceUrl(
            `data:image/png;base64,${res[0].THUMBNAIL}`
          );
          console.log('received thumbnail', this.uploadedImage);
          this.canUpdateImage = true;
          this.isUpdatingImage = true;
          this.showImage = true;
        }
      } else {
        this.isUpdatingImage = false;
        this.canUpdateImage = false;
        this.showImage = false;
      }
    })

    return this.documentHeader;
  }

  //Upload Image from Local
  async uploadFiles(files) {
    if (files.length === 0) return;

    var mimeType = files[0].type;
    if (mimeType.match(/image\/*/) == null) {
      return;
    }
    var reader = new FileReader();
    reader.readAsDataURL(files[0]);
    reader.onload = async (_event) => {
      this.uploadedImage = reader.result;
      await this.uploadImage(files);
    };
  }

  async uploadImage(files) {
    await this.displayPleaseWaitLoader();
    this.attachmentService.uploadFileToServer(files[0]).then(
      async (result) => {
        let documetAttachmentHeader = this.createDocumentAttachmentHeader(
          result.attachmentId,
          files[0]
        );
        let documetHeader = await this.createDocumentHeader(
          this.canUpdateImage
            ? this.documentHeader[0].DOC_ID
            : result.attachmentId,
          files[0],
          this.canUpdateImage ? 'M' : 'A'
        );

        let documetHeaderInput = {
          DOCUMENT: [
            {
              DOCUMENT_HEADER: documetHeader,
              DOCUMENT_ATTACHMENT: [documetAttachmentHeader],
            },
          ],
        };

        let res: any = await this.attachmentService.uploadAttachment(
          documetHeaderInput
        );
        if (res) {
          if (res.type == ResultType.success) {
            await this.unviredSDK.dbSaveWebData();
            if (res.data.InfoMessage === undefined) {
              this.showImage = true;
              this.currentDoc = res.data.DOCUMENT[0].DOCUMENT_HEADER;
              this.currentAttachment = res.data.DOCUMENT[0].DOCUMENT_ATTACHMENT;
              this.imageJustUploaded = true;
              this.canUpdateImage = true;
              await this.loadingController.dismiss();
              // await this.saveStructure('fromDocumentUpload');
              this.unviredSDK.logInfo(
                'StructurePage',
                'uploadImage',
                'Image uploaded successfully.'
              );
            } else if (res.data.InfoMessage.length > 0) {
              this.displayError = this.translate.instant(res.message.trim());
            }
          }
          if (res.type == ResultType.error) {
            if (res.code && res.code === 401) {
              this.unviredSDK.logError(
                'StructurePage',
                'uploadImage',
                'Error while uploading image.'
              );
              await this.dataService.logoutToInitialPage();
            } else if (res.data.InfoMessage.length > 0) {
              this.displayError = res.message.trim();
            }
          }
        } else {
          this.unviredSDK.logError(
            'StructurePage',
            'uploadImage',
            'Error while uploading image : ' + res.message
          );
          this.displayError = this.translate.instant(
            this.isUpdatingStructure
              ? 'Error Updating Structure'
              : 'Error Adding Structure'
          );
        }
      },
      (error) => {
        this.loadingController.dismiss();
        this.displayError = this.translate.instant(
          'Attachment upload failed, Please try again'
        );
      }
    );
  }

  async createDocumentHeader(
    attachmentUid: string,
    file: any,
    mode: string
  ): Promise<any> {
    var documentHeader = <DOCUMENT_HEADER>{};
    documentHeader.DOC_ID = attachmentUid;
    documentHeader.FILE_NAME = file.name;
    documentHeader.TITLE = '';
    documentHeader.CREATED_BY = '';
    documentHeader.CREATED_ON = moment().unix();
    documentHeader.P_MODE = mode;

    // Fetch user id from user role table.
    let userResult = await this.unviredSDK.dbExecuteStatement(
      `SELECT * FROM USER_ROLE`
    );
    if (userResult.type == ResultType.success) {
      if (userResult.data && userResult.data.length > 0) {
        documentHeader.CREATED_BY = userResult.data[0].USER_ID;
      }
    }
    return documentHeader;
  }

  createDocumentAttachmentHeader(attachmentUid: string, file: any): any {
    var newAttachment = <DOCUMENT_ATTACHMENT>{};
    newAttachment.UID = attachmentUid;
    newAttachment.FILE_NAME = file.name;
    newAttachment.MIME_TYPE = file.type;
    newAttachment.ATTACHMENT_STATUS = 'UPLOADED';
    return newAttachment;
  }

  //Update Structures
  async updateStructures() {
    let customData = {
      STRUCTURE: [
        {
          STRUCTURE_HEADER: {
            FACILITY_ID: this.structureData.FACILITY_ID,
            DIVISION_ID: this.structureData.DIVISION_ID,
            TAG: this.structureData.TAG,
            NAME: '',
            CATEGORY: '',
            STRUCT_TYPE: '',
            STATUS: '',
            P_MODE: '',
          },
        },
      ],
    };
    await this.dataService.getStructures(customData);
  }

  // Show Alert.
  async showAlert(message: string, navigateToFacilityPage?: string) {
    const alert = await this.alertController.create({
      header: this.translate.instant('Message'),
      message: message,
      backdropDismiss: false,
      buttons: [
        {
          text: this.translate.instant('OK'),
          handler: async () => {
            if (navigateToFacilityPage === '') {
              // this.router.navigate(['/facilities-divisions']);
              let fromStructurePage: NavigationExtras = {
                state: {
                  facility: 'division',
                },
              };
              this.ngZone.run(() =>
                this.router.navigate(
                  ['facilities-divisions'],
                  fromStructurePage
                )
              );
            }
          },
        },
      ],
    });
    await alert.present();
  }

  // Display Loading dialog.
  async displayPleaseWaitLoader() {
    const loading = await this.loadingController.create({
      message: this.translate.instant('Please wait') + '...',
      backdropDismiss: false,
    });
    await loading.present();
  }

  //Save Structure
  async saveStructure(fromDocumentUpload?) {
    await this.displayPleaseWaitLoader();
    let structureHeader = {
      LID: this.unviredSDK.guid().replace(/-/g, ''),
      FACILITY_ID: this.structureData.FACILITY_ID,
      DIVISION_ID: this.structureData.DIVISION_ID,
      TAG: this.structureData.TAG,
      NAME: this.structureData.NAME,
      CATEGORY: this.selectedCategoryValue,
      STRUCT_TYPE: this.selectedTypeValue,
      STATUS: this.selectedStatusValue,
      LATITUDE: this.structureData.LATITUDE || null,
      LONGITUDE: this.structureData.LONGITUDE || null,
      P_MODE: '',
      OBJECT_STATUS: 1,
    };
    structureHeader.P_MODE = this.isUpdatingStructure ? 'M' : 'A';
    structureHeader.OBJECT_STATUS = this.isUpdatingStructure ? 2 : 1;
    await this.unviredSDK.dbInsertOrUpdate(
      'STRUCTURE_HEADER',
      structureHeader,
      true
    );
    if (this.imageJustUploaded) {
      let structureDoc = {
        FID: structureHeader.LID,
        FACILITY_ID: this.structureData.FACILITY_ID,
        DIVISION_ID: this.structureData.DIVISION_ID,
        TAG: this.structureData.TAG,
        DOC_ID: this.currentDoc.DOC_ID,
        DOC_TYPE: 'image',
        THUMBNAIL: '',
        P_MODE: '',
        OBJECT_STATUS: null,
      };
      structureDoc.P_MODE = this.isUpdatingImage ? 'M' : 'A';
      structureDoc.OBJECT_STATUS = this.isUpdatingImage ? 2 : 1;
      await this.unviredSDK.dbInsertOrUpdate(
        'STRUCTURE_DOC',
        structureDoc,
        false
      );
    }
    let result: any = await this.dataService.modifyStructure({
      STRUCTURE_HEADER: structureHeader,
    });
    console.log('structure update response', result);
    await this.loadingController.dismiss();
    if (result) {
      if (result.type == ResultType.success) {
        await this.unviredSDK.dbSaveWebData();
        await this.getImageThumbnail();
        this.uploadImageTitle = this.isUpdatingImage
          ? 'Change Image'
          : 'Upload Image';
        if (result.data.InfoMessage === undefined) {
          this.unviredSDK.logInfo(
            'StructurePage',
            'saveStructure',
            'Structure has been modified successfully.'
          );
          this.updateStructures();
          if (!fromDocumentUpload) {
            await this.showAlert(
              this.translate.instant(
                this.isUpdatingStructure
                  ? 'Structure Updated Successfully'
                  : 'Structure Added Successfully'
              ),
              ''
            );
          }
        } else if (result.data.InfoMessage.length > 0) {
          this.displayError = this.translate.instant(result.message.trim());
        }
      }
      if (result.type == ResultType.error) {
        if (result.code && result.code === 401) {
          this.unviredSDK.logError(
            'StructurePage',
            'saveStructure',
            'Error while modifying Structure.'
          );
          await this.dataService.logoutToInitialPage();
        } else if (result.code && result.code === 404) {
          this.unviredSDK.logError(
            'StructurePage',
            'saveStructure',
            'Error while modifying Structure.'
          );
          await this.loadingController.dismiss();
        } else if (result.data.InfoMessage.length > 0) {
          this.displayError = result.message.trim();
        }
      }
    } else {
      this.unviredSDK.logError(
        'StructurePage',
        'saveStructure',
        'Error while modifying Structure : ' + result.message
      );
      this.displayError = this.translate.instant(
        this.isUpdatingStructure
          ? 'Error Updating Structure'
          : 'Error Adding Structure'
      );
    }
  }


  //Display Structure image in full screen
  async imageInFullscreen() {
    let responseData: any;
    await this.displayPleaseWaitLoader();
    let base64data: any;
    let documentHeader = await this.getImageThumbnail();
    const documentName = this.structureData.TAG;
    console.log('document name', documentName);
    const documentData = {
      DOCUMENT: [
        {
          DOCUMENT_HEADER: {
            DOC_ID: documentHeader[0].DOC_ID,
            NAME: '',
            DESCR: '',
            CREATED_BY: '',
            CREATED_ON: '',
            P_MODE: '',
          },
        },
      ],
    };
    let response = await this.dataService.getDocument(documentData);
    if (response?.DOCUMENT_ATTACHMENT[0] !== undefined) {
      responseData =
        await this.attachmentService.downloadAndWriteAttachmentToFile(
          response.DOCUMENT_ATTACHMENT[0],
          documentName
        );
    } else {
      this.displayError = response;
    }
    console.log('document download response', responseData);
    var reader = new FileReader();
    reader.readAsDataURL(responseData.body);
    reader.onload = async (_event) => {
      base64data = reader.result;
      console.log(base64data);
      let downloadedImage = this.sanitizer.bypassSecurityTrustResourceUrl(
        `${base64data}`
      );
      await this.loadingController.dismiss();
      const modal = await this.modalCtrl.create({
        cssClass: 'full-screen-modal',
        component: ImageFullScreenComponent,
        backdropDismiss: false,
        componentProps: {
          imagePath: downloadedImage,
          imageName: documentName,
        },
      });
      await modal.present();
    };
  }

  async goToFacility(selected: string) {
    let fromStructurePage: NavigationExtras = {
      state: {
        facility: selected,
      },
    };
    this.ngZone.run(() =>
      this.router.navigate(['facilities-divisions'], fromStructurePage)
    );
  }

  async goBack() {
    let fromStructurePage: NavigationExtras = {
      state: {
        facility: 'division',
      },
    };
    this.ngZone.run(() =>
      this.router.navigate(['facilities-divisions'], fromStructurePage)
    );
  }

  // Open location picker modal
  async openLocationPicker() {
    try {
      const modal = await this.modalCtrl.create({
        component: LocationPickerComponent,
        cssClass: 'map-modal',
        componentProps: {
          initialLatitude: this.structureData.LATITUDE,
          initialLongitude: this.structureData.LONGITUDE
        }
      });

      await modal.present();

      const { data } = await modal.onDidDismiss();
      if (data) {
        this.structureData.LATITUDE = data.latitude;
        this.structureData.LONGITUDE = data.longitude;
      }
    } catch (error) {
      console.error('Error opening location picker:', error);
      this.displayError = 'Error opening location picker';
    }
  }
}
