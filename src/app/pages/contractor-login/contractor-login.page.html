  <ion-fab vertical="top" size="small" horizontal="start" slot="fixed">
    <ion-fab-button color="light" size="small" (click)="gotoLogin()">
      <ion-icon name="arrow-back"></ion-icon>
    </ion-fab-button>
  </ion-fab>

  <div class="mainBlock">
    <div class="main-page">
      <img class="carmeuse-logo" src="assets/images/unvired.png" alt="Carmeuse-logo" />

      <ion-text [color]="messageColor" class="textCenter" *ngIf="showErrorMessage"
        (ionChange)="onChangeDisableErrorMsg($event)">
        <span>{{errorMessage}}</span>
      </ion-text>

      <ion-progress-bar type="indeterminate" *ngIf="progressbar"></ion-progress-bar>

      <div id="form">
        <div id="email">
          <ion-input mode="md" [(ngModel)]="emailId" placeholder="Enter email"
            style="--padding-start: 16px !important;min-height: 45px !important;" labelPlacement="stacked"
            fill="outline" (ionChange)="onChangeDisableErrorMsg($event)" (keyup.enter)="checkAndLogin()">
            <div slot="label">E-mail</div>
            <ion-icon slot="start" name="mail-outline" style="color: #1A374D;font-size: 25px;"></ion-icon>
          </ion-input>
        </div>
    <div id="password">
          <ion-input mode="md" [(ngModel)]="password" [type]="showPassword ? 'text' : 'password'"  placeholder="Enter password" 
            style="--padding-start: 16px !important;min-height: 45px !important;" labelPlacement="stacked"
            fill="outline" (ionChange)="onChangeDisableErrorMsg($event)" (keyup.enter)="checkAndLogin()" >
            <div slot="label">Password</div>
            <ion-icon slot="start" style="color: #1A374D;font-size: 25px;" name="lock-closed-outline"></ion-icon>
            <ion-button fill="clear" slot="end" [attr.aria-label]="showPassword ? 'Hide password' : 'Show password'" (click)="togglePasswordVisibility()">
              <ion-icon slot="icon-only" [name]="showPassword ? 'eye-outline' : 'eye-off'" aria-hidden="true" style="color: #9CB1BA"></ion-icon>
            </ion-button>
          </ion-input>
        </div> 

       
        <div id="forgotPassword">
          <p (click)="forgetPassword($event)">Forgotten Password</p>
        </div>

        <div id="login-button">
          <ion-button style="font-size: medium;" expand="full"
          [disabled]="!selectedUrl || !emailId || !password" 
          (click)="checkAndLogin()">Login</ion-button>
        </div>
      </div>



      <!-- <ion-card class="email">
        <ion-item lines="none">
          <ion-label position="floating">{{ "Email" | translate }}</ion-label>
          <ion-input type="email" (keyup.enter)="(!selectedUrl || !emailId || !password) ? '' :login()"
            [(ngModel)]="emailId" placeholder="{{'Enter Email' | translate }}"
            (ionChange)="onChangeDisableErrorMsg($event)">
          </ion-input>
        </ion-item>
      </ion-card>

      <ion-card class=password>
        <ion-item lines="none">
          <ion-label position="floating">{{ "Password" | translate }}</ion-label>
          <ion-input type="password" (keyup.enter)="(!selectedUrl || !emailId || !password) ? '' :login()"
            [(ngModel)]="password" placeholder="{{'Enter Password' | translate }}"
            (ionChange)="onChangeDisableErrorMsg($event)"></ion-input>
        </ion-item>
      </ion-card>

      <a href="" class="forgotPassword" (click)="forgetPassword($event)">{{ "Forgot Password?" | translate }}</a>
      <ion-button type="submit" (click)="login()" [disabled]="!selectedUrl || !emailId || !password"
        class="login-button">
        {{"Login" | translate }}
      </ion-button> -->
    </div>


    <div class="versionBlock" *ngIf="versionHide">
      <div class="version">Version: {{appVersion}}</div>
    </div>
  </div>