<ion-header>
  <ion-toolbar class="header-toolbar" [ngClass]="{'hybridToolbar': isHybridNative}">
    <ion-buttons slot="start">
      <ion-icon name="arrow-back-outline" (click)="goBack()" style="color:white;font-size: 25px;
    padding-left: 10px;"></ion-icon>
    </ion-buttons>
    <ion-title style="color: white !important;">{{ "Take Picture" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="camera-preview-ion-content" padding>

  <div *ngIf="!isAndroidBrowser" style="background: transparent; width: 100%; height: 90%;">
  </div>

  <div *ngIf="isAndroidBrowser" style="width: 100%; height: 100%;">
    <video id="browserVideo" autoplay="true" style="width: 100%;height: 100%;"></video>
  </div>

  <ion-fab vertical="bottom" horizontal="center" slot="fixed">
    <ion-fab-button color="primary" (click)="takePicture()">
      <ion-icon name="aperture"></ion-icon>
    </ion-fab-button>
  </ion-fab>
  <ion-fab vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button color="primary" (click)="switchCamera()">
      <ion-icon name="camera-reverse"></ion-icon>
    </ion-fab-button>
  </ion-fab>
  <ion-fab vertical="bottom" horizontal="start">
    <ion-fab-button color="primary">
      <ion-icon *ngIf='flashMode == "on"' name="flash" class="custom-icon"></ion-icon>
      <ion-icon *ngIf='flashMode == "auto"' name="flash"></ion-icon><span *ngIf='flashMode == "auto"'
        style="font-size: x-small;">A</span>
      <ion-icon *ngIf='flashMode == "off"' name="flash-off"></ion-icon>
    </ion-fab-button>
    <ion-fab-list side="top">
      <ion-fab-button color="primary" (click)="setFlash('on')">
        <ion-icon name="flash" class="custom-icon"></ion-icon>
      </ion-fab-button>
      <ion-fab-button color="primary">
        <ion-icon name="flash" (click)="setFlash('auto')"></ion-icon><span style="font-size: x-small;">A</span>
      </ion-fab-button>
      <ion-fab-button color="primary">
        <ion-icon name="flash-off" (click)="setFlash('off')"></ion-icon>
      </ion-fab-button>
    </ion-fab-list>
  </ion-fab>
</ion-content>
