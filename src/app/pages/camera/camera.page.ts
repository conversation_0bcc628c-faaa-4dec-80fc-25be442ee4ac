import { Component, OnInit } from '@angular/core';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { CameraPreview, CameraPreviewOptions, CameraPreviewPictureOptions } from '@awesome-cordova-plugins/camera-preview/ngx';
import { MenuController, Platform } from '@ionic/angular';
import { Router, ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Location } from '@angular/common';
import { ScreenOrientation } from '@awesome-cordova-plugins/screen-orientation/ngx';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { ShareDataService } from 'src/app/services/ShareData.service';

@Component({
  selector: 'app-camera',
  templateUrl: './camera.page.html',
  styleUrls: ['./camera.page.scss'],
})
export class CameraPage implements OnInit {

  cameraZoom: any;
  currentZoom: any;
  MAX_ZOOM: any;
  isZooming: boolean;
  mode: any;
  pageComponent: any;
  abrMode: any;
  maxRating: any;
  productName: any;
  isDemo: any;
  currentRating: any;
  flashMode: string = "off"
  controlId: any;
  isAndroidBrowser = false;

  public devicePlatform: string = "";
  public isHybridNative: boolean;

  constructor(
    public file: File,
    public cameraPreview: CameraPreview,
    public router: Router,
    public route: ActivatedRoute,
    public translate: TranslateService,
    private location: Location,
    private screenOrientation: ScreenOrientation,
    private platform: Platform,
    private sharedData: ShareDataService,
    private device: Device

  ) {

    this.route.queryParams.subscribe(params => {
      this.controlId = params['id'];
    });
  }

  ngOnInit() {
    let isbrowser = (this.device.platform === 'browser') ? true : false;
    let isAndroid = this.platform.is('android');
    this.isAndroidBrowser = isAndroid && isbrowser
    // set to landscape
    this.screenOrientation.lock(this.screenOrientation.ORIENTATIONS.PORTRAIT);
    // detect orientation changes
    this.screenOrientation.onChange().subscribe(
      () => {
        console.log("Orientation Changed");
      }
    );
    setTimeout(() => {
      this.launchCamera();
    }, 500);

    this.devicePlatform = this.sharedData.getDevicePlatform();
    this.devicePlatform = this.devicePlatform === "" ? "browser" : this.devicePlatform; // on reload in browser
    this.isHybridNative = this.devicePlatform === "browser" ? false : true;
  }

  launchCamera() {
    const cameraPreviewOpts: CameraPreviewOptions = {
      x: 0,
      y: 0,
      width: window.screen.width,
      height: window.screen.height,
      camera: "rear",
      tapPhoto: true,
      previewDrag: false,
      toBack: true,
      alpha: 1,
      storeToFile: false
    }
    // start camera
    this.cameraPreview.startCamera(cameraPreviewOpts).then(
      (res) => {
        this.currentZoom = 0;
        this.getMaxZoom();
        console.log(res)
      },
      (err) => {
        console.log(err)
      });
  }

  getMaxZoom() {
    this.cameraPreview.getMaxZoom().then((value) => {
      this.MAX_ZOOM = value
    }, (error) => {
      console.log("getMaxZoom error: " + error)
    });
    this.cameraPreview.getZoom().then((value) => {
      this.currentZoom = value
    }, (error) => {
      console.log("getMaxZoom error: " + error)
    });
    this.cameraPreview.getFlashMode().then((value) => {
      this.flashMode = value
    }, (error) => {
      console.log("getMaxZoom error: " + error)
    });
  }

  switchCamera() {
    this.cameraPreview.switchCamera().then((value) => {
      this.cameraPreview.getZoom().then((value) => {
        console.log(value)
      }, (error) => {
        console.log("getMaxZoom error: " + error)
      });
    }, (error) => {
      console.log("getMaxZoom error: " + error)
    });
  }

  setFlash(flashMode) {
    console.log(flashMode)
    this.flashMode = flashMode
    this.cameraPreview.setFlashMode(flashMode)
  }

  takePicture() {
    var pictureOpts: CameraPreviewPictureOptions = {
      width: 1280,
      height: 1280,
      quality: 100
    }
    var picture = ''
    // var eventCustom :any
    this.cameraPreview.takePicture(pictureOpts).then((imageData) => {
      console.log("imageData = " + imageData);
      if (imageData[0]) {
        picture = 'file://' + imageData[0]
      } else {
        picture = 'file://' + imageData
      }
      console.log("capture imageData = " + imageData);
      let eventCustom = new CustomEvent('capureImageData', {
        detail: {
          imageData: picture,
          controlId: this.controlId
        }
      });
      document.dispatchEvent(eventCustom);
      this.goBack();
    }, (browserData) => {
      if (browserData instanceof Blob) {
        let eventCustom = new CustomEvent('capureImageDataForAndroidBrowser', {
          detail: {
            imageData: browserData,
            controlId: this.controlId
          }
        });
        document.dispatchEvent(eventCustom);
        this.goBack();
      }
    });
  }

  writePicture(imagen) {
    let realData = imagen.split(",")[1];
    let blob = this.b64toBlob(realData, 'image/jpeg');
    return blob
  }

  b64toBlob(b64Data, contentType) {
    contentType = contentType || '';
    var sliceSize = 512;
    var byteCharacters = atob(b64Data);
    var byteArrays = [];

    for (var offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      var slice = byteCharacters.slice(offset, offset + sliceSize);

      var byteNumbers = new Array(slice.length);
      for (var i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      var byteArray = new Uint8Array(byteNumbers);

      byteArrays.push(byteArray);
    }

    var blob = new Blob(byteArrays, { type: contentType });
    return blob;
  }

  ionViewDidLeave() {
    this.cameraPreview.stopCamera();
    // allow user rotate
    this.screenOrientation.unlock();
    this.sharedData.setNavigateFromCameraPage(true);

  }

  goBack() {
    this.location.back();
    this.sharedData.setNavigateFromCameraPage(true)
  }
}
