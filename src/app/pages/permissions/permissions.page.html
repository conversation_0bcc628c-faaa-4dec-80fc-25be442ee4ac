<ion-header>
  <ion-toolbar class="header-toolbar">
    <ion-title>Required Permissions</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div style="margin: 20px !important;">

    <p>App requires the following permissions please allow to continue.</p>

    <p *ngFor="let permissionName of permissions; let i = index "> {{(i + 1)}}. {{permissionName}} </p>

    <p>Allow the permissions when prompted. If the the permissions are not prompted enable permissions from settings.
    </p>
    <p>Settings > Apps > select safe work order app > Permissions > Enable all permissions and tap Allow to continue.
    </p>

    <div style="width: 100% !important; text-align: center !important;">
      <ion-button style="width: 45% !important;" (click)="checkHasPermissions()">Allow</ion-button>
      <ion-button style="width: 45% !important;" (click)="exitApp()">Exit</ion-button>
    </div>
  </div>

</ion-content>