ion-content {
    --background: var(--ion-color-light);
}

ion-toolbar {
    padding-top: 20px !important;
}

// Fix content alignment for mobile permissions page
:host {
    // Override global empty-data-info styles that cause bottom alignment
    .empty-data-info {
        padding-top: 40px !important; // Override global 25vh
        position: static !important;
        transform: none !important;
        top: auto !important;
        bottom: auto !important;
        left: auto !important;
        right: auto !important;
    }

    // Fix scroll containers while preserving scrolling
    .scroll-content,
    .inner-scroll,
    .scroll-y {
        position: relative !important;
        top: 0 !important;
        transform: none !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
    }

    // Override any global styles that might affect positioning
    ion-list {
        margin-top: 0 !important;
        padding-top: 0 !important;
        position: static !important;
        transform: none !important;
    }

    // Ensure content containers start from top but preserve scrolling
    ion-content {
        position: relative !important;
        top: 0 !important;
        transform: none !important;
        overflow: auto !important;

        .content-area {
            position: static !important;
            top: 0 !important;
            transform: none !important;
        }

        // Ensure the scroll area works properly
        .scroll-content {
            overflow-y: auto !important;
            height: auto !important;
        }
    }
}