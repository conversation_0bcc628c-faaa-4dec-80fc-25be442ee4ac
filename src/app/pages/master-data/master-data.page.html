<ion-content class="ion-padding-top">
  <!-- Tabs navigation with styling to match the sample picture -->
  <ion-segment [(ngModel)]="selectedTab" (ionChange)="segmentChanged($event)" mode="md" scrollable="false">
    <ion-segment-button value='structureTypes'>
      <ion-label>Structure Types</ion-label>
    </ion-segment-button>
    <ion-segment-button value='structureCategories'>
      <ion-label>Structure Categories</ion-label>
    </ion-segment-button>
    <ion-segment-button value='structureStatus'>
      <ion-label>Structure Status</ion-label>
    </ion-segment-button>
    <ion-segment-button value='approvalTypes'>
      <ion-label>Approval Types</ion-label>
    </ion-segment-button>
    <ion-segment-button value='permitTypes'>
      <ion-label>Permit Types</ion-label>
    </ion-segment-button>
    <ion-segment-button value='roles'>
      <ion-label>Roles</ion-label>
    </ion-segment-button>
    <ion-segment-button value='skill'>
      <ion-label>Skills</ion-label>
    </ion-segment-button>
  </ion-segment>

  <!-- Loading state with shimmer effect -->
  <div *ngIf="isShowProgressBar" class="shimmer-container">
    <!-- Shimmer for search card -->
    <div class="shimmer-header-card">
      <div class="shimmer-title"></div>
      <div class="shimmer-actions">
        <div class="shimmer-button"></div>
      </div>
    </div>

    <!-- Shimmer for table -->
    <div class="shimmer-card">
      <div class="shimmer-header"></div>
      <div class="shimmer-row" *ngFor="let i of [1,2,3,4,5,6,7]"></div>
    </div>
  </div>

  <div [ngSwitch]="selectedTab">

    <!--#region Structure Type-->
    <div *ngIf="selectedTab == 'structureTypes' && !isShowProgressBar">
      <!-- Search card -->
      <div class="search-card">
        <div class="search-wrapper">
          <ion-icon name="search-outline"></ion-icon>
          <input type="text" placeholder="Search structure types..." [(ngModel)]="searchTerm" (input)="filterData()">
        </div>
        <div class="action-buttons">
          <button class="add-btn" (click)="openSelectedMaterDataDetailsModal({}, 0, null)">
            <ion-icon name="add-outline"></ion-icon>
            Add Type
          </button>
        </div>
      </div>

      <ion-card mode="md">
        <ion-grid class="header-card-grid">
          <ion-row>
            <ion-col size="3" class="ion-col-left-padding">
              <p class="header-card-grid-p">Code</p>
            </ion-col>
            <ion-col>
              <p class="header-card-grid-p">Description</p>
            </ion-col>
            <ion-col size="1.5" class="icon-button-col">
              <!-- Action buttons moved to search card -->
            </ion-col>
          </ion-row>
        </ion-grid>

        <!-- Data rows -->
        <div *ngIf="filteredStructureTypesList.length > 0">
          <ion-grid class="body-card-grid" *ngFor="let type of filteredStructureTypesList; let i = index;">
            <ion-row>
              <ion-col size="3" class="ion-col-left-padding">
                <p class="body-card-grid-p"><span class="badge badge-primary">{{type.STRUCT_TYPE}}</span></p>
              </ion-col>
              <ion-col>
                <p class="body-card-grid-p">{{type.DESCRIPTION}}</p>
              </ion-col>
              <ion-col size="1.5" class="icon-button-col">
                <ion-button fill="clear" color="danger" (click)="deleteSelectedItem(type, 'structureTypes');$event.stopPropagation();">
                  <ion-icon name="trash-outline" slot="icon-only"></ion-icon>
                </ion-button>
                <ion-button fill="clear" color="primary" (click)="openSelectedMaterDataDetailsModal(type, 1, i)">
                  <ion-icon name="create-outline" slot="icon-only"></ion-icon>
                </ion-button>
              </ion-col>
            </ion-row>
          </ion-grid>
        </div>

        <!-- Empty state -->
        <div *ngIf="filteredStructureTypesList.length == 0" class="empty-data-info">
          <ion-icon name="cube-outline"></ion-icon>
          <p>No Structure Types Available</p>
          <p>Click the + button to add a new structure type</p>
        </div>
      </ion-card>
    </div>
    <!--#endregion-->

    <!--#region Structure Categories-->
    <div *ngIf="selectedTab == 'structureCategories' && !isShowProgressBar">
      <!-- Search card -->
      <div class="search-card">
        <div class="search-wrapper">
          <ion-icon name="search-outline"></ion-icon>
          <input type="text" placeholder="Search structure categories..." [(ngModel)]="searchTerm" (input)="filterData()">
        </div>
        <div class="action-buttons">
          <button class="add-btn" (click)="openSelectedMaterDataDetailsModal({}, 0, null)">
            <ion-icon name="add-outline"></ion-icon>
            Add Category
          </button>
        </div>
      </div>

      <ion-card mode="md">
        <ion-grid class="header-card-grid">
          <ion-row>
            <ion-col size="3" class="ion-col-left-padding">
              <p class="header-card-grid-p">Code</p>
            </ion-col>
            <ion-col>
              <p class="header-card-grid-p">Description</p>
            </ion-col>
            <ion-col size="1.5" class="icon-button-col">
              <!-- Action buttons moved to search card -->
            </ion-col>
          </ion-row>
        </ion-grid>

        <!-- Data rows -->
        <div *ngIf="filteredStructureCategoriesList.length > 0">
          <ion-grid class="body-card-grid" *ngFor="let categories of filteredStructureCategoriesList; let i = index;">
            <ion-row>
              <ion-col size="3" class="ion-col-left-padding">
                <p class="body-card-grid-p"><span class="badge badge-success">{{categories.CATEGORY}}</span></p>
              </ion-col>
              <ion-col>
                <p class="body-card-grid-p">{{categories.DESCRIPTION}}</p>
              </ion-col>
              <ion-col size="1.5" class="icon-button-col">
                <ion-button fill="clear" color="danger" (click)="deleteSelectedItem(categories, 'structureCategories');$event.stopPropagation();">
                  <ion-icon name="trash-outline" slot="icon-only"></ion-icon>
                </ion-button>
                <ion-button fill="clear" color="primary" (click)="openSelectedMaterDataDetailsModal(categories, 1, i)">
                  <ion-icon name="create-outline" slot="icon-only"></ion-icon>
                </ion-button>
              </ion-col>
            </ion-row>
          </ion-grid>
        </div>

        <!-- Empty state -->
        <div *ngIf="filteredStructureCategoriesList.length == 0" class="empty-data-info">
          <ion-icon name="folder-outline"></ion-icon>
          <p>No Structure Categories Available</p>
          <p>Click the + button to add a new category</p>
        </div>
      </ion-card>
    </div>
    <!--#endregion-->

    <!--#region Structure Status-->
    <div *ngIf="selectedTab == 'structureStatus' && !isShowProgressBar">
      <!-- Search card -->
      <div class="search-card">
        <div class="search-wrapper">
          <ion-icon name="search-outline"></ion-icon>
          <input type="text" placeholder="Search structure status..." [(ngModel)]="searchTerm" (input)="filterData()">
        </div>
        <div class="action-buttons">
          <button class="add-btn" (click)="openSelectedMaterDataDetailsModal({}, 0, null)">
            <ion-icon name="add-outline"></ion-icon>
            Add Status
          </button>
        </div>
      </div>

      <ion-card mode="md">
        <ion-grid class="header-card-grid">
          <ion-row>
            <ion-col size="3" class="ion-col-left-padding">
              <p class="header-card-grid-p">Code</p>
            </ion-col>
            <ion-col>
              <p class="header-card-grid-p">Description</p>
            </ion-col>
            <ion-col size="2" style="text-align:center">
              <p class="header-card-grid-p">Is Active</p>
            </ion-col>
            <ion-col size="1.5" class="icon-button-col">
              <!-- Action buttons moved to search card -->
            </ion-col>
          </ion-row>
        </ion-grid>

        <!-- Data rows -->
        <div *ngIf="filteredStructureStatusList.length > 0">
          <ion-grid class="body-card-grid" *ngFor="let status of filteredStructureStatusList; let i = index;">
            <ion-row>
              <ion-col size="3" class="ion-col-left-padding">
                <p class="body-card-grid-p"><span class="badge badge-info">{{status.STATUS}}</span></p>
              </ion-col>
              <ion-col>
                <p class="body-card-grid-p">{{status.DESCRIPTION}}</p>
              </ion-col>
              <ion-col size="2" class="textCenter">
                <ion-toggle mode="md" color="primary" [(ngModel)]="status.IS_ACTIVE" [checked]="status.IS_ACTIVE"
                  (ionChange)="structureStatusIsActiveChange(status)" class="small-toggle">
                </ion-toggle>
              </ion-col>
              <ion-col size="1.5" class="icon-button-col">
                <ion-button fill="clear" color="danger" (click)="deleteSelectedItem(status, 'structureStatus');$event.stopPropagation();">
                  <ion-icon name="trash-outline" slot="icon-only"></ion-icon>
                </ion-button>
                <ion-button fill="clear" color="primary" (click)="openSelectedMaterDataDetailsModal(status, 1, i)">
                  <ion-icon name="create-outline" slot="icon-only"></ion-icon>
                </ion-button>
              </ion-col>
            </ion-row>
          </ion-grid>
        </div>

        <!-- Empty state -->
        <div *ngIf="filteredStructureStatusList.length == 0" class="empty-data-info">
          <ion-icon name="options-outline"></ion-icon>
          <p>No Structure Status Available</p>
          <p>Click the + button to add a new status</p>
        </div>
      </ion-card>
    </div>
    <!--#endregion-->

    <!--#region Roles-->
    <div *ngIf="selectedTab == 'roles' && !isShowProgressBar">
      <!-- Search card -->
      <div class="search-card">
        <div class="search-wrapper">
          <ion-icon name="search-outline"></ion-icon>
          <input type="text" placeholder="Search roles..." [(ngModel)]="searchTerm" (input)="filterData()">
        </div>
        <div class="action-buttons">
          <button class="add-btn" (click)="openSelectedMaterDataDetailsModal({}, 0, null)">
            <ion-icon name="add-outline"></ion-icon>
            Add Role
          </button>
          <button class="action-btn" (click)="saveRoles()">
            <ion-icon name="save-outline"></ion-icon>
            Save Roles
          </button>
        </div>
      </div>

      <ion-card mode="md">
        <ion-grid class="header-card-grid">
          <ion-row>
            <ion-col size="1.6">
              <p class="header-card-grid-p">Role Name</p>
            </ion-col>
            <ion-col class="textCenter" size="0.5">
              <p class="header-card-grid-p">Internal</p>
            </ion-col>
            <ion-col class="textCenter" size="0.9">
              <p class="header-card-grid-p">Config</p>
            </ion-col>
            <ion-col class="textCenter" size="0.9">
              <p class="header-card-grid-p">Facility</p>
            </ion-col>
            <ion-col class="textCenter" size="0.9">
              <p class="header-card-grid-p">Users</p>
            </ion-col>
            <ion-col class="textCenter" size="0.9">
              <p class="header-card-grid-p">Agents</p>
            </ion-col>
            <ion-col class="textCenter" size="0.9">
              <p class="header-card-grid-p">Procedures</p>
            </ion-col>
            <ion-col class="textCenter" size="0.6">
              <p class="header-card-grid-p">Request</p>
            </ion-col>
            <ion-col class="textCenter" size="0.5">
              <p class="header-card-grid-p">Review</p>
            </ion-col>
            <ion-col class="textCenter" size="0.6">
              <p class="header-card-grid-p">Approve</p>
            </ion-col>
            <ion-col class="textCenter" size="0.5">
              <p class="header-card-grid-p">Issue</p>
            </ion-col>
            <ion-col class="textCenter" size="0.5">
              <p class="header-card-grid-p"> Extend / Revise </p>
            </ion-col>
            <ion-col class="textCenter" size="0.5">
              <p class="header-card-grid-p">Execute</p>
            </ion-col>
            <ion-col class="textCenter" size="0.5">
              <p class="header-card-grid-p">Close</p>
            </ion-col>
            <ion-col class="textCenter" size="0.5">
              <p class="header-card-grid-p">Cancel</p>
            </ion-col>
            <ion-col class="textCenter" size="0.5">
              <p class="header-card-grid-p">Report</p>
            </ion-col>
            <ion-col class="textCenter" size="0.4">
              <!-- Action buttons moved to search card -->
            </ion-col>
          </ion-row>
        </ion-grid>

        <!-- Data rows -->
        <div *ngIf="filteredRolesList.length > 0">
          <ion-grid class="body-card-grid" *ngFor="let role of filteredRolesList; let i = index;">
            <ion-row>
              <ion-col size="1.6">
                <p class="body-card-grid-p"><span class="badge badge-danger">{{role.ROLE_NAME}}</span></p>
              </ion-col>
              <ion-col class="textCenter" size="0.5">
                <ion-toggle mode="md" color="primary" (click)="roleChange(role)" [(ngModel)]="role.IS_INTERNAL"
                  [checked]="role.IS_INTERNAL" class="small-toggle">
                </ion-toggle>
              </ion-col>
              <ion-col class="textCenter" size="0.9">
                <ion-toggle mode="md" color="primary" (click)="roleChange(role)" [(ngModel)]="role.CONFIGURATION"
                  [checked]="role.CONFIGURATION" class="small-toggle">
                </ion-toggle>
              </ion-col>
              <ion-col class="textCenter" size="0.9">
                <ion-toggle mode="md" color="primary" (click)="roleChange(role)" [(ngModel)]="role.FACILITY_MGMT"
                  [checked]="role.FACILITY_MGMT" class="small-toggle">
                </ion-toggle>
              </ion-col>
              <ion-col class="textCenter" size="0.9">
                <ion-toggle mode="md" color="primary" (click)="roleChange(role)" [(ngModel)]="role.USER_MGMT"
                  [checked]="role.USER_MGMT" class="small-toggle">
                </ion-toggle>
              </ion-col>
              <ion-col class="textCenter" size="0.9">
                <ion-toggle mode="md" color="primary" (click)="roleChange(role)" [(ngModel)]="role.AGENT_MGMT"
                  [checked]="role.AGENT_MGMT" class="small-toggle">
                </ion-toggle>
              </ion-col>
              <ion-col class="textCenter" size="0.9">
                <ion-toggle mode="md" color="primary" (click)="roleChange(role)" [(ngModel)]="role.PROC_MGMT"
                  [checked]="role.PROC_MGMT" class="small-toggle">
                </ion-toggle>
              </ion-col>
              <ion-col class="textCenter" size="0.6">
                <ion-toggle mode="md" color="primary" (click)="roleChange(role)" [(ngModel)]="role.REQUEST"
                  [checked]="role.REQUEST" class="small-toggle">
                </ion-toggle>
              </ion-col>
              <ion-col class="textCenter" size="0.5">
                <ion-toggle mode="md" color="primary" (click)="roleChange(role)" [(ngModel)]="role.REVIEW"
                  [checked]="role.REVIEW" class="small-toggle">
                </ion-toggle>
              </ion-col>
              <ion-col class="textCenter" size="0.6">
                <ion-toggle mode="md" color="primary" (click)="roleChange(role)" [(ngModel)]="role.APPROVE"
                  [checked]="role.APPROVE" class="small-toggle">
                </ion-toggle>
              </ion-col>
              <ion-col class="textCenter" size="0.5">
                <ion-toggle mode="md" color="primary" (click)="roleChange(role)" [(ngModel)]="role.ISSUE"
                  [checked]="role.ISSUE" class="small-toggle">
                </ion-toggle>
              </ion-col>
              <ion-col class="textCenter" size="0.5">
                <ion-toggle mode="md" color="primary" (click)="roleChange(role)" [(ngModel)]="role.EXTEND"
                  [checked]="role.EXTEND" class="small-toggle">
                </ion-toggle>
              </ion-col>
              <ion-col class="textCenter" size="0.5">
                <ion-toggle mode="md" color="primary" (click)="roleChange(role)" [(ngModel)]="role.EXECUTE"
                  [checked]="role.EXECUTE" class="small-toggle">
                </ion-toggle>
              </ion-col>
              <ion-col class="textCenter" size="0.5">
                <ion-toggle mode="md" color="primary" (click)="roleChange(role)" [(ngModel)]="role.CLOSE"
                  [checked]="role.CLOSE" class="small-toggle">
                </ion-toggle>
              </ion-col>
              <ion-col class="textCenter" size="0.5">
                <ion-toggle mode="md" color="primary" (click)="roleChange(role)" [(ngModel)]="role.CANCEL"
                  [checked]="role.CANCEL" class="small-toggle">
                </ion-toggle>
              </ion-col>
              <ion-col class="textCenter" size="0.5">
                <ion-toggle mode="md" color="primary" (click)="roleChange(role)" [(ngModel)]="role.REPORT"
                  [checked]="role.REPORT" class="small-toggle">
                </ion-toggle>
              </ion-col>
              <ion-col class="textCenter" size="0.4">
                <ion-button fill="clear" color="danger" (click)="deleteSelectedItem(role, 'roles');$event.stopPropagation();">
                  <ion-icon name="trash-outline" slot="icon-only"></ion-icon>
                </ion-button>
              </ion-col>
            </ion-row>
          </ion-grid>
        </div>

        <!-- Empty state -->
        <div *ngIf="filteredRolesList.length == 0" class="empty-data-info">
          <ion-icon name="people-outline"></ion-icon>
          <p>No Roles Available</p>
          <p>Click the + button to add a new role</p>
        </div>
      </ion-card>
    </div>
    <!--#endregion-->

    <!--#region Permit Types-->
    <div *ngIf="selectedTab == 'permitTypes' && !isShowProgressBar">
      <!-- Search card -->
      <div class="search-card">
        <div class="search-wrapper">
          <ion-icon name="search-outline"></ion-icon>
          <input type="text" placeholder="Search permit types..." [(ngModel)]="searchTerm" (input)="filterData()">
        </div>
        <div class="action-buttons">
          <button class="add-btn" (click)="openSelectedMaterDataDetailsModal({}, 0, null)">
            <ion-icon name="add-outline"></ion-icon>
            Add Permit Type
          </button>
        </div>
      </div>

      <ion-card mode="md">
        <ion-grid class="header-card-grid">
          <ion-row>
            <ion-col size="2" class="ion-col-left-padding">
              <p class="header-card-grid-p">Type</p>
            </ion-col>
            <ion-col size="3">
              <p class="header-card-grid-p">Description</p>
            </ion-col>
            <ion-col size="1" class="ion-col-left-padding">
              <p class="header-card-grid-p">Prefix</p>
            </ion-col>
            <ion-col size="2.5" class="ion-col-left-padding">
              <p class="header-card-grid-p">Form ID</p>
            </ion-col>
            <ion-col size="2" class="ion-col-left-padding">
              <p class="header-card-grid-p">Form Name</p>
            </ion-col>
            <ion-col size="1.5" class="icon-button-col">
              <!-- Action buttons moved to search card -->
            </ion-col>
          </ion-row>
        </ion-grid>

        <!-- Data rows -->
        <div *ngIf="filteredPermitTypesList.length > 0">
          <ion-grid class="body-card-grid" *ngFor="let type of filteredPermitTypesList; let i = index;">
            <ion-row>
              <ion-col size="2" class="ion-col-left-padding">
                <p class="body-card-grid-p"><span class="badge badge-primary">{{type.PERMIT_TYPE}}</span></p>
              </ion-col>
              <ion-col size="3">
                <p class="body-card-grid-p">
                  <span class="permit-type-with-icon">
                    <i *ngIf="type.ICON" class="fas fa-{{type.ICON}}" [style.color]="getColorHex(type.COLOR)" style="margin-right: 8px;"></i>
                    <i *ngIf="!type.ICON" class="fas fa-question-circle" style="margin-right: 8px; color: #3880ff;"></i>
                    {{type.DESCRIPTION}}
                  </span>
                </p>
              </ion-col>
              <ion-col size="1" class="ion-col-left-padding">
                <p class="body-card-grid-p"><span class="badge badge-info">{{type.PREFIX}}</span></p>
              </ion-col>
              <ion-col size="2.5" class="ion-col-left-padding">
                <p class="body-card-grid-p">{{type.FORM_ID}}</p>
              </ion-col>
              <ion-col size="2" class="ion-col-left-padding">
                <p class="body-card-grid-p"><span class="badge badge-success">{{type.FORM_NAME}}</span></p>
              </ion-col>
              <ion-col size="1.5" class="icon-button-col">
                <ion-button fill="clear" color="danger" (click)="deleteSelectedItem(type, 'permitTypes');$event.stopPropagation();">
                  <ion-icon name="trash-outline" slot="icon-only"></ion-icon>
                </ion-button>
                <ion-button fill="clear" color="primary" (click)="openSelectedMaterDataDetailsModal(type, 1, i)">
                  <ion-icon name="create-outline" slot="icon-only"></ion-icon>
                </ion-button>
              </ion-col>
            </ion-row>
          </ion-grid>
        </div>

        <!-- Empty state -->
        <div *ngIf="filteredPermitTypesList.length == 0" class="empty-data-info">
          <ion-icon name="document-outline"></ion-icon>
          <p>No Permit Types Available</p>
          <p>Click the + button to add a new permit type</p>
        </div>
      </ion-card>
    </div>
    <!--#endregion-->

    <!--#region Approval Types-->
    <div *ngIf="selectedTab == 'approvalTypes' && !isShowProgressBar">
      <!-- Search card -->
      <div class="search-card">
        <div class="search-wrapper">
          <ion-icon name="search-outline"></ion-icon>
          <input type="text" placeholder="Search approval types..." [(ngModel)]="searchTerm" (input)="filterData()">
        </div>
        <div class="action-buttons">
          <button class="add-btn" (click)="openSelectedMaterDataDetailsModal({}, 0, null)">
            <ion-icon name="add-outline"></ion-icon>
            Add Approval Type
          </button>
        </div>
      </div>

      <ion-card mode="md">
        <ion-grid class="header-card-grid">
          <ion-row>
            <ion-col size="2" class="ion-col-left-padding">
              <p class="header-card-grid-p">Type</p>
            </ion-col>
            <ion-col size="2" class="ion-col-left-padding">
              <p class="header-card-grid-p">Scope</p>
            </ion-col>
            <ion-col>
              <p class="header-card-grid-p">Description</p>
            </ion-col>
            <ion-col size="1.5" class="icon-button-col">
              <!-- Action buttons moved to search card -->
            </ion-col>
          </ion-row>
        </ion-grid>

        <!-- Data rows -->
        <div *ngIf="filteredApprovalTypesList.length > 0">
          <ion-grid class="body-card-grid" *ngFor="let type of filteredApprovalTypesList; let i = index;">
            <ion-row>
              <ion-col size="2" class="ion-col-left-padding">
                <p class="body-card-grid-p"><span class="badge badge-warning">{{type.APPR_TYPE}}</span></p>
              </ion-col>
              <ion-col size="2" class="ion-col-left-padding">
                <p class="body-card-grid-p"><span class="badge badge-secondary">{{type.SCOPE}}</span></p>
              </ion-col>
              <ion-col>
                <p class="body-card-grid-p">{{type.DESCRIPTION}}</p>
              </ion-col>
              <ion-col size="1.5" class="icon-button-col">
                <ion-button fill="clear" color="danger" (click)="deleteSelectedItem(type, 'approvalTypes');$event.stopPropagation();">
                  <ion-icon name="trash-outline" slot="icon-only"></ion-icon>
                </ion-button>
                <ion-button fill="clear" color="primary" (click)="openSelectedMaterDataDetailsModal(type, 1, i)">
                  <ion-icon name="create-outline" slot="icon-only"></ion-icon>
                </ion-button>
              </ion-col>
            </ion-row>
          </ion-grid>
        </div>

        <!-- Empty state -->
        <div *ngIf="filteredApprovalTypesList.length == 0" class="empty-data-info">
          <ion-icon name="checkmark-circle-outline"></ion-icon>
          <p>No Approval Types Available</p>
          <p>Click the + button to add a new approval type</p>
        </div>
      </ion-card>
    </div>
    <!--#endregion-->


    <!-- #region skills -->
    <div *ngIf="selectedTab == 'skill' && !isShowProgressBar">
      <!-- Search card -->
      <div class="search-card">
        <div class="search-wrapper">
          <ion-icon name="search-outline"></ion-icon>
          <input type="text" placeholder="Search skills..." [(ngModel)]="searchTerm" (input)="filterData()">
        </div>
        <div class="action-buttons">
          <button class="add-btn" (click)="openSelectedMaterDataDetailsModal({}, 0, null)">
            <ion-icon name="add-outline"></ion-icon>
            Add Skill
          </button>
        </div>
      </div>

      <ion-card mode="md">
        <ion-grid class="header-card-grid">
          <ion-row>
            <ion-col size="3" class="ion-col-left-padding">
              <p class="header-card-grid-p">Skill Type</p>
            </ion-col>
            <ion-col>
              <p class="header-card-grid-p">Description</p>
            </ion-col>
            <ion-col size="1.5" class="icon-button-col">
              <!-- Action buttons moved to search card -->
            </ion-col>
          </ion-row>
        </ion-grid>

        <!-- Data rows -->
        <div *ngIf="filteredSkillsList.length > 0">
          <ion-grid class="body-card-grid" *ngFor="let skills of filteredSkillsList; let i = index;">
            <ion-row>
              <ion-col size="3" class="ion-col-left-padding">
                <p class="body-card-grid-p"><span class="badge badge-warning">{{skills.SKILL_TYPE}}</span></p>
              </ion-col>
              <ion-col>
                <p class="body-card-grid-p">{{skills.DESCRIPTION}}</p>
              </ion-col>
              <ion-col size="1.5" class="icon-button-col">
                <ion-button fill="clear" color="danger" (click)="deleteSelectedItem(skills, 'skill');$event.stopPropagation();">
                  <ion-icon name="trash-outline" slot="icon-only"></ion-icon>
                </ion-button>
                <ion-button fill="clear" color="primary" (click)="openSelectedMaterDataDetailsModal(skills, 1, i)">
                  <ion-icon name="create-outline" slot="icon-only"></ion-icon>
                </ion-button>
              </ion-col>
            </ion-row>
          </ion-grid>
        </div>

        <!-- Empty state -->
        <div *ngIf="filteredSkillsList.length == 0" class="empty-data-info">
          <ion-icon name="construct-outline"></ion-icon>
          <p>No Skills Available</p>
          <p>Click the + button to add a new skill</p>
        </div>
      </ion-card>
    </div>
    <!-- end region skills -->
  </div>
</ion-content>