/* Background styling for the page */
:host {
  background: linear-gradient(to bottom, rgb(245, 246, 248) 0%, rgb(255, 255, 255) 100%);
  display: block;
  height: 100%;
  overflow-y: auto;
}

/* Content styling for proper scrolling */
:host ion-content {
  --background: transparent;
  --padding-top: 16px;
  --padding-start: 16px; /* Add 16px padding on left */
  --padding-end: 16px; /* Add 16px padding on right */
  --padding-bottom: 16px;
  --offset-top: 0 !important;
  height: 100%;
}

/* Page header styling */
.page-header {
  margin: 0 15px 20px;
  width: calc(100% - 30px);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

/* Search card styling */
.search-card {
  background-color: white;
  border-radius: 0;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 12px 16px;
  margin: 0; /* No horizontal margin needed as padding is on the content */
  width: 100%; /* Match full width of tabs */
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  border-radius: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.search-wrapper {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 0 12px;
  flex: 1;
  max-width: 400px;
}

.search-wrapper input {
  border: none;
  background: transparent;
  padding: 10px;
  width: 100%;
  font-size: 14px;
  color: #495057;
  outline: none;
}

.search-wrapper ion-icon {
  color: #adb5bd;
  font-size: 18px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background-color: #e9ecef;
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #3880ff;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-btn:hover {
  background-color: #3171e0;
}

.action-btn ion-icon, .add-btn ion-icon {
  font-size: 16px; /* Reduced from 18px */
}

/* Shimmer container */
.shimmer-container {
  padding: 0;
  margin-top: 16px; /* Match the 16px top margin of the tabs */
  position: relative;
  z-index: 1;
  width: 100%;
}

/* Shimmer for header */
.shimmer-header-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  border-radius: 0;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 12px 16px;
  margin: 0 0 0 0;
  width: 100%;
}

.shimmer-title {
  height: 36px;
  width: 200px;
  background: linear-gradient(90deg, #eee 0%, #f5f5f5 50%, #eee 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 6px;
}

.shimmer-actions {
  display: flex;
  gap: 10px;
}

.shimmer-button {
  height: 36px;
  width: 120px;
  background: linear-gradient(90deg, #eee 0%, #f5f5f5 50%, #eee 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 6px;
}

/* Tab styling to match the sample picture */
ion-segment {
  background-color: white;
  margin: 16px 0 0; /* Add 16px top padding */
  width: 100%; /* Fill entire width */
  overflow-x: auto;
  overflow-y: hidden; /* Prevent vertical scrolling */
  border-radius: 8px 8px 0 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
  display: flex;
  height: auto; /* Allow height to adjust based on content */
}

ion-segment-button {
  --color: #6c757d;
  --color-checked: #ffffff;
  --indicator-color: transparent;
  --background: white;
  --background-checked: #3880ff;
  --border-radius: 0;
  --border-color: transparent;
  --border-style: none;
  --border-width: 0;
  --padding-top: 8px; /* Reduced by ~30% from 12px */
  --padding-bottom: 8px; /* Reduced by ~30% from 12px */
  min-width: 120px;
  font-size: 14px;
  font-weight: 500;
  text-transform: none;
  letter-spacing: 0;
  transition: all 0.3s ease;
  flex: 1; /* Make buttons stretch to fill available width */
  height: auto; /* Allow height to adjust based on content */
  white-space: nowrap; /* Prevent text from wrapping */
  overflow: hidden; /* Hide any overflow */
  text-overflow: ellipsis; /* Add ellipsis for overflowing text */
}

/* Table styling */
ion-card {
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  background-color: white;
  border-radius: 0 0 12px 12px !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
  overflow: hidden;
  margin: 0 !important; /* No margin needed to match shimmer */
  width: 100%; /* Match full width of tabs */
  position: relative;
  box-sizing: border-box;
}

/* Header styling */
.header-card-grid {
  padding: 0px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.header-card-grid-p {
  font-weight: 600;
  font-style: normal;
  margin: 0;
  color: #495057;
  font-size: 14px;
}

/* Body styling */
.body-card-grid {
  padding: 0px;
  border-bottom: 1px solid #f1f3f5;
}

.body-card-grid ion-row {
  min-height: 36px;
}

.body-card-grid ion-col {
  display: flex;
  align-items: center;
}

.body-card-grid-p {
  font-style: normal;
  margin: 0;
  color: #212529;
  width: 100%;
}

/* Action buttons styling */
.icon-button-col {
  padding-right: 10px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px; /* Increased gap for better spacing between 16x16 buttons */
}

.add-icon {
  font-size: 16px;
  color: white;
}

/* Default button styling */
ion-button {
  --padding-start: 0px;
  --padding-end: 0px;
  --padding-top: 0px;
  --padding-bottom: 0px;
  height: 16px; /* Set to exactly 16px */
  width: 16px; /* Set to exactly 16px */
  min-width: 16px; /* Ensure fixed width */
  max-width: 16px; /* Ensure fixed width */
}

/* Make the icon inside buttons smaller */
ion-button ion-icon {
  font-size: 16px !important;
}

.edit-icon {
  font-size: 16px !important;
}

/* Specific styling for roles tab buttons */
[value='roles'] ~ div ion-button {
  --padding-start: 0px;
  --padding-end: 0px;
  --padding-top: 0px;
  --padding-bottom: 0px;
  height: 16px;
  width: 16px;
  min-width: 16px;
  max-width: 16px;
}

[value='roles'] ~ div ion-button ion-icon {
  font-size: 16px !important;
}

/* Column styling */
.ion-col-text-center {
  margin: auto;
  text-align: center;
}

.ion-col-left-padding {
  padding-left: 16px !important;
}

/* Empty state styling */
.empty-data-info {
  text-align: center;
  color: #6c757d;
  padding: 60px 20px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-data-info ion-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #adb5bd;
}

/* Toggle and Checkbox styling */
.p-tag-with-checkbox {
  text-align: center !important;
  padding-top: 0;
  line-height: 36px;
}

.textCenter {
  text-align: center;
  height: 100%;
}

/* Ensure the toggle container is centered */
.textCenter ion-toggle {
  margin: 0 auto;
  padding-top: 16px;
}

ion-toggle {
  --handle-width: 14px;
  --handle-height: 14px;
  --handle-max-height: 14px;
  --handle-spacing: 4px;
  width: 36px;
  height: 22px;
  padding: 0;
}

.small-toggle {
  transform: scale(0.8);
}

/* Permit type with icon styling */
.permit-type-with-icon {
  display: flex;
  align-items: center;
}

.permit-type-with-icon i {
  font-size: 16px;
  min-width: 20px; /* Ensure consistent spacing */
}

/* Badge styling */
.badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  background-color: #e9ecef;
  color: #495057;
}

.badge-primary {
  background-color: #e7f1ff;
  color: #0d6efd;
}

.badge-success {
  background-color: #e8f5e9;
  color: #28a745;
}

.badge-info {
  background-color: #e3f2fd;
  color: #0dcaf0;
}

.badge-warning {
  background-color: #fff3cd;
  color: #ffc107;
}

.badge-danger {
  background-color: #f8d7da;
  color: #dc3545;
}

.badge-secondary {
  background-color: #f1f3f5;
  color: #6c757d;
}

p {
  margin-top: revert !important;
  margin-bottom: 1rem !important;
}

/* Shimmer effect for loading state */
.shimmer-card {
  background-color: white;
  border-radius: 0 0 12px 12px;
  padding: 0;
  margin: 0;
  width: 100%;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Shimmer for header */
.shimmer-header {
  height: 36px;
  background: linear-gradient(90deg, #f0f0f0 0%, #f8f8f8 50%, #f0f0f0 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-bottom: 1px solid #e9ecef;
}

.shimmer-row {
  height: 36px;
  background: linear-gradient(90deg, #eee 0%, #f5f5f5 50%, #eee 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-bottom: 1px solid #f1f3f5;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Additional tab styling to ensure proper text display */
ion-segment-button ion-label {
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* Ensure selected tab text is white */
ion-segment-button.segment-button-checked {
  --color: #ffffff !important;
  color: #ffffff !important;
}

ion-segment-button.segment-button-checked ion-label {
  color: #ffffff !important;
}

/* Fix for segment button to ensure full background color */
ion-segment-button::part(indicator-background) {
  height: 100%;
  top: 0;
  bottom: 0;
}

/* Additional scrolling fixes */
:host {
  ion-content {
    --overflow: auto;

    &::part(scroll) {
      overflow-y: auto;
    }
  }
}

/* Responsive design for mobile */
@media (max-width: 768px) {
  :host ion-content {
    --padding-start: 8px; /* Reduce padding on mobile */
    --padding-end: 8px; /* Reduce padding on mobile */
  }

  ion-segment {
    width: 100%;
    margin: 0;
    padding: 0 !important;
  }

  ion-card {
    margin: 0 0 20px !important;
    width: 100%;
  }

  .header-card-grid, .body-card-grid {
    padding: 12px;
  }

  /* Ensure tabs are still visible on small screens */
  ion-segment-button {
    min-width: 100px;
    font-size: 13px;
  }
}