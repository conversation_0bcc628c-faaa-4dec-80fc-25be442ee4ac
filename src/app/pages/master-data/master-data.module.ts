import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { MasterDataPageRoutingModule } from './master-data-routing.module';

import { MasterDataPage } from './master-data.page';
import { TranslateModule } from '@ngx-translate/core';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { MasterDataDetailsComponent } from 'src/app/components/master-data-details/master-data-details.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    MasterDataPageRoutingModule,
    TranslateModule
  ],
  declarations: [MasterDataPage, MasterDataDetailsComponent],
  providers: [UnviredCordovaSDK],
})
export class MasterDataPageModule {}
