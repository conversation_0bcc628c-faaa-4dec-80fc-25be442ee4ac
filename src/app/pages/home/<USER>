ion-toolbar {
  --color: white;
}

ion-button {
  --box-shadow: transparent;
  --background: none;
  ion-label {
    color: white;
    margin-left: 10px;
  }
}

ion-row {
  justify-content: center;
}

ion-card {
  width: 160px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  cursor: pointer;
}

.fa-wrench {
  color: #ff4081;
}

.fa-user-secret {
  color: #3f51b5;
}

.fa-building {
  color: #ff9800;
}

.fa-file-alt {
  color: #009688;
}

.fa-database {
  color: #8bc34a;
}

.fa-users {
  color: #3f51b5;
}

.fa-book {
  color: #259b24;
}

p {
  margin-top: 10%;
  font-weight: 600;
}

ion-grid {
  height: 100%;
}

.mainBlock {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.versionBlock {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.version {
  font-size: 12px;
  margin-bottom: 1%;
  color: slategrey;
  font-family: unset;
}

.image {
  height: 90px;
  width: 90px;
}