import { Component, NgZone } from '@angular/core';
import { Router } from '@angular/router';
import {
  RequestType,
  ResultType,
  UnviredCordovaSDK,
} from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import {
  AlertController,
  LoadingController,
  ModalController,
  Platform,
} from '@ionic/angular';
import { EditUserFacilityComponent } from 'src/app/components/edit-user-facility/edit-user-facility.component';
import {
  FACILITY_HEADER,
  USER_CONTEXT_HEADER,
  USER_ROLE,
} from 'src/app/data-models/data_classes';

import { DataService } from 'src/app/services/data.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.page.html',
  styleUrls: ['./home.page.scss'],
})
export class HomePage {
  public appVersion: string = '';
  public showVersion: boolean = true;
  public userData: USER_ROLE[] = [];
  public facility: FACILITY_HEADER[] = [];
  public facilityName: string = '';
  public isFacilityAvailable: boolean = false;
  public isAndroid: boolean = false;
  public userFacility: string = '';
  public userName: string = '';
  private platformState: string = '';
  constructor(
    public ngZone: NgZone,
    private route: Router,
    private dataService: DataService,
    public modalCtrl: ModalController,
    private loadingController: LoadingController,
    public alertController: AlertController,
    public unviredSDK: UnviredCordovaSDK,
    public platform: Platform
  ) {}

  async ionViewDidEnter() {
    if (this.platform.is('android')) {
      this.isAndroid = true;
    }
 
      this.appVersion = this.dataService.getAppVersion();
  }

  async ngOnInit(){
    console.log("ngonint called in home" )
    this.platformState = await this.platform.ready();
    console.log(this.platformState)
    if (this.platformState.length > 0) {
      await this.initialData();
    }
   
  }


  async initialData(){
    await this.loadcustomizationAndUserContext();
    await this.facilitiesFromDb();
    await this.dataService.getAgents();
    await this.getUsersServerData();
    let userContext: any = localStorage.getItem('userContext');
    if (userContext) {
      userContext = JSON.parse(userContext);
    }
    this.userName =
      userContext?.USER_CONTEXT_HEADER?.FIRST_NAME +
      ' ' +
      userContext?.USER_CONTEXT_HEADER?.LAST_NAME;


    
      await this.dataService.getAllUserData()


       
    
      await this.dataService.getAllSkillData();


  }

  async getUsersServerData() {
    let userAgent = await this.dataService.getData('AGENT_HEADER');
    for await (let element of userAgent) {
      console.log('calling get agent users.....')
      let customData = {
        AGENT_USER: [
          {
            AGENT_USER_HEADER: {
              AGENT_ID: element?.AGENT_ID,
            },
          },
        ],
      };
      await this.dataService.getAgentUser(customData);
    }
  }
  async loadcustomizationAndUserContext() {
    // Get User Context
    let result: any = await this.dataService.getUserContext();
    if (result) {
      if (result.type == ResultType.success) {
        await this.unviredSDK.dbSaveWebData();
        if (result?.data?.InfoMessage === undefined) {
          this.unviredSDK.logInfo(
            'HomePage',
            'loadcustomizationAndUserContext',
            'User Context has been downloaded successfully.'
          );
          console.log("result in home page ",result)
          if (result?.data?.USER_CONTEXT?.length > 0) {
            localStorage.setItem(
              'userContext',
              JSON.stringify(result.data.USER_CONTEXT[0])
            );
            await this.dbCall();
          }
        } else if (result?.data?.InfoMessage?.length > 0) {
          await this.alertforFacilityChange(result.message?.trim());
        }
      }
      if (result.type == ResultType.error) {
        if (result.code && result.code === 401) {
          this.unviredSDK.logError(
            'HomePage',
            'loadcustomizationAndUserContext',
            'Error while downloading User Context.'
          );
          this.dataService.logoutToInitialPage();
        } else if (result?.data?.InfoMessage?.length > 0) {
          await this.alertforFacilityChange(result.message?.trim());
        }
      }
    } else {
      this.unviredSDK.logError(
        'HomePage',
        'loadcustomizationAndUserContext',
        'Error while downloading User Context : ' + result.message
      );
      await this.alertforFacilityChange('Error downloading User Context');
    }
    if (this.dataService.isStartCustomization) {
      await this.serverCall();
    }
  }

  //Make server call
  async serverCall() {
    this.dataService.isStartCustomization = false;
    // Displaying customization daialog.
    await this.displayPleaseWaitLoader();
    // Download Customization
    await this.dataService.getCustomization();
    let userAgentResult = await this.unviredSDK.dbExecuteStatement(
      `SELECT * FROM USER_CONTEXT_HEADER`
    );
    if (userAgentResult.type == ResultType.success) {
      if (userAgentResult?.data?.length > 0) {
        this.userFacility = userAgentResult.data[0].CURRENT_FACILITY;
      }
    }
    let customData = {
      STRUCTURE: [
        {
          STRUCTURE_HEADER: {
            FACILITY_ID: this.userFacility,
            DIVISION_ID: '',
            TAG: '',
            NAME: '',
            CATEGORY: '',
            STRUCT_TYPE: '',
            STATUS: '',
            P_MODE: '',
          },
        },
      ],
    };
   await this.dataService.getStructures(customData);
   let res =  await this.dataService.downloadPermits(null, null, true);
    console.log("res in home is " , res)
    if(res.type == ResultType.success){
      this.loadingController.dismiss();
    }
   
  }

  //Db call to load Tiles with respect to roles
  async dbCall() {
    this.ngZone.run(async () => {
      this.userData = await this.dataService.getData('USER_ROLE');
      console.log(this.userData);
      let platform = await this.unviredSDK.platform();

      switch (platform) {
        case 'android':
        case 'ios':
          this.userData[0].AGENT_MGMT = 'false';
          this.userData[0].CONFIGURATION = 'false';
          this.userData[0].FACILITY_MGMT = 'false';
          this.userData[0].PROC_MGMT = 'false';
          this.userData[0].REPORT = 'false';
          this.userData[0].USER_MGMT = 'false';

          break;
      }
    });
  }

  // Display Loading dialog.
  async displayPleaseWaitLoader() {
    const loading = await this.loadingController.create({
      message: 'Please wait for data download to complete...',
      backdropDismiss: false,
    });
    await loading.present();
  }

  // Facility Db Call
  async facilitiesFromDb() {
    let userContextHeaders = await this.dataService.getData(
      'USER_CONTEXT_HEADER'
    );

    if (!userContextHeaders[0]) {
      return;
    }

    let userContextHeader: USER_CONTEXT_HEADER = userContextHeaders[0];
    let selectedFacilityId = "";
    if (
      userContextHeader?.CURRENT_FACILITY &&
      userContextHeader?.CURRENT_FACILITY_DESC
    ) {
      this.isFacilityAvailable = true;
      this.facilityName =
        userContextHeader?.CURRENT_FACILITY +
        ' - ' +
        userContextHeader?.CURRENT_FACILITY_DESC;
        selectedFacilityId = userContextHeader?.CURRENT_FACILITY;
    } else if (userContextHeader?.CURRENT_FACILITY) {
      this.isFacilityAvailable = true;
      this.facilityName = userContextHeader?.CURRENT_FACILITY;
      selectedFacilityId = userContextHeader?.CURRENT_FACILITY;

    } else {
      this.isFacilityAvailable = false;
      this.facilityName = '';
      selectedFacilityId = '';
    }
    this.dataService.setSelectedFacilityId(selectedFacilityId);
  }

  // Facility from Server
  async facilitiesFromServer() {
    let result = await this.dataService.getFacility();
    if (result?.FACILITY?.length > 0) {
      await this.addFacilityOrUpdate();
    }
  }

  // Facility add or update Modal View
  async addFacilityOrUpdate() {
    const modal = await this.modalCtrl.create({
      component: EditUserFacilityComponent,
      backdropDismiss: false,
      cssClass: 'addOrUpdateFacility',
      componentProps: {
        facilityFromHome: this.facility,
      },
    });
    await modal.present();
    let { data } = await modal.onDidDismiss();
    if (data) {
      this.isFacilityAvailable = true;
      await this.facilitiesFromDb();
      await this.dataService.downloadPermits(null, null, true);
    }
  }

  async logoutToInitialPage() {
    const alert = await this.alertController.create({
      message: `Are you sure you want to logout?`,
      buttons: [
        {
          text: 'Cancel',
          cssClass: 'modal-button-cancel',
          handler: (blah) => {},
        },
        {
          text: 'Logout',
          handler: async (data) => {
            this.dataService.logoutToInitialPage();
          },
        },
      ],
    });
    await alert.present();
  }

  //Alert to show warning
  async alertforFacilityChange(messageReceived) {
    const alert = await this.alertController.create({
      message: messageReceived,
      buttons: [
        {
          text: 'Okay',
        },
      ],
    });
    await alert.present();
  }

  // Tiles Routings
  goToPermits() {
    this.route.navigate(['/permits']);
  }

  goToAgents() {
    this.route.navigate(['/agents']);
  }

  goToFacilitiesDivisions() {
    this.route.navigate(['/facilities-divisions']);
  }

  gotoReports() {
    this.ngZone.run(() => {
      this.route.navigate(['/reports']);
    });
  }

  gotoMasterData() {
    this.ngZone.run(() => {
      this.route.navigate(['/master-data']);
    });
  }

  goToUsers() {
    this.route.navigate(['/users']);
  }

  goToRepairProcedures() {
    this.route.navigate(['/repair-procedures']);
  }

  goToSettings() {
    this.route.navigate(['/settings']);
  }
}
