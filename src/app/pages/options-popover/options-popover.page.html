<ion-list class="list-options">
  <ion-item button="true" detail="false" lines="none" *ngFor="let option of options" (click)="selectOption(option)">
    <i [class]="option.icon" [ngStyle]="getIconColor(option)"></i>
    <ion-label class="option-label">{{option.name}}</ion-label>
    <ion-badge *ngIf="option.unReadCount > 0">{{option.unReadCount}}</ion-badge>
    <ion-icon *ngIf="option.IS_CHECKED" color="success" name="checkmark-outline"></ion-icon>
  </ion-item>
</ion-list>