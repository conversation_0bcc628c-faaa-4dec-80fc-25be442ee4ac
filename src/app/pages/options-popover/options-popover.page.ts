import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { PopoverController, NavParams } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
// import { AppConstants } from '../shared/app-constants';
// import { FILTER_OPTION, FILTER_PARAMS } from '../shared/data-modal/HEADER';
// import { ShareDataService } from '../shared/services/ShareData.service';

@Component({
  selector: 'app-options-popover',
  templateUrl: './options-popover.page.html',
  styleUrls: ['./options-popover.page.scss'],
})
export class OptionsPopoverPage implements OnInit {
  public options = [];
  public devicePlatform = '';
  public isHybridNative: boolean;
  public flagToLoadFormOrFormset: string = '';
  public displayType: string;
  public category: number;
  // public appliedFilter: FILTER_PARAMS;
  // public categoryOptions: Array<FILTER_OPTION> = [];
  // public constants: AppConstants;

  constructor(
    // private shareData: ShareDataService,
    public popoverController: PopoverController,
    private route: ActivatedRoute,
    private translate: TranslateService,
    public navParams: NavParams
  ) {
    // this.constants = new AppConstants();
    // this.devicePlatform = this.shareData.getDevicePlatform();
    // this.displayType = this.navParams.data.optionType;
  }

  ngOnInit() {
    this.devicePlatform = (this.devicePlatform === '') ? 'browser' : this.devicePlatform;     // on reload in browser
    this.isHybridNative = (this.devicePlatform === 'browser') ? false : true;
    this.route.queryParams.subscribe(params => {
      this.flagToLoadFormOrFormset = params['type'];
    });
  }

  ionViewWillEnter() {
    console.log("ionViewWillEnter called");
    this.displayRenderOptions();

    // switch (this.displayType) {
    //   case "toolbar":
    //     this.displayToolBarOptions();
    //     break;
    //   case "task":
    //     this.displayTaskOptions();
    //     break;
    //   case "renderer":
    //     this.displayRenderOptions();
    //     break;
    //   case "filter":
    //     this.filterSortOtions();
    //     break;
    // }
  }

  displayToolBarOptions() {
    let availableOptions: any[] = [
      { name: this.translate.instant("Refresh My Forms"), value: 1, icon: 'fa fa-repeat' },
      { name: this.translate.instant("Refresh System Data"), value: 2, icon: 'fa fa-download' },
      { name: this.translate.instant("Settings"), value: 3, icon: 'fa fa-cog' },
      { name: this.translate.instant("Help"), value: 4, icon: 'fa fa-question-circle' },
      { name: this.translate.instant("Logout"), value: 5, icon: 'fa fa-sign-out' },
    ];

    this.options[0] = availableOptions[0];
    this.options[1] = availableOptions[1];

    if (this.devicePlatform === 'browser') {
      // For browser
      this.options[2] = availableOptions[3];
      this.options[3] = availableOptions[4];
    } else {
      // For mobile app
      this.options[2] = availableOptions[2];
      this.options[3] = availableOptions[3];
    }
  }

  displayTaskOptions() {
    // let task = this.navParams.data.optionData;
    // let rendererOptions: any[] = [
    //   { name: this.translate.instant('History'), value: 13, icon: 'fa fa-history' },
    //   { name: this.translate.instant('Share'), value: 14, icon: 'fa fa-share-alt' },
    //   { name: this.translate.instant('Delegate'), value: 15, icon: 'fa fa-share' },
    //   { name: this.translate.instant('Trash'), value: 16, icon: 'fa fa-trash-o' },
    //   { name: this.translate.instant('PDF'), value: 18, icon: 'fa fa-file-pdf-o' },
    //   { name: this.translate.instant('Review'), value: 19, icon: 'fa fa-minus-circle' },
    //   { name: this.translate.instant('Change Reviewer'), value: 20, icon: 'fa fa-exchange' },
    //   { name: this.translate.instant('Print'), value: 21, icon: 'fa fa-print' }
    // ];
    // for (let i = 0; i < rendererOptions.length; i++) {
    //   if (task.markComplete && (i == 0 || i == 4)) {
    //     this.options.push(rendererOptions[i]);
    //   } else if (!task.markComplete && task.showFailReviewOption) {
    //     this.options.push(rendererOptions[i]);
    //   } else if (!task.markComplete && (task.TASK_TYPE_LABEL == this.translate.instant('Checked out') && (i != 3)) && ((!task.showFailReviewOption && (i !== 5)) && (!task.showFailReviewOption && (i !== 6)))) {
    //     this.options.push(rendererOptions[i]);
    //   } else if (!task.markComplete && task.TASK_TYPE_LABEL !== this.translate.instant('Checked out') && ((!task.showFailReviewOption && (i !== 5)) && (!task.showFailReviewOption && (i !== 6)))){
    //     this.options.push(rendererOptions[i]);
    //   }
    // }
  }

  displayRenderOptions() {
    console.log("displayRenderOptions called");
    this.navParams.data['form'].ATTRIBUTES = "";
    let clientCompField = [];
    // let clientCompField = this.navParams.data['form'].ATTRIBUTES ? JSON.parse(this.navParams.data['form'].ATTRIBUTES).filter((e: { key: string; }) => { return e.key.trim() == "client-complete-check-field" }) : [];
    let formData = (this.navParams.data['formData'] && this.navParams.data['formData'].data) ? this.navParams.data['formData'].data : "";
    let completeFld = (clientCompField.length > 0 && formData !== "" && clientCompField[0].value !== "") ? formData[clientCompField[0].value] : true;
    // let isDownloaded = this.shareData.getCustomizationDownloaded();
    let isdisableFormCompletion = false;
    let disableFormCompletion = [];
    // let disableFormCompletion = this.navParams.data['form'].ATTRIBUTES ? JSON.parse(this.navParams.data['form'].ATTRIBUTES).filter((e: { key: string; })  => { return e.key.trim() == "disable-form-completion" }) : [];
    if (disableFormCompletion && disableFormCompletion.length > 0 && disableFormCompletion[0]) {
      isdisableFormCompletion = disableFormCompletion[0].value;
    }else{
      isdisableFormCompletion = false;
    }
    let rendererOptions: any[] = [
      // { name: this.translate.instant('Save and Sync'), value: 7, icon: 'fa fa-check-circle', cndt: ((!this.navParams.data.readOnly && !this.navParams.data.shiftType && (this.flagToLoadFormOrFormset == "form") && (((this.navParams.data.taskType === this.constants.TASK_USER_STATUS.IN_PROGRESS) && (this.navParams.data.taskHeaderType === 'APPROVAL') && (this.navParams.data.percentageCompleted === 100)) || ((this.navParams.data.percentageCompleted === 100 && completeFld) && (this.navParams.data.taskHeaderType !== 'APPROVAL')))) || (!this.navParams.data.readOnly && !this.navParams.data.shiftType && (this.flagToLoadFormOrFormset == "form") && (this.navParams.data.taskHeaderType !== 'APPROVAL') && (this.navParams.data.percentageCompleted !== 100 || (this.navParams.data.percentageCompleted === 100 && !completeFld)))) && this.navParams.data.isHybridNative},
      // { name: this.translate.instant('Complete'), value: 7, icon: 'fa fa-check-circle', cndt: (!this.navParams.data.readOnly && !this.navParams.data.shiftType && (this.flagToLoadFormOrFormset == "form") && (this.navParams.data.taskHeaderType !== 'APPROVAL') && (this.navParams.data.percentageCompleted !== 100 || (this.navParams.data.percentageCompleted === 100 && !completeFld))) },
      // { name: this.translate.instant('Save'), value: 7, icon: 'fa fa-check-circle', cndt: (!this.navParams.data.readOnly && !this.navParams.data.shiftType && (this.flagToLoadFormOrFormset == "form") && (((this.navParams.data.taskType === this.constants.TASK_USER_STATUS.IN_PROGRESS) && (this.navParams.data.taskHeaderType === 'APPROVAL') && (this.navParams.data.percentageCompleted === 100)) || ((this.navParams.data.percentageCompleted === 100 && completeFld) && (this.navParams.data.taskHeaderType !== 'APPROVAL')))) },
      // { name: this.translate.instant('History'), value: 14, icon: 'fa fa-history', cndt: (isDownloaded && !this.navParams.data.readOnly && !this.navParams.data.shiftType && (this.flagToLoadFormOrFormset == "form") && (this.navParams.data.formReadWriteFlag || this.navParams.data.taskType === this.constants.TASK_USER_STATUS.IN_PROGRESS) && (this.navParams.data.taskHeaderType === 'APPROVAL')) },
      // { name: this.translate.instant('Comments'), value: 8, icon: 'fa fa-comments', cndt: isDownloaded },
      // { name: this.translate.instant('Info'), value: 9, icon: 'fa fa-info-circle', cndt: isDownloaded },
      // { name: this.translate.instant('Alert'), value: 10, icon: 'fa fa-bell-o', cndt: isDownloaded && this.navParams.data.alertBadge, unReadCount: this.navParams.data.count },
      // { name: this.translate.instant('Document'), value: 11, icon: 'fa fa-copy', cndt: (this.navParams.data.showDocs && isDownloaded) },
      // { name: this.translate.instant('Help'), value: 12, icon: 'fa fa-question-circle', cndt: isDownloaded }

      // { name: this.translate.instant('Complete'), value: 7, icon: 'fa fa-check-circle', cndt: (!isdisableFormCompletion && !this.navParams.data['readOnly'] && !this.navParams.data['shiftType'] && (this.flagToLoadFormOrFormset == "form") && (this.navParams.data['percentageCompleted'] !== 100 || (this.navParams.data['percentageCompleted'] === 100 && !completeFld))) },
      // { name: this.translate.instant('Save'), value: 7, icon: 'fa fa-check-circle', cndt: (!isdisableFormCompletion && !this.navParams.data['readOnly'] && !this.navParams.data['shiftType'] && (this.flagToLoadFormOrFormset == "form") && (((this.navParams.data['percentageCompleted'] === 100)) || ((this.navParams.data['percentageCompleted'] === 100 && completeFld)))) },
      // { name: this.translate.instant('History'), value: 14, icon: 'fa fa-history', cndt: (!this.navParams.data['readOnly'] && !this.navParams.data['shiftType'] && (this.flagToLoadFormOrFormset == "form") && (this.navParams.data['formReadWriteFlag'])) },
      // { name: this.translate.instant('Comments'), value: 8, icon: 'fa fa-comments', cndt: true },
      // { name: this.translate.instant('Info'), value: 9, icon: 'fa fa-info-circle', cndt: true },
      // { name: this.translate.instant('Alert'), value: 10, icon: 'fa fa-bell-o', cndt: this.navParams.data['alertBadge'], unReadCount: this.navParams.data['count'] },
      // { name: this.translate.instant('Document'), value: 11, icon: 'fa fa-copy', cndt: (this.navParams.data['showDocs']) },
      // { name: this.translate.instant('Help'), value: 12, icon: 'fa fa-question-circle', cndt: true }
      { name: this.translate.instant('Complete'), value: 7, icon: 'fa fa-check-circle', cndt: (!isdisableFormCompletion && this.navParams.data['percentageCompleted'] !== 100 ) },
      { name: this.translate.instant('Save'), value: 7, icon: 'fa fa-check-circle', cndt: (!isdisableFormCompletion && this.navParams.data['percentageCompleted'] === 100)}
    ];

    for (let i = 0; i < rendererOptions.length; i++) {
      if (i == 3 && this.devicePlatform === 'browser' && rendererOptions[3].cndt) {
        this.options.push(rendererOptions[i]);
      } else if (rendererOptions[i].cndt) {
        this.options.push(rendererOptions[i]);
      }
    }
  }

  filterSortOtions() {
    // this.category = this.navParams.get('optionData');
    // this.categoryOptions = [
    //   { name: this.translate.instant("All"), IS_CHECKED: false, ACTIVE_FILTER: this.constants.FILTER_FORMS.ALL },
    //   { name: this.translate.instant("Open"), IS_CHECKED: false, ACTIVE_FILTER: this.constants.FILTER_FORMS.OPEN },
    //   { name: this.translate.instant("Due Today"), IS_CHECKED: false, ACTIVE_FILTER: this.constants.FILTER_FORMS.DUE_TODAY },
    //   { name: this.translate.instant("Completed"), IS_CHECKED: false, ACTIVE_FILTER: this.constants.FILTER_FORMS.COMPLETED },
    // ]
    // this.appliedFilter = { SORTED_BY: 1, GROUPED_BY: 0, CATEGORY: this.category };
    // this.categoryOptions = this.categoryOptions.map(option => {
    //   if (option.ACTIVE_FILTER === this.appliedFilter.CATEGORY) {
    //     option.IS_CHECKED = true;
    //   }
    //   return option;
    // });
    // this.options = this.categoryOptions;
  }

  selectOption(option: any) {
    // if (this.displayType == "filter") {
    //   this.categoryOptions.forEach((ele) => {
    //     if (ele.name == option.name) {
    //       option.IS_CHECKED = true;
    //       this.category = option.ACTIVE_FILTER;
    //     } else {
    //       option.IS_CHECKED = false;
    //     }
    //   })
    //   this.appliedFilter = { SORTED_BY: 1, GROUPED_BY: 0, CATEGORY: this.category };
    //   this.shareData.setFilterForForms(this.appliedFilter);
    //   this.popoverController.dismiss(this.appliedFilter);
    // } else {
      this.popoverController.dismiss(option.name);
    // }
  }

  getIconColor(option: any) {
    let colorStyle = { 'color': '' };
    switch (option.icon) {
      case 'fa fa-sun-o':
        colorStyle.color = 'rgba(240, 187, 41, 0.863)';
        break;
      case 'fa fa-moon-o':
        colorStyle.color = 'rgba(33, 98, 158, 0.863)';
        break;
      case 'fa fa-sign-out':
        colorStyle.color = 'rgba(179, 43, 43, 0.863)';
        break;
    }
    return colorStyle;
  }
}
