import { Pipe, PipeTransform } from '@angular/core';
import { PermitStatus } from '../shared/app-constants';

@Pipe({
  name: 'StatusTextFormatFilterPipe',
})
export class StatusTextFormatFilterPipe implements PipeTransform {
  transform(value: any, args?: any): any {
    if (!value) return null;
    let text = '';
    switch (value) {
      case PermitStatus.OPEN:
        text = 'Open';
        break;
      case PermitStatus.IN_REVIEW:
        text = 'In Review';
        break;
      case PermitStatus.APPROVED:
        text = 'Approved';
        break;
      case PermitStatus.ISSUED:
        text = 'Issued';
        break;
      case PermitStatus.CLOSED:
        text = 'Closed';
        break;
      case PermitStatus.CANCELLED:
        text = 'Cancelled';
        break;
    }
    return text;
  }
}
