import { Pipe, PipeTransform } from '@angular/core';
import { DbResult, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { PermitStatus } from '../shared/app-constants';
import { PERMIT_HEADER } from '../data-models/data_classes';

@Pipe({
    name: 'GetTagNamePipe',
})
export class GetTagNamePipe implements PipeTransform {
    constructor(public unviredSDK: UnviredCordovaSDK) { }

    async transform(value: any, args?: any): Promise<any> {
        if (!value) return null;
        let getTagNameQueryResult: DbResult = await this.unviredSDK.dbExecuteStatement(`SELECT NAME AS NAME FROM STRUCTURE_HEADER WHERE TAG='${value}'`);
        return getTagNameQueryResult?.data[0]?.NAME ? getTagNameQueryResult?.data[0]?.NAME : value;
    }
}

@Pipe({
    name: 'GetRequestedByNamePipe',
})
export class GetRequestedByNamePipe implements PipeTransform {
    constructor(public unviredSDK: UnviredCordovaSDK) { }

    async transform(value: any, args?: any): Promise<any> {
        if (!value) return null;
        let getRequestedByNamePipeResult: DbResult = await this.unviredSDK.dbExecuteStatement(`SELECT FIRST_NAME AS FIRST_NAME,  LAST_NAME AS LAST_NAME FROM USER_HEADER WHERE USER_ID='${value}'`);
        return getRequestedByNamePipeResult?.data[0]?.FIRST_NAME ? `${getRequestedByNamePipeResult?.data[0]?.FIRST_NAME} ${getRequestedByNamePipeResult?.data[0]?.LAST_NAME}` : value;
    }
}


@Pipe({
    name: 'GetFullDateByTimestampPipe',
})
export class GetFullDateByTimestampPipe implements PipeTransform {
    constructor(public unviredSDK: UnviredCordovaSDK) { }

    async transform(value: any, args?: any): Promise<any> {
        if (!value) return null;
        const currentDate = new Date(value);
        const options: any = {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        };
        return currentDate.toLocaleString('en-US', options);
    }
}

@Pipe({
    name: 'GetPermitDescriptionPipe',
})
export class GetPermitDescriptionPipe implements PipeTransform {
    constructor(public unviredSDK: UnviredCordovaSDK) { }

    async transform(value: any, args?: any): Promise<any> {
        if (!value) return null;
        let getPermitDescriptionPipeResult: DbResult = await this.unviredSDK.dbExecuteStatement(`SELECT DESCRIPTION AS DESC FROM PERMIT_TYPE_HEADER WHERE PERMIT_TYPE='${value}'`);
        return getPermitDescriptionPipeResult?.data[0]?.DESC ? getPermitDescriptionPipeResult?.data[0]?.DESC : value;
    }
}

@Pipe({
    name: 'GetPermitDivisionNamePipe',
})
export class GetPermitDivisionNamePipe implements PipeTransform {
    constructor(public unviredSDK: UnviredCordovaSDK) { }

    async transform(value: any, args?: any): Promise<any> {
        if (!value) return null;
        let getPermitDivisionNamePipeResult: DbResult = await this.unviredSDK.dbExecuteStatement(`SELECT NAME AS NAME FROM DIVISION_HEADER WHERE DIVISION_ID='${value}'`);
        return getPermitDivisionNamePipeResult?.data[0]?.NAME ? getPermitDivisionNamePipeResult?.data[0]?.NAME : value;
    }
}

@Pipe({
    name: 'GetAgentNamePipe',
})
export class GetAgentNamePipe implements PipeTransform {
    constructor(public unviredSDK: UnviredCordovaSDK) { }

    async transform(value: any, args?: any): Promise<any> {
        if (!value) return null;
        let getAgentNamePipePipeResult: DbResult = await this.unviredSDK.dbExecuteStatement(`SELECT NAME AS NAME FROM AGENT_HEADER WHERE AGENT_ID='${value}'`);
        return getAgentNamePipePipeResult?.data[0]?.NAME ? getAgentNamePipePipeResult?.data[0]?.NAME : value;
    }
}

@Pipe({
    name: 'GetFullDateByTimestampPipeReport',
})
export class GetFullDateByTimestampPipeReport implements PipeTransform {
    constructor(public unviredSDK: UnviredCordovaSDK) { }

    async transform(value: any, args?: any): Promise<any> {
        if (!value) return null;
        if(typeof value === 'string')
        value = Number(value);
        const currentDate = new Date(value);
        const options: any = {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        };
        return currentDate.toLocaleString('en-US', options);
    }
}

@Pipe({
    name: 'IsActionButtonDissabledPipe',
})
export class IsActionButtonDissabledPipe implements PipeTransform {
    constructor(public unviredSDK: UnviredCordovaSDK) { }
    temp = false;
    async transform(value: any, args?: any): Promise<any> {
        if (!value) return null;
        let selectedPermit = localStorage.getItem('selectedPermit')
        let permit: PERMIT_HEADER;
        if (selectedPermit) {
          permit = JSON.parse(selectedPermit);
        }
        if (value.STATUS == PermitStatus.OPEN) {
            this.temp = false;
        }
        if (value.STATUS == PermitStatus.IN_REVIEW) {
            let query = `SELECT ps.USER_ID FROM permit_stakeholder ps WHERE ps.permit_no = '${permit?.PERMIT_NO}' AND ps.role = 'REVIEW'  AND CONTEXT IS NULL
            AND EXISTS (SELECT 1 FROM user_context_header uch WHERE uch.USER_ID = ps.USER_ID) AND EXISTS (
                SELECT 1
                FROM USER_ROLE ur
                WHERE ur.user_id = ps.user_id
                  AND ur.approve = 'true'
              );`
            let res: DbResult = await this.unviredSDK.dbExecuteStatement(query);
            this.temp = res?.data?.length > 0 ? false : true
        }
        return this.temp;
    }
}

@Pipe({
    name: 'GetApprovalTypeUserPipe',
    pure: false
})
export class GetApprovalTypeUserPipe implements PipeTransform {
    transform(items: any[], filter: any): any {
        if (!items || !filter) {
            return items;
        }
        if (filter?.CONTEXT != null) {
            let arr =items.filter(item => item.APPR_TYPE.indexOf(filter.APPR_TYPE) !== -1);
            return arr
        } else {
            return Array.from(new Set(items))
        }
    }
}