//	Generated using Unvired Modeller - Build R-4.000.0139



export class DataStructure {
  LID: string;
  FID: string;
  OBJECT_STATUS: number = 1;
  SYNC_STATUS: number = 0;
}


export class AGENT_HEADER extends DataStructure {
    // No desc available
AGENT_ID: string;

// No desc available
NAME: string;

// No desc available
IS_INTERNAL: string;

// No desc available
CONTACT: string;

// No desc available
PHONE: string;

// No desc available
EMAIL: string;

// No desc available
ADDRESS: string;

// No desc available
IS_ACTIVE: string;

// No desc available
P_MODE: string;
  
  constructor() {
  super();
    this.AGENT_ID = "";

this.NAME = "";

this.IS_INTERNAL = "";

this.CONTACT = "";

this.PHONE = "";

this.EMAIL = "";

this.ADDRESS = "";

this.IS_ACTIVE = "";

this.P_MODE = "";
}
}

export class AGENT_FACILITY extends DataStructure {
    // No desc available
AGENT_ID: string;

// No desc available
FACILITY_ID: string;

// No desc available
P_MODE: string;
  
  constructor() {
  super();
    this.AGENT_ID = "";

this.FACILITY_ID = "";

this.P_MODE = "";
}
}

export class AGENT_USER_HEADER extends DataStructure {
    // No desc available
AGENT_ID: string;

// No desc available
USER_ID: string;

// No desc available
P_MODE: string;
  
  constructor() {
  super();
    this.AGENT_ID = "";

this.USER_ID = "";

this.P_MODE = "";
}
}

export class APPROVAL_TYPE_HEADER extends DataStructure {
    // No desc available
APPR_TYPE: string;

// No desc available
SCOPE: string;

// No desc available
DESCRIPTION: string;

// No desc available
P_MODE: string;
  
  constructor() {
  super();
    this.APPR_TYPE = "";

this.SCOPE = "";

this.DESCRIPTION = "";

this.P_MODE = "";
}
}

export class DIVISION_HEADER extends DataStructure {
    // No desc available
FACILITY_ID: string;

// No desc available
DIVISION_ID: string;

// No desc available
NAME: string;

// No desc available
P_MODE: string;
  
  constructor() {
  super();
    this.FACILITY_ID = "";

this.DIVISION_ID = "";

this.NAME = "";

this.P_MODE = "";
}
}

export class DOCUMENT_HEADER extends DataStructure {
    // No desc available
DOC_ID: string;

TITLE: string;

// No desc available
FILE_NAME: string;

MIME_TYPE: string;

DOC_TYPE: string;

THUMBNAIL: string;

CHANGED_BY: string;

CHANGED_ON:string;

// No desc available
// DESCR: string;

// No desc available
CREATED_BY: string;

// No desc available
CREATED_ON: number;

// No desc available
P_MODE: string;
  
  constructor() {
  super();
    this.DOC_ID = "";

    this.TITLE = '';

this.FILE_NAME = "";

// this.DESCR = "";

this.CREATED_BY = "";

this.CREATED_ON = null;

this.P_MODE = "";

this.CHANGED_BY = "" ;

this.CHANGED_ON =null;
this.THUMBNAIL = '';
this.MIME_TYPE = '';
this.DOC_TYPE ='';



}
}

export class DOCUMENT_ATTACHMENT extends DataStructure {
    // UID
UID: string;

// File Name
FILE_NAME: string;

// Mime Type
MIME_TYPE: string;

// Download URL
URL: string;

// External or Internal URL
EXTERNAL_URL: string;

// External URL Requires Authentication
URL_REQUIRES_AUTH: string;

// Path to the file on the device
LOCAL_PATH: string;

// Do not cache
NO_CACHE: string;

// Server timestamp
SERVER_TIMESTAMP: number;

// Tag 1
TAG1: string;

// Tag 2
TAG2: string;

// Tag 3
TAG3: string;

// Tag 4
TAG4: string;

// Tag 5
TAG5: string;

// Status
ATTACHMENT_STATUS: string;

// Auto Download Flag
AUTO_DOWNLOAD: string;

// Name of the param
PARAM: string;

// Message from User
MESSAGE: string;
  
  constructor() {
  super();
    this.UID = "";

this.FILE_NAME = "";

this.MIME_TYPE = "";

this.URL = "";

this.EXTERNAL_URL = "";

this.URL_REQUIRES_AUTH = "";

this.LOCAL_PATH = "";

this.NO_CACHE = "";

this.SERVER_TIMESTAMP = null;

this.TAG1 = "";

this.TAG2 = "";

this.TAG3 = "";

this.TAG4 = "";

this.TAG5 = "";

this.ATTACHMENT_STATUS = "";

this.AUTO_DOWNLOAD = "";

this.PARAM = "";

this.MESSAGE = "";
}
}

export class FACILITY_HEADER extends DataStructure {
    // No desc available
FACILITY_ID: string;

// No desc available
NAME: string;

// No desc available
P_MODE: string;

// No desc available
BOUNDARY: string;
  
  constructor() {
  super();
    this.FACILITY_ID = "";

this.NAME = "";

this.BOUNDARY = "";

this.P_MODE = "";
}
}

export class FORM_HEADER extends DataStructure {
    // No desc available
FORM_ID: string;

// No desc available
FORM_VERSION: number;

// No desc available
FORM_NAME: string;

// No desc available
FORM_TITLE: string;

// No desc available
FORM_DESC: string;

// No desc available
TEMPLATE: string;

// No desc available
CATEGORY: string;

// No desc available
AVATAR: string;

// No desc available
ATTRIBUTES: string;

// No desc available
FORM_TYPE: string;
  
  constructor() {
  super();
    this.FORM_ID = "";

this.FORM_VERSION = null;

this.FORM_NAME = "";

this.FORM_TITLE = "";

this.FORM_DESC = "";

this.TEMPLATE = "";

this.CATEGORY = "";

this.AVATAR = "";

this.ATTRIBUTES = "";

this.FORM_TYPE = "";
}
}

export class INPUT_GET_FORM_TEMPLATE_HEADER extends DataStructure {
    // No desc available
CATEGORY: string;

// No desc available
FORM_NAME: string;

// No desc available
FORM_VERSION: string;

// No desc available
WITH_DATA: string;
  
  constructor() {
  super();
    this.CATEGORY = "";

this.FORM_NAME = "";

this.FORM_VERSION = "";

this.WITH_DATA = "";
}
}

export class PERMIT_HEADER extends DataStructure {
    // No desc available
PERMIT_NO: string;

// No desc available
PERMIT_TYPE: string;

// Type Description for display
TYPE_DESC: string;

// No desc available
DESCRIPTION: string;

// No desc available
FACILITY_ID: string;

// No desc available
DIVISION_ID: string;

// No desc available
TAG: string;

// No desc available
AGENT_ID_INT: string;

// No desc available
AGENT_ID_EXT: string;

// No desc available
JOB_NO: string;

// No desc available
STATUS: string;

// No desc available
REQUESTED_BY: string;

// No desc available
REQUESTED_ON: number;

// No desc available
SOURCE: string;

// No desc available
PERMIT_DATE: number;

// No desc available
EXPIRY_DATE: number;

// No desc available
IS_EXTENDED: string;

// No desc available
EXTENSION_DATE: number;

// Flag to indicate if the permit was extended
HAS_EXTENSION: boolean;

// Formatted dates for display with time
FORMATTED_START_DATE: string;
FORMATTED_END_DATE: string;

// No desc available
COMMENTS: string;

APPROVAL_SUMMARY: string;

// No desc available
P_MODE: string;
  
  constructor() {
  super();
    this.PERMIT_NO = "";

this.PERMIT_TYPE = "";

this.TYPE_DESC = "";

this.DESCRIPTION = "";

this.FACILITY_ID = "";

this.DIVISION_ID = "";

this.TAG = "";

this.AGENT_ID_INT = "";

this.AGENT_ID_EXT = "";

this.JOB_NO = "";

this.STATUS = "";

this.REQUESTED_BY = "";

this.REQUESTED_ON = null;

this.SOURCE = "";

this.PERMIT_DATE = null;

this.EXPIRY_DATE = null;

this.IS_EXTENDED = "";

this.EXTENSION_DATE = null;

this.HAS_EXTENSION = false;

this.FORMATTED_START_DATE = "";
this.FORMATTED_END_DATE = "";

this.COMMENTS = "";

this.P_MODE = "";
}
}

export class PERMIT_STAKEHOLDER extends DataStructure {
    // No desc available
PERMIT_NO: string;

// No desc available
ROW_ID: string;

// No desc available
ROLE: string;

// No desc available
CONTEXT: string;

// No desc available
AGENT_ID: string;

// No desc available
USER_ID: string;

// No desc available
COMMENT: string;

// No desc available
P_MODE: string;

  // No desc available
  IS_ACTIVE: string;

  
  
  constructor() {
  super();
    this.PERMIT_NO = "";

this.ROW_ID = "";

this.ROLE = "";

this.CONTEXT = "";

this.AGENT_ID = "";

this.USER_ID = "";

this.COMMENT = "";

this.P_MODE = "";

this.IS_ACTIVE = "";
}
}

export class PERMIT_FORM extends DataStructure {
    // No desc available
PERMIT_NO: string;

// No desc available
FORM_GUID: string;

// No desc available
FORM_ID: string;

// No desc available
FORM_NAME: string;

// No desc available
FORM_VERSION: number;

// No desc available
FORM_TITLE: string;

// No desc available
DATA: string;

// No desc available
CHANGED_BY: string;

// No desc available
CHANGED_ON: number;

// No desc available
PARTIAL_FLAG: string;

// No desc available
COMPLETED: string;

// No desc available
EXTERNAL_URL: string;

// No desc available
P_MODE: string;
  
  constructor() {
  super();
    this.PERMIT_NO = "";

this.FORM_GUID = "";

this.FORM_ID = "";

this.FORM_NAME = "";

this.FORM_VERSION = null;

this.FORM_TITLE = "";

this.DATA = "";

this.CHANGED_BY = "";

this.CHANGED_ON = null;

this.PARTIAL_FLAG = "";

this.COMPLETED = "";

this.EXTERNAL_URL = "";

this.P_MODE = "";
}
}

export class PERMIT_DOC extends DataStructure {
    // No desc available
PERMIT_NO: string;

// No desc available
DOC_ID: string;

// No desc available
DOC_TYPE: string;

// No desc available
THUMBNAIL: string;

// No desc available
P_MODE: string;
  
  constructor() {
  super();
    this.PERMIT_NO = "";

this.DOC_ID = "";

this.DOC_TYPE = "";

this.THUMBNAIL = "";

this.P_MODE = "";
}
}

export class PERMIT_LOG extends DataStructure {
    // No desc available
PERMIT_NO: string;

// No desc available
LOG_NO: number;

// No desc available
PERMIT_STATUS: string;

APPROVAL: string;

APPR_TYPE: string;

// No desc available
CREATED_ON: number;

// No desc available
CREATED_BY: string;

// No desc available
COMMENT: string;

// No desc available
P_MODE: string;

ACTION: string;
  
  constructor() {
  super();
    this.PERMIT_NO = "";

this.LOG_NO = null;

this.PERMIT_STATUS = "";

this.CREATED_ON = null;

this.CREATED_BY = "";

this.COMMENT = "";

this.P_MODE = "";

this.ACTION = "";
}
}

export class PERMIT_QUERY_CTX_HEADER extends DataStructure {
    // No desc available
PERMIT_NO: string;

// No desc available
PERMIT_TYPE: string;

// No desc available
DESCRIPTION: string;

// No desc available
FACILITY_ID: string;

// No desc available
DIVISION_ID: string;

// No desc available
TAG: string;

// No desc available
AGENT_ID: string;

// No desc available
JOB_NO: string;

// No desc available
STATUS: string;

// No desc available
REQUESTED_BY: string;

// No desc available
REQUESTED_ON: number;

// No desc available
SOURCE: string;

// No desc available
PERMIT_DATE: number;

// No desc available
EXPIRY_DATE: number;

// No desc available
IS_EXTENDED: string;

// No desc available
IS_EXPIRED: string;

// No desc available
USER_ID: string;

// No desc available
EXTENSION_DATE: number;

// No desc available
START_DATE: number;

// No desc available
END_DATE: number;

// No desc available
REC_OFFSET: string;

// No desc available
REC_LIMIT: string;

  constructor() {
  super();
    this.PERMIT_NO = "";

this.PERMIT_TYPE = "";

this.DESCRIPTION = "";

this.FACILITY_ID = "";

this.DIVISION_ID = "";

this.TAG = "";

this.AGENT_ID = "";

this.JOB_NO = "";

this.STATUS = "";

this.REQUESTED_BY = "";

this.REQUESTED_ON = null;

this.SOURCE = "";

this.PERMIT_DATE = null;

this.EXPIRY_DATE = null;

this.IS_EXTENDED = "";

this.IS_EXPIRED = "";

this.USER_ID = "";

this.EXTENSION_DATE = null;

this.START_DATE = null;

this.END_DATE = null;

this.REC_OFFSET = "";

this.REC_LIMIT = "";
}
}

export class PERMIT_TYPE_HEADER extends DataStructure {
    	// No desc available
	PERMIT_TYPE: string;

	// No desc available
	FORM_ID: string;

	// No desc available
	FORM_NAME: string;

	// No desc available
	DESCRIPTION: string;

	// No desc available
	PREFIX: string;

	// No desc available
	COLOR: number;

	// No desc available
	ICON: string;

	// No desc available
	P_MODE: string;
    
    constructor() {
    super();
    	this.PERMIT_TYPE = "";

	this.FORM_ID = "";

	this.FORM_NAME = "";

	this.DESCRIPTION = "";

	this.PREFIX = "";

	this.COLOR = null;

	this.ICON = "";

	this.P_MODE = "";
	}
}


export class PERMIT_TYPE_APPROVAL extends DataStructure {
    // No desc available
PERMIT_TYPE: string;

// No desc available
APPR_TYPE: string;

// No desc available
P_MODE: string;
  
  constructor() {
  super();
    this.PERMIT_TYPE = "";

this.APPR_TYPE = "";

this.P_MODE = "";
}
}

export class ROLE_HEADER extends DataStructure {
    // No desc available
ROLE_NAME: string;

// No desc available
IS_INTERNAL: string;

// No desc available
CONFIGURATION: string;

// No desc available
USER_MGMT: string;

// No desc available
AGENT_MGMT: string;

// No desc available
PROC_MGMT: string;

// No desc available
FACILITY_MGMT: string;

// No desc available
REQUEST: string;

// No desc available
REVIEW: string;

// No desc available
APPROVE: string;

// No desc available
ISSUE: string;

// No desc available
EXTEND: string;

// No desc available
EXECUTE: string;

// No desc available
CLOSE: string;

// No desc available
CANCEL: string;

// No desc available
REPORT: string;

// No desc available
P_MODE: string;
  
  constructor() {
  super();
    this.ROLE_NAME = "";

this.IS_INTERNAL = "";

this.CONFIGURATION = "";

this.USER_MGMT = "";

this.AGENT_MGMT = "";

this.PROC_MGMT = "";

this.FACILITY_MGMT = "";

this.REQUEST = "";

this.REVIEW = "";

this.APPROVE = "";

this.ISSUE = "";

this.EXTEND = "";

this.EXECUTE = "";

this.CLOSE = "";

this.CANCEL = "";

this.REPORT = "";

this.P_MODE = "";
}
}

export class STRUCTURE_HEADER extends DataStructure {
    // No desc available
FACILITY_ID: string;

// No desc available
DIVISION_ID: string;

// No desc available
TAG: string;

// No desc available
NAME: string;

// No desc available
CATEGORY: string;

// No desc available
STRUCT_TYPE: string;

// No desc available
STATUS: string;

// No desc available
LATITUDE: number;

// No desc available
LONGITUDE: number;

// No desc available
P_MODE: string;
  
  constructor() {
  super();
    this.FACILITY_ID = "";

this.DIVISION_ID = "";

this.TAG = "";

this.NAME = "";

this.CATEGORY = "";

this.STRUCT_TYPE = "";

this.STATUS = "";

this.LATITUDE = null;

this.LONGITUDE = null;

this.P_MODE = "";
}
}

export class STRUCTURE_DOC extends DataStructure {
    // No desc available
FACILITY_ID: string;

// No desc available
DIVISION_ID: string;

// No desc available
TAG: string;

// No desc available
DOC_ID: string;

// No desc available
DOC_TYPE: string;

// No desc available
THUMBNAIL: string;

// No desc available
P_MODE: string;
  
  constructor() {
  super();
    this.FACILITY_ID = "";

this.DIVISION_ID = "";

this.TAG = "";

this.DOC_ID = "";

this.DOC_TYPE = "";

this.THUMBNAIL = "";

this.P_MODE = "";
}
}

export class STRUCTURE_CAT_HEADER extends DataStructure {
    // No desc available
CATEGORY: string;

// No desc available
DESCRIPTION: string;

// No desc available
P_MODE: string;
  
  constructor() {
  super();
    this.CATEGORY = "";

this.DESCRIPTION = "";

this.P_MODE = "";
}
}

export class STRUCTURE_STATUS_HEADER extends DataStructure {
    // No desc available
STATUS: string;

// No desc available
IS_ACTIVE: string;

// No desc available
DESCRIPTION: string;

// No desc available
P_MODE: string;
  
  constructor() {
  super();
    this.STATUS = "";

this.IS_ACTIVE = "";

this.DESCRIPTION = "";

this.P_MODE = "";
}
}

export class STRUCTURE_TYPE_HEADER extends DataStructure {
    // No desc available
STRUCT_TYPE: string;

// No desc available
DESCRIPTION: string;

// No desc available
P_MODE: string;
  
  constructor() {
  super();
    this.STRUCT_TYPE = "";

this.DESCRIPTION = "";

this.P_MODE = "";
}
}

export class USER_HEADER extends DataStructure {
    // No desc available
USER_ID: string;

// No desc available
FIRST_NAME: string;

// No desc available
LAST_NAME: string;

// No desc available
EMAIL: string;

// No desc available
PHONE: string;

// No desc available
ROLE_NAME: string;

// No desc available
CERTIFICATE_NO: string;

// No desc available
P_MODE: string;

  IS_INTERNAL: string;
  
  constructor() {
  super();
    this.USER_ID = "";

this.FIRST_NAME = "";

this.LAST_NAME = "";

this.EMAIL = "";

this.PHONE = "";

this.ROLE_NAME = "";

this.CERTIFICATE_NO = "";

this.P_MODE = "";

this.IS_INTERNAL = "";
}
}

export class USER_APPROVAL_TYPE extends DataStructure {
    // No desc available
USER_ID: string;

// No desc available
APPR_TYPE: string;

// No desc available
P_MODE: string;
  
  constructor() {
  super();
    this.USER_ID = "";

this.APPR_TYPE = "";

this.P_MODE = "";
}
}

export class USER_CONTEXT_HEADER extends DataStructure {
    // No desc available
USER_ID: string;

// No desc available
FIRST_NAME: string;

// No desc available
LAST_NAME: string;

// No desc available
EMAIL: string;

// No desc available
PHONE: string;

// No desc available
CURRENT_FACILITY: string;

// No desc available
CURRENT_FACILITY_DESC: string;
  
  constructor() {
  super();
    this.USER_ID = "";

this.FIRST_NAME = "";

this.LAST_NAME = "";

this.EMAIL = "";

this.PHONE = "";

this.CURRENT_FACILITY = "";

this.CURRENT_FACILITY_DESC = "";
}
}

export class USER_ROLE extends DataStructure {
    // No desc available
USER_ID: string;

// No desc available
ROLE_NAME: string;

// No desc available
IS_INTERNAL: string;

// No desc available
CONFIGURATION: string;

// No desc available
USER_MGMT: string;

// No desc available
AGENT_MGMT: string;

// No desc available
PROC_MGMT: string;

// No desc available
FACILITY_MGMT: string;

// No desc available
REQUEST: string;

// No desc available
REVIEW: string;

// No desc available
APPROVE: string;

// No desc available
ISSUE: string;

// No desc available
EXTEND: string;

// No desc available
EXECUTE: string;

// No desc available
CLOSE: string;

// No desc available
CANCEL: string;

// No desc available
REPORT: string;
  
  constructor() {
  super();
    this.USER_ID = "";

this.ROLE_NAME = "";

this.IS_INTERNAL = "";

this.CONFIGURATION = "";

this.USER_MGMT = "";

this.AGENT_MGMT = "";

this.PROC_MGMT = "";

this.FACILITY_MGMT = "";

this.REQUEST = "";

this.REVIEW = "";

this.APPROVE = "";

this.ISSUE = "";

this.EXTEND = "";

this.EXECUTE = "";

this.CLOSE = "";

this.CANCEL = "";

this.REPORT = "";
}
}

export class USER_FACILITY extends DataStructure {
    // No desc available
USER_ID: string;

// No desc available
FACILITY_ID: string;

// No desc available
NAME: string;
  
  constructor() {
  super();
    this.USER_ID = "";

this.FACILITY_ID = "";

this.NAME = "";
}
}

export class USER_DIVISION extends DataStructure {
    // No desc available
USER_ID: string;

// No desc available
FACILITY_ID: string;

// No desc available
DIVISION_ID: string;

// No desc available
NAME: string;
  
  constructor() {
  super();
    this.USER_ID = "";

this.FACILITY_ID = "";

this.DIVISION_ID = "";

this.NAME = "";
}
}

export class USER_AGENT extends DataStructure {
    // No desc available
USER_ID: string;

// No desc available
AGENT_ID: string;

// No desc available
NAME: string;

// No desc available
IS_INTERNAL: string;

// No desc available
CONTACT: string;

// No desc available
PHONE: string;

// No desc available
EMAIL: string;

// No desc available
ADDRESS: string;

// No desc available
IS_ACTIVE: string;
  
  constructor() {
  super();
    this.USER_ID = "";

this.AGENT_ID = "";

this.NAME = "";

this.IS_INTERNAL = "";

this.CONTACT = "";

this.PHONE = "";

this.EMAIL = "";

this.ADDRESS = "";

this.IS_ACTIVE = "";
}
}


export class SKILL_HEADER extends DataStructure {
  // No desc available
SKILL_TYPE: string;

// No desc available
DESCRIPTION: string;

// No desc available
P_MODE: string;

constructor() {
super();
  this.SKILL_TYPE = "";

this.DESCRIPTION = "";

this.P_MODE = "";
}
}


export class USER_SKILL_HEADER extends DataStructure {
  // No desc available
USER_ID: string;

// No desc available
SKILL_TYPE: string;

RATING: string;

// No desc available
P_MODE: string;

constructor() {
super();
  this.USER_ID = "";

this.SKILL_TYPE = "";

this.RATING = null;

this.P_MODE = "";
}
}

export class USER_DOC_HEADER extends DataStructure {
  // No desc available
USER_ID: string;

// No desc available
DOC_ID: string;

DOC_CTX: any;

// No desc available
P_MODE: string;

constructor() {
super();
  this.USER_ID = "";

this.DOC_ID = "";

this.DOC_CTX = null;

this.P_MODE = "";
}
}





export interface Skill {
  skill: string;
  rating: number;
  certificates: any;
}

export interface skillsData {
  user_id: string
  username: string;
  External: string;
  skills: Skill[];
}


export interface FormComponent {
  key?: string;
  defaultValue?: string;
  components?: FormComponent[];
  columns?: Column[];
  [key: string]: any; // Allow other properties
}
 
export interface Column {
  components?: FormComponent[];
  [key: string]: any; // Allow other properties
}
 
export interface FormData {
  components?: FormComponent[];
  [key: string]: any; // Allow other properties
}





