import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { DataService } from '../services/data.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class InitGuard implements CanActivate {
  constructor(private dataService: DataService) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Promise<boolean> | Observable<boolean> | boolean {
    return this.checkInitialization();
  }

  private async checkInitialization(): Promise<boolean> {
    try {
      await this.dataService.waitForAppInitialization();
      return true;
    } catch (error) {
      console.error('Initialization failed:', error);
      return false;
    }
  }
}
