import { Component, Input, OnInit } from '@angular/core';
import { <PERSON>ading<PERSON>ontroller, ModalController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { FACILITY_HEADER } from 'src/app/data-models/data_classes';
import { DataService } from 'src/app/services/data.service';
import { MasterSelectedMode } from 'src/app/shared/app-constants';
import { BoundaryPickerComponent } from '../boundary-picker/boundary-picker.component';

@Component({
  selector: 'app-edit-facility',
  templateUrl: './edit-facility.component.html',
  styleUrls: ['./edit-facility.component.scss'],
})
export class EditFacilityComponent implements OnInit {
  @Input() inputFacility: any;
  @Input() mode: any;
  public facility: FACILITY_HEADER;
  public displayError: any;
  public title: string = '';
  public buttonText: string = '';
  public progressbar: boolean = false;

  constructor(
    public modalCtrl: ModalController,
    public dataService: DataService,
    private loadingController: LoadingController,
    private translate: TranslateService
  ) { }

  ngOnInit() {
    this.title = this.mode == MasterSelectedMode.edit ? 'Update Facility' : 'Add Facility';
    this.buttonText = this.mode == MasterSelectedMode.edit ? 'Update' : 'Add';
    this.facility = this.mode == MasterSelectedMode.edit ? { ...this.inputFacility } : new FACILITY_HEADER();
  }

  async addFacility() {
    try {
      this.progressbar = true;
      this.displayError = '';

      this.facility.P_MODE = this.mode == MasterSelectedMode.edit ? 'M' : 'A';
      let customData = {
        FACILITY: [
          {
            FACILITY_HEADER: this.facility,
          },
        ],
      };

      const actionType = this.mode == MasterSelectedMode.edit ? 'updating' : 'adding';
      await this.displayPleaseWaitLoader(
        this.translate.instant(`Please wait, Facility data is being ${actionType}`)
      );

      let result = await this.dataService.addFacility(customData);

      this.loadingController.dismiss();
      this.progressbar = false;

      if (result) {
        this.displayError = result;
      } else {
        // Success - close the modal
        this.modalCtrl.dismiss({
          'success': true,
          'facility': this.facility
        });
      }
    } catch (error) {
      this.loadingController.dismiss();
      this.progressbar = false;
      this.displayError = 'An error occurred. Please try again.';
      console.error('Error in addFacility:', error);
    }
  }

  // Display Loading dialog
  async displayPleaseWaitLoader(messageReceived: string) {
    const loading = await this.loadingController.create({
      message: messageReceived,
      backdropDismiss: false,
    });
    await loading.present();
  }

  closeModal() {
    this.modalCtrl.dismiss();
  }

  async openBoundaryPicker() {
    try {
      // Create a fixed-size modal for the map
      const modal = await this.modalCtrl.create({
        component: BoundaryPickerComponent,
        cssClass: 'boundary-map-modal',
        componentProps: {
          initialBoundary: this.facility.BOUNDARY
        },
        backdropDismiss: false,
        showBackdrop: true,
        // Remove breakpoints to use fixed size
        keyboardClose: false
      });

      await modal.present();

      const result = await modal.onDidDismiss();
      if (result.data) {
        this.facility.BOUNDARY = result.data;
      }
    } catch (error) {
      console.error('Error opening boundary picker:', error);
      this.displayError = 'Error opening boundary picker';
    }
  }
}
