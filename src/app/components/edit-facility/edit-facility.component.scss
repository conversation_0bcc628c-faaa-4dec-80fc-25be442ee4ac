ion-toolbar {
  --color: white;
}

ion-card {
  margin: 0 0 10px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

/* Form layout */
.form-container {
  display: flex;
  flex-direction: column;
  gap: 5px;
  max-width: 100%;
  padding: 15px;
}

.form-row {
  display: flex;
  gap: 20px;
  width: 100%;
  margin-bottom: 5px;
}

.form-group {
  flex: 1;
  min-width: 0;
}

/* Validation styling */
.validation-error {
  color: var(--ion-color-danger);
  font-size: 12px;
  margin-left: 16px;
  margin-bottom: 8px;
}

:host ::ng-deep ion-input.ion-invalid {
  --border-color: var(--ion-color-danger) !important;
}

:host ::ng-deep ion-input.ion-valid {
  --border-color: var(--ion-color-success) !important;
}

/* Boundary field styling */
.boundary-field {
  --background: transparent;
  margin-bottom: 10px;

  ion-label {
    color: var(--ion-color-medium);
    font-size: 14px;
    margin-bottom: 5px;
  }
}

.boundary-input-container {
  display: flex;
  align-items: center;
  width: 100%;
  border: 1px solid var(--ion-color-medium);
  border-radius: 4px;
  overflow: hidden;

  ion-input {
    flex: 1;
    --padding-start: 10px;
    font-size: 14px;
    --placeholder-color: var(--ion-color-medium);
    --placeholder-opacity: 0.7;
  }

  ion-button {
    margin: 0;
    height: 38px;
    --padding-start: 8px;
    --padding-end: 8px;

    ion-icon {
      color: var(--ion-color-primary);
      font-size: 20px;
    }
  }
}

.boundary-note {
  font-size: 12px;
  color: var(--ion-color-medium);
  margin-top: 5px;
  display: flex;
  align-items: center;

  ion-icon {
    margin-right: 5px;
    font-size: 14px;
    color: var(--ion-color-success);
  }
}

.boundary-field {
  ion-label {
    small {
      font-weight: normal;
      opacity: 0.7;
      font-size: 12px;
    }
  }
}

/* Floating label styling */
ion-item {
  --border-color: #ced4da;
  --border-radius: 4px;
  --background: transparent;
  margin-bottom: 0px;
}

ion-label {
  font-weight: 500;
  color: #00629b !important;
}

.error {
  text-align: center;
  color: #d63232;
  margin-top: 10px;
}