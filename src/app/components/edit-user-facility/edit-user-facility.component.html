<ion-header>
  <ion-toolbar color="primary">
    <ion-title>Select Facility</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="closeModal()">
        <i slot="icon-only" class="fal fa-times fa-lg iconWithinButton"></i>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-item [class.selected]="facility.FACILITY_ID === userFacility" mode="ios" [button]="true"
    *ngFor="let facility of facilities; index as i" (click)="updateFacility(facility)">
    <ion-icon slot="start" name="business-outline"></ion-icon>
    <ion-label>
      <h2>{{facility.NAME}}</h2>
      <p>{{facility.FACILITY_ID}}</p>
    </ion-label>
  </ion-item>
</ion-content>