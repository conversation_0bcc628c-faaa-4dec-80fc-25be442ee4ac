import { Component, Input, OnInit } from '@angular/core';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { ModalController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { FACILITY_HEADER } from 'src/app/data-models/data_classes';
import { DataService } from 'src/app/services/data.service';

@Component({
  selector: 'app-edit-user-facility',
  templateUrl: './edit-user-facility.component.html',
  styleUrls: ['./edit-user-facility.component.scss'],
})
export class EditUserFacilityComponent implements OnInit {
  @Input() facilityFromHome: any;
  public facilities: any;
  public selectedFacility: any = {};
  public displayError: string;
  public isUpdatingContent: boolean = false;
  public userFacility: string = '';
  public userObject: any;

  constructor(
    public modalCtrl: ModalController,
    public dataService: DataService,
    private unviredSDK: UnviredCordovaSDK,
    public translate: TranslateService
  ) { }

  async ngOnInit() {
    let result = await this.dataService.getData('USER_FACILITY', null, 'NAME');
    if (result.length > 0) {
      this.facilities = result;
      let userContextResult = await this.dataService.getData(
        'USER_CONTEXT_HEADER'
      );
      this.userObject = userContextResult[0];
      this.userFacility = userContextResult[0]?.CURRENT_FACILITY ? userContextResult[0]?.CURRENT_FACILITY : '';
    }
  }

  async closeModal() {
    this.modalCtrl.dismiss();
  }

  async updateFacility(facility: any) {
    this.userObject.CURRENT_FACILITY = facility.FACILITY_ID;
    this.userObject.CURRENT_FACILITY_DESC = facility.NAME;
    this.userObject.OBJECT_STATUS = 2;
    await this.unviredSDK.dbInsertOrUpdate(
      'USER_CONTEXT_HEADER',
      this.userObject,
      true
    );
    let result: any = await this.dataService.modifyUserContext(
      this.userObject,
      true
    );
    let deleteInspectionsQuery = `DELETE FROM PERMIT_HEADER`;
    await this.unviredSDK.dbExecuteStatement(deleteInspectionsQuery);
    if (result) {
      this.displayError = result;
    }
  }
}
