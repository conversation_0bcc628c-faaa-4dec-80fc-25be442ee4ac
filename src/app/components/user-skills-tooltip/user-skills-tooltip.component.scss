.skills-tooltip {
  min-width: 420px;
  max-width: 550px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  max-height: 80vh;
  overflow-y: auto;

  // Fix white patch on corners by ensuring proper border radius inheritance
  * {
    box-sizing: border-box;
  }

  // Ensure the first child (header) has proper top border radius
  > :first-child {
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
  }

  // Ensure the last child has proper bottom border radius
  > :last-child {
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
  }
}

.tooltip-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  .user-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .user-icon {
      font-size: 20px;
      color: rgba(255, 255, 255, 0.9);
    }

    .user-name {
      font-size: 14px;
      font-weight: 600;
      color: white;
    }
  }

  .close-btn {
    --color: rgba(255, 255, 255, 0.8);
    --padding-start: 4px;
    --padding-end: 4px;
    margin: 0;
    
    ion-icon {
      font-size: 18px;
    }

    &:hover {
      --color: white;
    }
  }
}

.tooltip-content {
  padding: 16px;
}

.skills-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;

  .skills-icon {
    font-size: 16px;
    color: #6366f1;
  }

  .skills-title {
    font-size: 13px;
    font-weight: 600;
    color: #374151;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.skills-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.skill-item {
  padding: 16px 0;
  border-bottom: 1px solid #f3f4f6;

  &:last-child {
    border-bottom: none;
  }

  .skill-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .skill-info {
      flex: 1;
      min-width: 0;

      .skill-name {
        font-size: 15px;
        font-weight: 600;
        color: #1f2937;
        line-height: 1.3;
        margin-bottom: 2px;
      }

      .skill-type {
        font-size: 11px;
        color: #6b7280;
        font-style: italic;
      }

      .skill-type-badge {
        margin-top: 4px;
        --background: #6366f1;
        --color: white;
        height: 20px;
        font-size: 10px;

        ion-label {
          font-size: 10px;
          font-weight: 500;
          margin: 0;
        }
      }
    }

    .skill-rating {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 2px;

      .stars {
        display: flex;
        gap: 1px;

        ion-icon {
          font-size: 14px;
          transition: color 0.2s ease;
        }
      }

      .rating-value {
        font-size: 10px;
        color: #6b7280;
        font-weight: 500;
      }
    }
  }
}

// Certificates Section
.certificates-section {
  margin-top: 8px;

  .certificates-header {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 8px;

    .certificates-icon {
      font-size: 14px;
      color: #f59e0b;
    }

    .certificates-title {
      font-size: 12px;
      font-weight: 600;
      color: #374151;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  .certificates-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .certificate-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: #f8fafc;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e2e8f0;

    &:hover {
      background: #e2e8f0;
      border-color: #cbd5e1;
      transform: translateY(-1px);
    }

    .certificate-info {
      display: flex;
      align-items: center;
      gap: 10px;
      flex: 1;
      min-width: 0;

      .file-icon {
        font-size: 18px;
        color: #6366f1;
        flex-shrink: 0;
      }

      .certificate-details {
        flex: 1;
        min-width: 0;

        .certificate-name {
          font-size: 13px;
          font-weight: 500;
          color: #1f2937;
          line-height: 1.3;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .certificate-meta {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-top: 2px;

          .certificate-date {
            font-size: 11px;
            color: #6b7280;
          }
        }
      }
    }

    .open-icon {
      font-size: 16px;
      color: #6b7280;
      flex-shrink: 0;
      transition: color 0.2s ease;
    }

    &:hover .open-icon {
      color: #6366f1;
    }
  }
}

// No Certificates Message
.no-certificates {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
  margin-top: 8px;

  .no-cert-icon {
    font-size: 16px;
    color: #9ca3af;
  }

  .no-cert-text {
    font-size: 12px;
    color: #6b7280;
    font-style: italic;
  }
}

.no-skills {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  text-align: center;
  color: #6b7280;

  .info-icon {
    font-size: 20px;
    color: #9ca3af;
  }

  .no-skills-text {
    font-size: 13px;
    font-style: italic;
  }
}

// Responsive adjustments
@media (max-width: 600px) {
  .skills-tooltip {
    min-width: 320px;
    max-width: 400px;
  }

  .tooltip-header {
    padding: 12px;

    .user-info .user-name {
      font-size: 13px;
    }
  }

  .tooltip-content {
    padding: 12px;
  }

  .skill-item {
    .skill-header .skill-info .skill-name {
      font-size: 14px;
    }

    .skill-header .skill-rating .stars ion-icon {
      font-size: 12px;
    }

    .certificates-section .certificate-item {
      padding: 6px 10px;

      .certificate-info .certificate-details .certificate-name {
        font-size: 12px;
      }
    }
  }
}

@media (max-width: 480px) {
  .skills-tooltip {
    min-width: 280px;
    max-width: 350px;
  }
}

// Animation for smooth appearance
.skills-tooltip {
  animation: fadeInScale 0.2s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// Global styles for the popover (needs to be in global.scss or here with :global)
:global(.skills-tooltip-popover) {
  --width: auto !important;
  --min-width: 450px !important;
  --max-width: 600px !important;

  .popover-content {
    width: auto !important;
    min-width: 450px !important;
    max-width: 600px !important;
    max-height: 80vh !important;
    overflow-y: auto !important;
  }

  // Better positioning to prevent cutoff
  .popover-wrapper {
    position: fixed !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
    z-index: 20000 !important;
  }

  @media (max-width: 768px) {
    --min-width: 350px !important;
    --max-width: 450px !important;

    .popover-content {
      min-width: 350px !important;
      max-width: 450px !important;
    }
  }

  @media (max-width: 480px) {
    --min-width: 300px !important;
    --max-width: 380px !important;

    .popover-content {
      min-width: 300px !important;
      max-width: 380px !important;
    }

    .popover-wrapper {
      left: 10px !important;
      right: 10px !important;
      top: 50% !important;
      transform: translateY(-50%) !important;
      width: calc(100vw - 20px) !important;
    }
  }
}
