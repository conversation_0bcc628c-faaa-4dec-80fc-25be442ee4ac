<div class="skills-tooltip">
  <!-- Header -->
  <div class="tooltip-header">
    <div class="user-info">
      <ion-icon name="person-circle-outline" class="user-icon"></ion-icon>
      <span class="user-name">{{ userName }}</span>
    </div>
    <ion-button fill="clear" size="small" (click)="close()" class="close-btn">
      <ion-icon slot="icon-only" name="close"></ion-icon>
    </ion-button>
  </div>

  <!-- Skills Content -->
  <div class="tooltip-content">
    <div class="skills-header">
      <ion-icon name="construct-outline" class="skills-icon"></ion-icon>
      <span class="skills-title">Skills & Ratings</span>
    </div>

    <!-- Skills List -->
    <div class="skills-list" *ngIf="skills.length > 0">
      <div class="skill-item" *ngFor="let skill of skills">
        <div class="skill-header">
          <div class="skill-info">
            <div class="skill-name">{{ skill.description || skill.skillType }}</div>
            <ion-chip class="skill-type-badge" color="primary" *ngIf="skill.skillType">
              <ion-label>{{ skill.skillType }}</ion-label>
            </ion-chip>
          </div>
          <div class="skill-rating">
            <div class="stars">
              <ion-icon
                *ngFor="let star of getStarRating(skill.rating)"
                [name]="star"
                [style.color]="getRatingColor(skill.rating)">
              </ion-icon>
            </div>
            <span class="rating-value">{{ skill.rating }}/5</span>
          </div>
        </div>

        <!-- Certificates Section -->
        <div class="certificates-section" *ngIf="skill.certificates && skill.certificates.length > 0">
          <div class="certificates-header">
            <ion-icon name="ribbon-outline" class="certificates-icon"></ion-icon>
            <span class="certificates-title">Certificates ({{ skill.certificates.length }})</span>
          </div>
          <div class="certificates-list">
            <div
              class="certificate-item"
              *ngFor="let certificate of skill.certificates"
              (click)="openCertificate(certificate)">
              <div class="certificate-info">
                <ion-icon [name]="getFileIcon(certificate.mimeType)" class="file-icon"></ion-icon>
                <div class="certificate-details">
                  <div class="certificate-name">{{ certificate.fileName }}</div>
                  <div class="certificate-meta">
                    <span class="certificate-date">{{ formatDate(certificate.createdOn) }}</span>
                  </div>
                </div>
              </div>
              <ion-icon name="open-outline" class="open-icon"></ion-icon>
            </div>
          </div>
        </div>

        <!-- No Certificates Message -->
        <div class="no-certificates" *ngIf="!skill.certificates || skill.certificates.length === 0">
          <ion-icon name="document-outline" class="no-cert-icon"></ion-icon>
          <span class="no-cert-text">No certificates available</span>
        </div>
      </div>
    </div>

    <!-- No Skills Message -->
    <div class="no-skills" *ngIf="skills.length === 0">
      <ion-icon name="information-circle-outline" class="info-icon"></ion-icon>
      <span class="no-skills-text">No skills found for this user</span>
    </div>
  </div>
</div>
