import { Component, Input, OnInit } from '@angular/core';
import { PopoverController } from '@ionic/angular';

@Component({
  selector: 'app-user-skills-tooltip',
  templateUrl: './user-skills-tooltip.component.html',
  styleUrls: ['./user-skills-tooltip.component.scss'],
})
export class UserSkillsTooltipComponent implements OnInit {
  @Input() skills: any[] = [];
  @Input() userName: string = '';
  @Input() onCertificateClick: (certificate: any) => void;

  constructor(private popoverController: PopoverController) { }

  ngOnInit() {
    console.log('User Skills Tooltip initialized with:', this.skills, this.userName);
  }

  close() {
    this.popoverController.dismiss();
  }

  // Handle certificate click
  openCertificate(certificate: any) {
    if (this.onCertificateClick) {
      this.onCertificateClick(certificate);
    }
  }

  // Get file icon based on mime type
  getFileIcon(mimeType: string): string {
    if (!mimeType) return 'document-outline';

    const type = mimeType.toLowerCase();
    if (type.includes('pdf')) return 'document-text-outline';
    if (type.includes('image')) return 'image-outline';
    if (type.includes('word') || type.includes('doc')) return 'document-outline';
    if (type.includes('excel') || type.includes('sheet')) return 'grid-outline';
    if (type.includes('powerpoint') || type.includes('presentation')) return 'easel-outline';

    return 'document-outline';
  }

  // Format file size
  formatFileSize(bytes: number): string {
    if (!bytes) return '';

    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  // Format date
  formatDate(dateString: string): string {
    if (!dateString) return '';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch (error) {
      return dateString;
    }
  }

  // Get star rating display
  getStarRating(rating: number): string[] {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    // Add full stars
    for (let i = 0; i < fullStars; i++) {
      stars.push('star');
    }
    
    // Add half star if needed
    if (hasHalfStar) {
      stars.push('star-half');
    }
    
    // Add empty stars to make total of 5
    const remainingStars = 5 - Math.ceil(rating);
    for (let i = 0; i < remainingStars; i++) {
      stars.push('star-outline');
    }
    
    return stars;
  }

  // Get rating color based on value
  getRatingColor(rating: number): string {
    if (rating >= 4) return '#22c55e'; // Green
    if (rating >= 3) return '#eab308'; // Yellow
    if (rating >= 2) return '#f97316'; // Orange
    return '#ef4444'; // Red
  }
}
