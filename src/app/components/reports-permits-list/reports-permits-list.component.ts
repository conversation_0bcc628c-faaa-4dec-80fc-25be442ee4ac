import { Component, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { PermitDetailsComponent } from '../permit-details/permit-details.component';
import { PermitStatus, PermitStatusProgress } from 'src/app/shared/app-constants';

@Component({
  selector: 'app-reports-permits-list',
  templateUrl: './reports-permits-list.component.html',
  styleUrls: ['./reports-permits-list.component.scss'],
})
export class ReportsPermitsListComponent implements OnInit {
  permits: any;
  title: string;
  allPermits:any

  constructor(private modalController: ModalController,) { }

  ngOnInit() { }

  closeModal() {
    this.modalController.dismiss(false, null, 'report-permit-list');
  }

  async showPermitDetails(permit: any) {
    // localStorage.setItem('permitDetailsFromreport', JSON.stringify(permit));
    this.allPermits.forEach((ele, i) => {
      if(ele.PERMIT_HEADER && ele.PERMIT_HEADER.PERMIT_NO && ele.PERMIT_HEADER.PERMIT_NO === permit.PERMIT_NO){
        localStorage.setItem('permitDetailsFromreport', JSON.stringify(ele));
        }
      });
    localStorage.setItem('selectedPermit', JSON.stringify(permit));
    let userRole: string = '';
    let isShowModal: boolean = true;
    let isProcessSwitchCase: boolean = true;

    // Show inspection details modal based on status
    if (isShowModal) {
      const modal = await this.modalController.create({
        component: PermitDetailsComponent,
        cssClass: 'full-screen-modal',
        backdropDismiss: false,
        id: 'permit-details',
        componentProps: {
          userRole: userRole,
          permit: permit,
          isReport: true
        },
      });
      modal.onDidDismiss().then(async (result) => { });
      return await modal.present();
    }
  }

  getLabelBasedOnStatus(status: string) {
    let label = '';
    switch (status.trim()) {
      case PermitStatus.OPEN:
        label = 'Open';
        break;
      case PermitStatus.IN_REVIEW:
        label = 'In Review';
        break;
      case PermitStatus.CANCELLED:
        label = 'Canceled';
        break;
      case PermitStatus.APPROVED:
        label = 'Approved';
        break;
      case PermitStatus.ISSUED:
        label = 'Issued';
        break;
      case PermitStatus.CLOSED:
        label = 'Closed';
        break;
      default:
        label = 'Open';
        break;
    }
    return label;
  }

  GetProgressPercentCssClass(status: string) {
    let cssClass = '';
    switch (status.trim()) {
      case PermitStatus.OPEN:
        cssClass = 'orange';
        break;
      case PermitStatus.IN_REVIEW:
        cssClass = 'orange';
        break;
      case PermitStatus.CANCELLED:
        cssClass = 'light-Grey';
        break;
      case PermitStatus.CLOSED:
        cssClass = 'darak-Green';
        break;
      case PermitStatus.APPROVED:
        cssClass = 'light-Green';
        break;
      case PermitStatus.ISSUED:
        cssClass = 'darak-Green';
        break;
      default:
        cssClass = 'orange';
        break;
    }
    return cssClass;
  }

  permitStatusProgress(status: string) {
    let percentage = 0;
    switch (status.trim()) {
      case PermitStatus.OPEN:
        percentage = PermitStatusProgress.OPEN;
        break;
      case PermitStatus.IN_REVIEW:
        percentage = PermitStatusProgress.IN_REVIEW;
        break;
      case PermitStatus.APPROVED:
        percentage = PermitStatusProgress.APPROVED;
        break;
      case PermitStatus.ISSUED:
        percentage = PermitStatusProgress.ISSUED;
        break;
      case PermitStatus.CLOSED:
        percentage = PermitStatusProgress.COMPLETED;
        break;
      case PermitStatus.CANCELLED:
        percentage = PermitStatusProgress.CANCELLED;
        break;
      default:
        percentage = PermitStatusProgress.OPEN;
        break;
    }
    return percentage;
  }
  
  GetBorderCssClass(status: string) {
    let cssClass = '';
    switch (status.trim()) {
      case PermitStatus.OPEN:
        cssClass = 'orange-border';
        break;
      case PermitStatus.IN_REVIEW:
        cssClass = 'orange-border';
        break;
      case PermitStatus.CANCELLED:
        cssClass = 'light-Grey-border';
        break;
      case PermitStatus.CLOSED:
        cssClass = 'darak-Green-border';
        break;
      case PermitStatus.APPROVED:
        cssClass = 'light-Green-border';
        break;
      case PermitStatus.ISSUED:
        cssClass = 'darak-Green-border';
        break;
      default:
        cssClass = 'orange-border';
        break;
    }
    return cssClass;
  }
}
