ion-toolbar {
  padding-inline: 10px;
  padding-top: 0px;
  padding-bottom: 0px;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 0.0125em;
}

p {
  margin: 0 !important;
}

// Status colors
.orange {
  --progress-background: orange;
  color: orange;
}

.light-Green {
  --progress-background: rgb(105, 226, 105);
  color: rgb(105, 226, 105);
}

.darak-Green {
  --progress-background: rgb(13, 172, 13);
  color: rgb(13, 172, 13);
}

.light-Grey {
  --progress-background: indianred;
  color: indianred;
}

// Table styles from permits page
.permits-table-wrapper {
  overflow-x: auto;
  padding: 0 16px;
}

.permits-table {
  width: 100%;
  margin-bottom: 20px;
  background-color: transparent;
}

.table-header {
  display: flex;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
  font-weight: 600;
  color: #475569;
  height: 40px;
  align-items: center;
  border-bottom: 1px solid #e2e8f0;
}

.table-row {
  display: flex;
  background-color: white;
  border-radius: 0;
  margin-bottom: 1px;
  height: 60px;
  align-items: center;
  border-bottom: 1px solid #e2e8f0;
  transition: background-color 0.2s ease-in-out;
  cursor: pointer;
  position: relative;

  &:hover {
    background-color: #f8fafc;
  }

  &:last-child {
    border-radius: 0 0 8px 8px;
  }
}

.header-cell, .table-cell {
  padding: 8px 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-cell {
  width: 120px;
  flex: 0 0 120px;
}

.permit-no-cell {
  width: 150px;
  flex: 0 0 150px;
}

.desc-cell {
  flex: 1;
  min-width: 200px;
}

.type-cell {
  width: 120px;
  flex: 0 0 120px;
}

.date-cell {
  width: 180px;
  flex: 0 0 180px;
}

.requester-cell {
  width: 280px;
  flex: 0 0 280px;
}

.actions-cell {
  width: 90px;
  flex: 0 0 90px;
  text-align: center;
}

.status-wrapper {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  white-space: nowrap;
}

.outline-badge {
  background-color: transparent;
  border: 1px solid currentColor;
}

.extended-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  background-color: #3b82f6;
  color: white;
}

// Empty state styling
.no-results-message {
  display: flex;
  justify-content: center;
  padding: 40px 0;
}

.no-results-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #64748b;

  ion-icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: #94a3b8;
  }

  p {
    font-size: 16px;
    margin-bottom: 16px;
  }
}

// Responsive styles
@media (max-width: 1200px) {
  .permits-table {
    margin: 0 0 20px;
  }

  .permits-table-wrapper {
    overflow-x: auto;
  }

  .table-header, .table-row {
    min-width: 1300px;
  }
}

@media (max-width: 768px) {
  .table-header {
    display: none;
  }

  .table-row {
    flex-direction: column;
    height: auto;
    min-width: auto;
    align-items: flex-start;
    padding: 15px;
  }

  .table-cell {
    width: 100% !important;
    min-width: auto !important;
    flex: none !important;
    padding: 5px 0;
    white-space: normal;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .table-cell:before {
    content: attr(data-label);
    font-weight: 600;
    width: 40%;
    color: #64748b;
  }
}