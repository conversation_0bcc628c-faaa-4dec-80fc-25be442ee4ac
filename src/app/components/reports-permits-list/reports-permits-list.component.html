<ion-header style="box-shadow: none !important;border-bottom: 1px solid gainsboro;">
  <ion-toolbar color="primary">
    <ion-buttons slot="start" mode="ios">
      <ion-button (click)="closeModal()">
        <ion-icon slot="icon-only" name="arrow-back-outline" style="font-size: 24px;padding-left: 5px;"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title style="font-weight: 600;font-size: large;padding-inline: 0px;">{{title}} Permits
      ({{permits.length}})</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content style="--background: #f6f7f8;--offset-top: 0px !important;">
  <div class="permits-table-wrapper">
    <div class="permits-table">
      <div class="table-header">
        <div class="header-cell status-cell">Status</div>
        <div class="header-cell permit-no-cell">Permit No.</div>
        <div class="header-cell desc-cell">Description</div>
        <div class="header-cell type-cell">Type</div>
        <div class="header-cell date-cell">Start Date</div>
        <div class="header-cell date-cell">End Date</div>
        <div class="header-cell requester-cell">Requested By</div>
        <div class="header-cell actions-cell">Actions</div>
      </div>

      <!-- Show filtered results when available -->
      <div class="table-row"
           *ngFor="let permit of permits"
           (click)="showPermitDetails(permit)">
        <div class="table-cell status-cell" data-label="Status">
          <div class="status-wrapper">
            <div class="status-badge outline-badge" [ngClass]="GetProgressPercentCssClass(permit.STATUS)">
              {{ getLabelBasedOnStatus(permit.STATUS) }}
            </div>
            <div *ngIf="permit.IS_EXTENDED == 'true' || permit.IS_EXTENDED == true" class="extended-badge">Extended</div>
          </div>
        </div>
        <div class="table-cell permit-no-cell" data-label="Permit No.">{{ permit.PERMIT_NO }}</div>
        <div class="table-cell desc-cell" data-label="Description">{{ permit.DESCRIPTION }}</div>
        <div class="table-cell type-cell" data-label="Type">{{ permit.TYPE_DESC || permit.PERMIT_TYPE }}</div>
        <div class="table-cell date-cell" data-label="Start Date">
          {{ permit.PERMIT_DATE | GetFullDateByTimestampPipeReport | async }}
        </div>
        <div class="table-cell date-cell" data-label="End Date">
          {{ permit.EXPIRY_DATE | GetFullDateByTimestampPipeReport | async }}
        </div>
        <div class="table-cell requester-cell" data-label="Requested By">
          {{ permit.REQUESTED_BY | GetRequestedByNamePipe | async }}, {{ permit.REQUESTED_ON | date }}
        </div>
        <div class="table-cell actions-cell" data-label="Actions">
          <ion-button size="small" fill="clear" (click)="showPermitDetails(permit); $event.stopPropagation();">
            Details
            <ion-icon slot="end" name="chevron-forward-outline"></ion-icon>
          </ion-button>
        </div>
      </div>
    </div>

    <!-- Show empty state message when filtered list is empty -->
    <div *ngIf="permits.length === 0" class="no-results-message">
      <div class="no-results-content">
        <ion-icon name="document-text-outline"></ion-icon>
        <p>No Reports Found</p>
      </div>
    </div>
  </div>
</ion-content>