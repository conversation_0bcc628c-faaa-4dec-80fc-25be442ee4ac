<div class="modal-header">
  <div class="title">{{ title }}</div>
  <button class="close-button" (click)="closeModal()">✕</button>
</div>
<ion-progress-bar type="indeterminate" *ngIf="progressbar"></ion-progress-bar>

<div class="form-container">
    <div class="form-row">
      <div class="form-group">
        <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;"
          placeholder="Enter Structure Tag" [disabled]="isUpdatingStructure" [(ngModel)]="structureData.TAG"
          required="true" labelPlacement="stacked" fill="outline">
          <div slot="label">Structure Tag <ion-text color="danger">*</ion-text></div>
          <ion-icon slot="start" name="pricetag-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
        </ion-input>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;"
          placeholder="Enter Structure Description" [(ngModel)]="structureData.NAME" maxlength="100"
          required="true" labelPlacement="stacked" fill="outline">
          <div slot="label">Structure Description <ion-text color="danger">*</ion-text></div>
          <ion-icon slot="start" name="document-text-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
        </ion-input>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <ion-select style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;" interface="popover"
          placeholder="Select Structure Category" fill="outline" labelPlacement="stacked"
          [(ngModel)]="selectedCategoryValue">
          <div slot="label">Structure Category <ion-text color="danger">*</ion-text></div>
          <ion-icon slot="start" name="list-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
          <ion-select-option *ngFor="let category of structureCategories" [value]="category.CATEGORY">
            {{category.CATEGORY}} - {{category.DESCRIPTION}}
          </ion-select-option>
        </ion-select>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <ion-select style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;" interface="popover"
          placeholder="Select Structure Type" fill="outline" labelPlacement="stacked"
          [(ngModel)]="selectedTypeValue">
          <div slot="label">Structure Type <ion-text color="danger">*</ion-text></div>
          <ion-icon slot="start" name="construct-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
          <ion-select-option *ngFor="let type of structureTypes" [value]="type.STRUCT_TYPE">
            {{type.STRUCT_TYPE}} - {{type.DESCRIPTION}}
          </ion-select-option>
        </ion-select>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <ion-select style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;" interface="popover"
          placeholder="Select Structure Status" fill="outline" labelPlacement="stacked"
          [(ngModel)]="selectedStatusValue">
          <div slot="label">Structure Status <ion-text color="danger">*</ion-text></div>
          <ion-icon slot="start" name="stats-chart-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
          <ion-select-option *ngFor="let status of structureStatus" [value]="status.STATUS">
            {{status.STATUS}} - {{status.DESCRIPTION}}
          </ion-select-option>
        </ion-select>
      </div>
    </div>

    <!-- Location Fields -->
    <div class="form-row location-fields">
      <div class="form-group">
        <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;"
          placeholder="Latitude" [(ngModel)]="structureData.LATITUDE"
          type="number" step="0.000001" labelPlacement="stacked" fill="outline"
          [disabled]="!facilityHasBoundary">
          <div slot="label">Latitude</div>
          <ion-icon slot="start" name="location-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
        </ion-input>
      </div>
      <div class="form-group">
        <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;"
          placeholder="Longitude" [(ngModel)]="structureData.LONGITUDE"
          type="number" step="0.000001" labelPlacement="stacked" fill="outline"
          [disabled]="!facilityHasBoundary">
          <div slot="label">Longitude</div>
          <ion-icon slot="start" name="navigate-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
        </ion-input>
      </div>
    </div>

    <!-- Location Warning Message -->
    <div class="form-row" *ngIf="!facilityHasBoundary">
      <div class="form-group location-warning">
        <ion-text color="warning">
          <ion-icon name="warning-outline"></ion-icon>
          Location selection is disabled because the facility boundary is not defined.
        </ion-text>
      </div>
    </div>

    <!-- Map Button -->
    <div class="form-row">
      <div class="form-group">
        <ion-button class="map-button" (click)="openLocationPicker()" [disabled]="!facilityHasBoundary">
          <ion-icon name="map-outline" slot="start"></ion-icon>
          Select Location on Map
        </ion-button>
      </div>
    </div>

    <p class="error">{{displayError | translate}}</p>
  </div>

<div class="footer-buttons">
  <button class="cancel-button" (click)="closeModal()">Cancel</button>
  <button class="save-button"
    [disabled]="!structureData.TAG || !structureData.NAME || !selectedCategoryValue || !selectedTypeValue || !selectedStatusValue"
    (click)="saveStructure()">Save</button>
</div>
