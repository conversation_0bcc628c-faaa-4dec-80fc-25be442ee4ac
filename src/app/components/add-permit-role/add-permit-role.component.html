<ion-header>
  <ion-toolbar mode="ios" color="primary">
    <ion-title>Assign User For Review</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="cancel()">Close</ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>
<ion-content>
  <ion-segment [(ngModel)]="segmentValue" style="margin: 0px 25px 0px 20px;width: auto;" mode="md">
    <ion-segment-button value="Internal">
      <ion-label style="font-weight: 600;">Internal</ion-label>
    </ion-segment-button>
    <ion-segment-button value="External" *ngIf="isExternal">
      <ion-label style="font-weight: 600;">External</ion-label>
    </ion-segment-button>
  </ion-segment>

  <div [ngSwitch]="segmentValue">
    <div *ngSwitchCase="'Internal'" >
      <ion-list>
        <ion-item mode="ios" [button]="true" (click)="selectedUser(permit)" *ngFor="let permit of internalUsers">
          <ion-icon color="primary" slot="start" name="person-circle-outline" size="large"></ion-icon>
          <ion-label>{{permit.FIRST_NAME}} {{permit.LAST_NAME}}</ion-label>
        </ion-item>
      </ion-list>
    </div>

      <div *ngSwitchCase="'External'" >
        <ion-list>
          <ion-item mode="ios" [button]="true" (click)="selectedUser(permit)" *ngFor="let permit of externalUsers">
            <ion-icon color="primary" slot="start" name="person-circle-outline" size="large"></ion-icon>
            <ion-label>{{permit.FIRST_NAME}} {{permit.LAST_NAME}}</ion-label>
          </ion-item>
        </ion-list>
      </div>  
  </div>

  
</ion-content>