<ion-header mode="ios">
  <ion-toolbar color="primary" mode="ios">
    <ion-title>Edit Details</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="cancel()"> <ion-icon slot="icon-only" name="close"></ion-icon></ion-button>
    </ion-buttons>
  </ion-toolbar>
  <ion-progress-bar type="indeterminate" *ngIf="progressbar"></ion-progress-bar>
</ion-header>

<ion-content class="ion-padding">
  <form [formGroup]="createForm">
    <ion-input [value]="permitHeader.STATUS | StatusTextFormatFilterPipe" readonly="true"
      style="min-height: 45px !important;--padding-start: 16px !important;margin-bottom: 20px;"
      labelPlacement="floating" fill="outline">
      <div slot="label">Status</div>
    </ion-input>

    <ion-select style="--padding-start: 16px !important;min-height: 43px;margin-bottom: 20px;" interface="popover"
      placeholder="Select Permit Type" fill="outline" required="true" labelPlacement="stacked"
      formControlName="permitType">
      <div slot="label">Select Permit Type <ion-text color="danger">*</ion-text></div>
      <ion-select-option value="" class="selectClearOption">Select</ion-select-option>
      <ion-select-option *ngFor="let permit of permitTypesList"
        [value]="permit.PERMIT_TYPE">{{permit.DESCRIPTION}}</ion-select-option>
    </ion-select>

    <ion-select placeholder="Select Facility" style="--padding-start: 16px !important;min-height: 43px;margin-bottom: 20px;"
      interface="popover" fill="outline" formControlName="facility" required="true" label-placement="stacked">
      <div slot="label">Select Facility <ion-text color="danger">*</ion-text></div>
      <ion-select-option value="" class="selectClearOption">Select</ion-select-option>
      <ion-select-option *ngFor="let facility of facilitiesList" [value]="facility.FACILITY_ID">{{facility.FACILITY_ID}}
        -
        {{facility.NAME}}</ion-select-option>
    </ion-select>

    <ion-select (ionChange)="divisionChange()" placeholder="Select Division"
      style="--padding-start: 16px !important;min-height: 43px;margin-bottom: 20px;" interface="popover" fill="outline"
      formControlName="division" required="true" label-placement="stacked">
      <div slot="label">Select Division <ion-text color="danger">*</ion-text></div>
      <ion-select-option value="" class="selectClearOption">Select</ion-select-option>
      <ion-select-option *ngFor="let division of divisionsList"
        [value]="division.DIVISION_ID">{{division.NAME}}</ion-select-option>
    </ion-select>

    <ion-select style="--padding-start: 16px !important;min-height: 43px;margin-bottom: 20px;" interface="popover"
      placeholder="Select Structure Tag" fill="outline" formControlName="structureTag" required="true"
      label-placement="stacked">
      <div slot="label">Select Structure Tag <ion-text color="danger">*</ion-text></div>
      <ion-select-option value="" class="selectClearOption">Select</ion-select-option>
      <ion-select-option *ngFor="let structure of structuresArray"
        [value]="structure.TAG">{{structure.NAME}}</ion-select-option>
    </ion-select>

    <ion-input style="--padding-start: 16px !important;min-height: 43px;margin-bottom: 20px;" maxlength="40" placeholder="Enter Description"
      formControlName="description" required="true" labelPlacement="stacked" fill="outline">
      <div slot="label">Enter Description <ion-text color="danger">*</ion-text></div>
    </ion-input>

    <ion-input style="--padding-start: 16px !important;min-height: 43px;margin-bottom: 20px;" maxlength="20" placeholder="Enter Job Number"
      formControlName="jobNumber" labelPlacement="stacked" fill="outline">
      <div slot="label">Enter Job Number</div>
    </ion-input>
  </form>

  <div style="margin-top: 2%;text-align: center;">
    <ion-text style="color: indianred;" class="textCenter" *ngIf="errorMessage.length > 0">
      <span>{{errorMessage}}</span>
    </ion-text>
  </div>
</ion-content>

<ion-footer mode="ios">
  <ion-toolbar mode="ios">
    <ion-button slot="end" color="danger" mode="md" (click)="cancel()">Cancel</ion-button>
    <ion-button slot="end" color="success" mode="md" [disabled]="!createForm.valid" (click)="save()">Save</ion-button>
  </ion-toolbar>
</ion-footer>