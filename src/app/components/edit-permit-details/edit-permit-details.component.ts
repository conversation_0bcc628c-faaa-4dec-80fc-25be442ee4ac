import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { ModalController } from '@ionic/angular';
import { DIVISION_HEADER, FACILITY_HEADER, PERMIT_HEADER, PERMIT_TYPE_HEADER, STRUCTURE_HEADER } from 'src/app/data-models/data_classes';
import { DataService } from 'src/app/services/data.service';
import * as moment from 'moment';

@Component({
  selector: 'app-edit-permit-details',
  templateUrl: './edit-permit-details.component.html',
  styleUrls: ['./edit-permit-details.component.scss'],
})
export class EditPermitDetailsComponent implements OnInit {
  public permitHeader: any;
  public permitTypesList: PERMIT_TYPE_HEADER[] = [];
  public facilitiesList: FACILITY_HEADER[] = [];
  public createForm: FormGroup;
  public structuresArray: STRUCTURE_HEADER[] = [];
  public divisionsList: DIVISION_HEADER[] = [];
  public errorMessage: string = '';
  public progressbar: boolean = false;

  constructor(public modalController: ModalController,
    public dataService: DataService,
    public unviredSDK: UnviredCordovaSDK,) {
    this.createForm = new FormGroup({
      permitType: new FormControl('', Validators.required),
      facility: new FormControl('', Validators.required),
      division: new FormControl(
        { value: '', disabled: true },
        Validators.required
      ),
      description: new FormControl('', Validators.required),
      structureTag: new FormControl(
        { value: '', disabled: true },
        Validators.required
      ),
      jobNumber: new FormControl(''),
    });

    this.createForm.controls['facility'].valueChanges.subscribe((value) => {
      this.createForm.controls['division'].setValue('');
      this.createForm.controls['structureTag'].setValue('');
      if (value) {
        this.createForm.controls['division'].enable();
      } else {
        this.createForm.controls['division'].disable();
        this.createForm.controls['structureTag'].disable();
      }
    });

    this.createForm.controls['division'].valueChanges.subscribe((value) => {
      this.createForm.controls['structureTag'].setValue('');
      if (value) {
        this.createForm.controls['structureTag'].enable();
      } else {
        this.createForm.controls['structureTag'].disable();
      }
    });
  }

  async ngOnInit() {
    this.setFormValues();
    await this.getPermitTypes();
    await this.getFacilities();
    await this.getDivisions();
    await this.getStructureTags();
  }

  setFormValues() {
    this.createForm.controls['permitType'].setValue(this.permitHeader.PERMIT_TYPE);
    this.createForm.controls['facility'].setValue(this.permitHeader.FACILITY_ID);
    this.createForm.controls['division'].setValue(this.permitHeader.DIVISION_ID);
    this.createForm.controls['description'].setValue(this.permitHeader.DESCRIPTION);
    this.createForm.controls['structureTag'].setValue(this.permitHeader.TAG);
    this.createForm.controls['jobNumber'].setValue(this.permitHeader.JOB_NO);
  }

  cancel() { this.modalController.dismiss(false, '', 'edit-permit-basic-details') }

  async save() {
    this.progressbar = true;
    let permitUpdateQuery = `UPDATE PERMIT_HEADER SET P_MODE = 'M', OBJECT_STATUS = 2 WHERE PERMIT_NO = '${this.permitHeader.PERMIT_NO}'`;
    let permitUpdateQueryResult = await this.unviredSDK.dbExecuteStatement(
      permitUpdateQuery
    );
    if (permitUpdateQueryResult.type == ResultType.success) {
      this.permitHeader.P_MODE = 'M';
      this.permitHeader.OBJECT_STATUS = 2;
    }
    let isAvailableButton = this.permitHeader.hasOwnProperty('isShowDetailsButton');
    if (isAvailableButton) {
      delete this.permitHeader.isShowDetailsButton;
    }
    let isShowReviseButton = this.permitHeader.hasOwnProperty('isShowReviseButton');
    if (isShowReviseButton) {
      delete this.permitHeader.isShowReviseButton;
    }
    // Remove permitTypeInfo as it's only used for display purposes and should not be sent to server
    let hasPermitTypeInfo = this.permitHeader.hasOwnProperty('permitTypeInfo');
    if (hasPermitTypeInfo) {
      delete this.permitHeader.permitTypeInfo;
    }
    this.permitHeader.EXTENSION_DATE = moment(this.permitHeader.EXTENSION_DATE).valueOf();
    this.permitHeader.IS_EXTENDED = (this.permitHeader.IS_EXTENDED == true || this.permitHeader.IS_EXTENDED == 'true') ? 'true' : 'false';

    this.permitHeader.PERMIT_TYPE = this.createForm.controls['permitType'].value;
    this.permitHeader.FACILITY_ID = this.createForm.controls['facility'].value;
    this.permitHeader.DIVISION_ID = this.createForm.controls['division'].value;
    this.permitHeader.DESCRIPTION = this.createForm.controls['description'].value;
    this.permitHeader.TAG = this.createForm.controls['structureTag'].value;
    this.permitHeader.JOB_NO = this.createForm.controls['jobNumber'].value;

    let permitHeaderResponse: any = await this.dataService.modifyPermit(
      this.permitHeader
    );
    let infoMsg = this.dataService.handleInfoMessage(permitHeaderResponse);
    if (permitHeaderResponse.type == ResultType.success) {
      this.progressbar = false;
      if (infoMsg && infoMsg?.length > 0) {
        this.errorMessage = infoMsg;
      } else { this.modalController.dismiss(this.permitHeader, '', 'edit-permit-basic-details') }
    } else {
      this.progressbar = false;
      if (
        permitHeaderResponse.message &&
        permitHeaderResponse.message.length > 0
      ) {
        this.errorMessage =
          permitHeaderResponse.message;
      } else if (
        permitHeaderResponse.error &&
        permitHeaderResponse.error.length > 0
      ) {
        this.errorMessage =
          permitHeaderResponse.error;
      } else {
        this.errorMessage =
          'Error occured while updating permit, Please try again'
      }
    }
  }

  async getDivisions() {
    // await this.displayPleaseWaitLoader('Please wait, Loading Divisions');
    let res = await this.dataService.getData(
      'DIVISION_HEADER',
      `FACILITY_ID='${this.createForm.controls['facility'].value}'`
    );
    // console.log("Divisions from Db", res);
    if (res.length > 0) {
      this.divisionsList = res;
    } else {
      await this.getDivisionsFromServer(
        this.createForm.controls['facility'].value
      );
    }
  }

  async getDivisionsFromServer(facility) {
    this.divisionsList = [];
    let res: any;
    let customData = {
      DIVISION: [
        {
          DIVISION_HEADER: {
            FACILITY_ID: facility,
            DIVISION_ID: '',
            NAME: '',
            P_MODE: '',
          },
        },
      ],
    };
    res = await this.dataService.getDivisions(customData);
    res = await this.dataService.getData(
      'DIVISION_HEADER',
      `FACILITY_ID='${facility}'`
    );
    console.log("Divisions from Db", res);
    if (res.length > 0) {
      this.divisionsList = res;
    }
  }

  async getPermitTypes() {
    let headerName = 'PERMIT_TYPE_HEADER';
    let fetchMasterDataQuery = `SELECT * FROM ${headerName} ORDER BY PERMIT_TYPE ASC`;
    await this.unviredSDK
      .dbExecuteStatement(fetchMasterDataQuery)
      .then(async (result: any) => {
        if (result.type == ResultType.success) {
          if (result.data.length > 0) {
            this.permitTypesList = result.data;
          }
        }
      });
  }

  async getFacilities() {
    let headerName = 'FACILITY_HEADER';
    let getFacilitiesQuery = `SELECT * FROM ${headerName} ORDER BY NAME ASC`;
    await this.unviredSDK
      .dbExecuteStatement(getFacilitiesQuery)
      .then(async (result: any) => {
        if (result.type == ResultType.success) {
          if (result.data.length > 0) {
            this.facilitiesList = result.data;
          }
        }
      });
  }

  async divisionChange() {
    await this.getStructureTags();
  }

  async getStructureTags() {
    this.structuresArray = [];
    let res = await this.dataService.getData(
      'STRUCTURE_HEADER',
      `FACILITY_ID='${this.createForm.controls['facility'].value}' AND DIVISION_ID='${this.createForm.controls['division'].value}'`
    );
    if (res.length > 0) {
      this.structuresArray = res;
      await this.getStructures(
        this.createForm.controls['facility'].value,
        this.createForm.controls['division'].value
      );
    } else {
      await this.getStructuresFromServer();
    }
  }

  async getStructures(facilityId, divisionId) {
    let res = await this.dataService
      .executeQuery(`SELECT STRUCTURE_HEADER.FACILITY_ID, STRUCTURE_HEADER.DIVISION_ID, STRUCTURE_HEADER.CATEGORY, STRUCTURE_HEADER.NAME, STRUCTURE_HEADER.TAG,STRUCTURE_HEADER.STATUS,
    STRUCTURE_HEADER.STRUCT_TYPE, STRUCTURE_TYPE_HEADER.DESCRIPTION AS TYPE_DESCRIPTION, STRUCTURE_CAT_HEADER.DESCRIPTION AS CAT_DESCRIPTION FROM STRUCTURE_HEADER LEFT
   JOIN STRUCTURE_TYPE_HEADER ON STRUCTURE_HEADER.STRUCT_TYPE= STRUCTURE_TYPE_HEADER.STRUCT_TYPE
   LEFT JOIN STRUCTURE_CAT_HEADER ON STRUCTURE_HEADER.CATEGORY=STRUCTURE_CAT_HEADER.CATEGORY WHERE STRUCTURE_HEADER.FACILITY_ID = '${facilityId}' AND STRUCTURE_HEADER.DIVISION_ID = '${divisionId}' ORDER BY STRUCTURE_HEADER.TAG ASC`);
    if (res.length > 0) {
      this.structuresArray = res;
    }
    return;
  }

  async getStructuresFromServer() {
    this.structuresArray = [];
    let res: any;
    let customData = {
      STRUCTURE: [
        {
          STRUCTURE_HEADER: {
            FACILITY_ID: this.createForm.controls['facility'].value,
            DIVISION_ID: this.createForm.controls['division'].value,
            TAG: '',
            NAME: '',
            CATEGORY: '',
            STRUCT_TYPE: '',
            STATUS: '',
            P_MODE: '',
          },
        },
      ],
    };
    res = await this.dataService.getStructures(customData);
    res = await this.dataService.getData(
      'STRUCTURE_HEADER',
      `FACILITY_ID='${this.createForm.controls['facility'].value}' AND DIVISION_ID='${this.createForm.controls['division'].value}'`
    );
    if (res.length > 0) {
      this.structuresArray = res;
      res = await this.dataService.getStructureTypes();
      res = await this.dataService.getStructureCategories();
      await this.getStructures(
        this.createForm.controls['facility'].value,
        this.createForm.controls['division'].value
      );
    }
  }
}
