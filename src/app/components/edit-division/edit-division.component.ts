import { Component, Input, OnInit } from '@angular/core';
import { <PERSON>ading<PERSON>ontroller, ModalController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { DIVISION_HEADER } from 'src/app/data-models/data_classes';
import { DataService } from 'src/app/services/data.service';
import { MasterSelectedMode } from 'src/app/shared/app-constants';

@Component({
  selector: 'app-edit-division',
  templateUrl: './edit-division.component.html',
  styleUrls: ['./edit-division.component.scss'],
})
export class EditDivisionComponent implements OnInit {
  @Input() facilityId: any;
  @Input() inputDivision: any;
  @Input() mode: any;

  public division: DIVISION_HEADER;
  public displayError: any;
  public title: string = '';
  public buttonText: string = '';
  public progressbar: boolean = false;

  constructor(
    public modalCtrl: ModalController,
    public dataService: DataService,
    private loadingController: LoadingController,
    private translate: TranslateService
  ) { }

  ngOnInit() {
    this.title = this.mode == MasterSelectedMode.edit ? 'Update Division' : 'Add Division';
    this.buttonText = this.mode == MasterSelectedMode.edit ? 'Update' : 'Add';
    this.division = this.mode == MasterSelectedMode.edit ? { ...this.inputDivision } : new DIVISION_HEADER();
  }

  async addDivision() {
    try {
      this.progressbar = true;
      this.displayError = '';

      this.division.P_MODE = this.mode == MasterSelectedMode.edit ? 'M' : 'A';
      this.division.FACILITY_ID = this.facilityId;

      let customData = {
        DIVISION: [
          {
            DIVISION_HEADER: this.division,
          },
        ],
      };

      const actionType = this.mode == MasterSelectedMode.edit ? 'updating' : 'adding';
      await this.displayPleaseWaitLoader(
        this.translate.instant(`Please wait, Division data is being ${actionType}`)
      );

      let result = await this.dataService.addDivision(customData);

      this.loadingController.dismiss();
      this.progressbar = false;

      if (result) {
        this.displayError = result;
      } else {
        // Success - close the modal
        this.modalCtrl.dismiss({
          'success': true,
          'division': this.division
        });
      }
    } catch (error) {
      this.loadingController.dismiss();
      this.progressbar = false;
      this.displayError = 'An error occurred. Please try again.';
      console.error('Error in addDivision:', error);
    }
  }

  // Display Loading dialog
  async displayPleaseWaitLoader(messageReceived: string) {
    const loading = await this.loadingController.create({
      message: messageReceived,
      backdropDismiss: false,
    });
    await loading.present();
  }

  closeModal() {
    this.modalCtrl.dismiss();
  }
}
