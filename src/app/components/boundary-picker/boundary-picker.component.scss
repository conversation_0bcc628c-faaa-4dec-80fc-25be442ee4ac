ion-content {
  --padding-bottom: 16px;
  --padding-top: 0;
  --padding-start: 0;
  --padding-end: 0;
  --background: white;
}

.map-container {
  width: 100%;
  height: 580px; // Increased height by 40% from 350px
  margin: 0;
}

.map-instructions {
  padding: 8px 12px;
  background-color: rgba(255, 255, 255, 0.9);
  border-left: 4px solid var(--ion-color-primary);
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1000;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-width: 90%;

  p {
    margin: 0;
    font-size: 12px;
    color: #666;
    line-height: 1.2;

    ion-icon {
      vertical-align: middle;
      margin-right: 5px;
      color: var(--ion-color-primary);
      font-size: 14px;
    }
  }
}

.boundary-info {
  margin: 10px 10px 0;

  .boundary-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--ion-color-medium);
    margin-bottom: 2px;
  }

  .boundary-data {
    font-family: monospace;
    font-size: 10px;
    background-color: #f5f5f5;
    padding: 4px;
    border-radius: 4px;
    max-height: 40px;
    overflow-y: auto;
    white-space: nowrap;
    overflow-x: auto;
    color: var(--ion-color-dark);
  }
}

.button-bar {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  margin-top: 10px;

  .spacer {
    flex: 1;
  }

  ion-button {
    margin: 0 4px;
  }
}

/* Facility name label styling */
::ng-deep .facility-name-label {
  transition: all 0.2s ease-in-out;
  transform-origin: center;
  user-select: none;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-family: var(--ion-font-family, inherit);
}

/* Make facility name labels more visible on hover */
::ng-deep .facility-name-label:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}
