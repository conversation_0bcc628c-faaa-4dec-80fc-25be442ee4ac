<ion-header mode="ios">
  <ion-toolbar color="primary" mode="ios">
    <ion-title>Draw Facility Boundary</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="cancel()">
        <ion-icon slot="icon-only" name="close"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div class="map-instructions">
    <p>
      <ion-icon name="information-circle-outline"></ion-icon>
      Use the drawing tools to create a boundary for your facility. Click to add points and complete the polygon by clicking on the first point.
    </p>
  </div>

  <div #mapContainer id="map" class="map-container"></div>

  <div class="boundary-info" *ngIf="boundaryWKT">
    <div class="boundary-label">Boundary Data (6 decimal precision)</div>
    <div class="boundary-data">{{boundaryWKT}}</div>
  </div>

  <!-- Button bar inside content instead of footer -->
  <div class="button-bar">
    <ion-button color="medium" (click)="clearDrawing()">
      <ion-icon name="trash-outline" slot="start"></ion-icon>
      Clear
    </ion-button>
    <div class="spacer"></div>
    <ion-button color="danger" (click)="cancel()">
      Cancel
    </ion-button>
    <ion-button color="success" [disabled]="!boundaryWKT" (click)="confirmBoundary()">
      Save
    </ion-button>
  </div>
</ion-content>
