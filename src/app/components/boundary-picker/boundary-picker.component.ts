import { Component, OnInit, Input, Output, EventEmitter, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { ModalController } from '@ionic/angular';

@Component({
  selector: 'app-boundary-picker',
  templateUrl: './boundary-picker.component.html',
  styleUrls: ['./boundary-picker.component.scss'],
})
export class BoundaryPickerComponent implements OnInit, AfterViewInit {
  @ViewChild('mapContainer', { static: false }) mapContainer: ElementRef;
  @Input() initialBoundary: string = '';
  @Input() facilityName: string = '';
  @Output() boundarySelected = new EventEmitter<string>();

  map: google.maps.Map;
  drawingManager: google.maps.drawing.DrawingManager;
  selectedPolygon: google.maps.Polygon = null;
  existingPolygon: google.maps.Polygon = null;
  boundaryWKT: string = '';
  facilityNameOverlay: google.maps.OverlayView = null;

  constructor(private modalController: ModalController) { }

  ngOnInit() {
    // Initialize with input values if provided
    if (this.initialBoundary) {
      this.boundaryWKT = this.initialBoundary;
    }
  }

  ngAfterViewInit() {
    this.initMap();
  }

  initMap() {
    // Default map options with a reasonable default center and zoom
    const mapOptions = {
      center: { lat: 0, lng: 0 }, // Will be overridden by boundary or geolocation
      zoom: 15,
      mapTypeId: google.maps.MapTypeId.ROADMAP,
      mapTypeControl: true,
      streetViewControl: true,
      fullscreenControl: true,
      zoomControl: true
    };

    // Create map
    this.map = new google.maps.Map(this.mapContainer.nativeElement, mapOptions);

    // Initialize drawing manager
    this.initDrawingManager();

    // If there's an initial boundary, display it and center the map on it
    if (this.initialBoundary) {
      // Priority 1: Center on existing boundary
      this.displayExistingBoundary();
    } else {
      // Priority 2: Try to get user's current location if there's no initial boundary
      this.getCurrentLocation();
    }
  }

  initDrawingManager() {
    // Create drawing manager
    this.drawingManager = new google.maps.drawing.DrawingManager({
      drawingMode: google.maps.drawing.OverlayType.POLYGON,
      drawingControl: true,
      drawingControlOptions: {
        position: google.maps.ControlPosition.TOP_CENTER,
        drawingModes: [
          google.maps.drawing.OverlayType.POLYGON
        ]
      },
      polygonOptions: {
        fillColor: '#3880ff',
        fillOpacity: 0.3,
        strokeWeight: 2,
        strokeColor: '#3880ff',
        clickable: true,
        editable: true,
        zIndex: 1
      }
    });

    // Set drawing manager on map
    this.drawingManager.setMap(this.map);

    // Add event listener for when polygon is complete
    google.maps.event.addListener(this.drawingManager, 'polygoncomplete', (polygon: google.maps.Polygon) => {
      // Remove any existing selected polygon
      if (this.selectedPolygon) {
        this.selectedPolygon.setMap(null);
      }

      this.selectedPolygon = polygon;

      // Convert polygon to WKT format
      this.boundaryWKT = this.polygonToWKT(polygon);

      // Switch drawing mode off after polygon is complete
      this.drawingManager.setDrawingMode(null);

      // Get polygon coordinates and center
      const path = polygon.getPath();
      const coordinates: google.maps.LatLngLiteral[] = [];

      for (let i = 0; i < path.getLength(); i++) {
        const point = path.getAt(i);
        coordinates.push({ lat: point.lat(), lng: point.lng() });
      }

      // Calculate center of polygon
      const center = this.calculatePolygonCenter(coordinates);

      // Add facility name label to the map
      this.addFacilityNameLabel(center, coordinates);

      // Add listener for polygon changes
      google.maps.event.addListener(polygon.getPath(), 'set_at', () => {
        this.boundaryWKT = this.polygonToWKT(polygon);
      });

      google.maps.event.addListener(polygon.getPath(), 'insert_at', () => {
        this.boundaryWKT = this.polygonToWKT(polygon);
      });
    });
  }

  displayExistingBoundary() {
    // Clear any existing polygon
    if (this.existingPolygon) {
      this.existingPolygon.setMap(null);
    }

    // Convert WKT to polygon
    const coordinates = this.wktToCoordinates(this.initialBoundary);
    if (coordinates.length > 0) {
      this.existingPolygon = new google.maps.Polygon({
        paths: coordinates,
        strokeColor: '#3880ff',
        strokeOpacity: 0.8,
        strokeWeight: 2,
        fillColor: '#3880ff',
        fillOpacity: 0.35,
        editable: true
      });

      this.existingPolygon.setMap(this.map);
      this.selectedPolygon = this.existingPolygon;

      // Fit map to polygon bounds
      const bounds = new google.maps.LatLngBounds();
      coordinates.forEach(coord => bounds.extend(coord));

      // Center and zoom the map to show the boundary
      this.map.fitBounds(bounds);

      // Calculate the center of the polygon for better centering
      const center = this.calculatePolygonCenter(coordinates);

      // Add facility name label to the map
      this.addFacilityNameLabel(center, coordinates);

      // Add a small padding to the bounds to ensure the polygon is fully visible
      // and not right at the edge of the map
      google.maps.event.addListenerOnce(this.map, 'bounds_changed', () => {
        // If the polygon is very small or has few points, adjust zoom level
        if (coordinates.length <= 4 || bounds.isEmpty()) {
          this.map.setCenter(center);
          this.map.setZoom(17); // Set an appropriate zoom level for small areas
        } else {
          this.map.setZoom(Math.max(this.map.getZoom() - 1, 1)); // Zoom out slightly for better visibility
        }
      });

      // Add listener for polygon changes
      google.maps.event.addListener(this.existingPolygon.getPath(), 'set_at', () => {
        this.boundaryWKT = this.polygonToWKT(this.existingPolygon);
      });

      google.maps.event.addListener(this.existingPolygon.getPath(), 'insert_at', () => {
        this.boundaryWKT = this.polygonToWKT(this.existingPolygon);
      });
    }
  }

  getCurrentLocation() {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const pos = {
            lat: position.coords.latitude,
            lng: position.coords.longitude
          };
          this.map.setCenter(pos);
          this.map.setZoom(15); // Set an appropriate zoom level for the user's location
        },
        (error) => {
          console.log('Error: The Geolocation service failed.', error);
          // Use a reasonable default location if geolocation fails
          this.setDefaultMapCenter();
        },
        {
          enableHighAccuracy: true, // Request high accuracy for better results
          timeout: 5000,           // Wait only 5 seconds for a response
          maximumAge: 0            // Don't use cached position
        }
      );
    } else {
      console.log('Error: Your browser doesn\'t support geolocation.');
      this.setDefaultMapCenter();
    }
  }

  // Set a default map center if geolocation fails or isn't available
  setDefaultMapCenter() {
    // Default to a neutral location or a location relevant to your application
    const defaultCenter = { lat: 0, lng: 0 };
    this.map.setCenter(defaultCenter);
    this.map.setZoom(2); // Zoom out to show a large area
  }

  // Convert polygon to WKT format with submeter accuracy (6 decimal precision)
  polygonToWKT(polygon: google.maps.Polygon): string {
    const path = polygon.getPath();
    let coordinates = [];

    for (let i = 0; i < path.getLength(); i++) {
      const point = path.getAt(i);
      coordinates.push(`${point.lng().toFixed(6)} ${point.lat().toFixed(6)}`);
    }

    // Close the polygon by adding the first point again
    if (path.getLength() > 0) {
      const firstPoint = path.getAt(0);
      coordinates.push(`${firstPoint.lng().toFixed(6)} ${firstPoint.lat().toFixed(6)}`);
    }

    return `POLYGON((${coordinates.join(', ')}))`;
  }

  // Convert WKT to coordinates with submeter accuracy (6 decimal precision)
  wktToCoordinates(wkt: string): google.maps.LatLngLiteral[] {
    const coordinates: google.maps.LatLngLiteral[] = [];

    if (!wkt || !wkt.startsWith('POLYGON')) {
      return coordinates;
    }

    try {
      // Extract coordinates from WKT format
      const coordsString = wkt.substring(wkt.indexOf('((') + 2, wkt.indexOf('))'));
      const coordPairs = coordsString.split(',');

      coordPairs.forEach(pair => {
        const trimmedPair = pair.trim();
        const [lngStr, latStr] = trimmedPair.split(' ');

        // Parse with high precision
        const lng = parseFloat(parseFloat(lngStr).toFixed(6));
        const lat = parseFloat(parseFloat(latStr).toFixed(6));

        if (!isNaN(lat) && !isNaN(lng)) {
          coordinates.push({ lat, lng });
        }
      });
    } catch (error) {
      console.error('Error parsing WKT:', error);
    }

    return coordinates;
  }

  // Calculate the center point of a polygon
  calculatePolygonCenter(coordinates: google.maps.LatLngLiteral[]): google.maps.LatLngLiteral {
    if (coordinates.length === 0) {
      return { lat: 0, lng: 0 };
    }

    // Calculate the centroid of the polygon
    let lat = 0;
    let lng = 0;

    coordinates.forEach(coord => {
      lat += coord.lat;
      lng += coord.lng;
    });

    return {
      lat: lat / coordinates.length,
      lng: lng / coordinates.length
    };
  }

  clearDrawing() {
    if (this.selectedPolygon) {
      this.selectedPolygon.setMap(null);
      this.selectedPolygon = null;
    }

    if (this.existingPolygon) {
      this.existingPolygon.setMap(null);
      this.existingPolygon = null;
    }

    this.boundaryWKT = '';
    this.drawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
  }

  confirmBoundary() {
    this.boundarySelected.emit(this.boundaryWKT);
    this.modalController.dismiss(this.boundaryWKT);
  }

  cancel() {
    this.modalController.dismiss();
  }

  /**
   * Adds a facility name label to the map at the center of the polygon
   * @param center The center point of the polygon
   * @param coordinates The coordinates of the polygon
   */
  addFacilityNameLabel(center: google.maps.LatLngLiteral, coordinates: google.maps.LatLngLiteral[]) {
    // Remove any existing overlay
    if (this.facilityNameOverlay) {
      this.facilityNameOverlay.setMap(null);
      this.facilityNameOverlay = null;
    }

    // Create a custom overlay for the facility name
    class FacilityNameOverlay extends google.maps.OverlayView {
      private div: HTMLElement;
      private position: google.maps.LatLng;
      private text: string;
      private color: string;

      constructor(position: google.maps.LatLng, text: string, color: string, map: google.maps.Map) {
        super();
        this.position = position;
        this.text = text;
        this.color = color;
        this.setMap(map);
      }

      override onAdd() {
        // Create the label div
        const div = document.createElement('div');
        div.className = 'facility-name-label';
        div.style.position = 'absolute';
        div.style.backgroundColor = this.color;
        div.style.color = 'white';
        div.style.padding = '6px 10px';
        div.style.borderRadius = '4px';
        div.style.fontWeight = 'bold';
        div.style.fontSize = '14px';
        div.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.3)';
        div.style.zIndex = '1000';
        div.style.pointerEvents = 'none'; // Allow clicks to pass through to the map
        div.style.opacity = '0.9';
        div.style.textAlign = 'center';
        div.style.minWidth = '100px';
        div.style.whiteSpace = 'nowrap';
        div.innerHTML = this.text;

        // Add the label to the overlay's layer
        const panes = this.getPanes();
        panes.overlayLayer.appendChild(div);

        this.div = div;
      }

      override draw() {
        if (!this.div) return;

        // Position the label at the center of the facility
        const overlayProjection = this.getProjection();
        const position = overlayProjection.fromLatLngToDivPixel(this.position);

        // Center the label
        this.div.style.left = (position.x - (this.div.offsetWidth / 2)) + 'px';
        this.div.style.top = (position.y - (this.div.offsetHeight / 2)) + 'px';
      }

      override onRemove() {
        if (this.div) {
          this.div.parentNode.removeChild(this.div);
          delete this.div;
        }
      }
    }

    // Use the provided facility name or a default
    const displayName = this.facilityName || 'Facility Boundary';

    // Create the facility name overlay
    this.facilityNameOverlay = new FacilityNameOverlay(
      new google.maps.LatLng(center.lat, center.lng),
      displayName,
      '#3880ff', // Use the same color as the polygon
      this.map
    );
  }
}
