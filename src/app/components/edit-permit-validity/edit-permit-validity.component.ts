import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { ModalController } from '@ionic/angular';
import { DataService } from 'src/app/services/data.service';
import * as moment from 'moment';

@Component({
  selector: 'app-edit-permit-validity',
  templateUrl: './edit-permit-validity.component.html',
  styleUrls: ['./edit-permit-validity.component.scss'],
})
export class EditPermitValidityComponent implements OnInit {
  public errorMessage: string = '';
  public progressbar: boolean = false;
  public permit: any;
  public createForm: FormGroup;
  public minDate = null;

  maxEndDate: Date | null = null;
  minEndDate: Date | null = null;

  constructor(public unviredSDK: UnviredCordovaSDK, public modalController: ModalController, public dataService: DataService) { }

  ngOnInit() {
    this.createForm = new FormGroup({
      endDate: new FormControl(new Date(this.permit.EXPIRY_DATE), [Validators.required, this.endDateValidator.bind(this)]),
      startDate: new FormControl(new Date(this.permit.PERMIT_DATE), Validators.required),
    });

    // Initialize date range
    this.updateEndDateRange(this.createForm.controls['startDate'].value);

    // Listen for start date changes
    this.createForm.get('startDate')?.valueChanges.subscribe(value => {
      if (value) {
        this.updateEndDateRange(value);
        this.createForm.controls['endDate'].setValue(this.getDefaultEndDate(value));
        // Revalidate end date when start date changes
        this.createForm.controls['endDate'].updateValueAndValidity();
      }
    });
  }

  cancel() {
    this.modalController.dismiss(false, '', 'edit-permit-validity')
  }

  async save() {
    this.progressbar = true;
    let permitUpdateQuery = `UPDATE PERMIT_HEADER SET P_MODE = 'M', OBJECT_STATUS = 2 WHERE PERMIT_NO = '${this.permit.PERMIT_NO}'`;
    let permitUpdateQueryResult = await this.unviredSDK.dbExecuteStatement(
      permitUpdateQuery
    );
    if (permitUpdateQueryResult.type == ResultType.success) {
      this.permit.P_MODE = 'M';
      this.permit.OBJECT_STATUS = 2;
    }
    let isAvailableButton = this.permit.hasOwnProperty('isShowDetailsButton');
    if (isAvailableButton) {
      delete this.permit.isShowDetailsButton;
    }
    let isShowReviseButton = this.permit.hasOwnProperty('isShowReviseButton');
    if (isShowReviseButton) {
      delete this.permit.isShowReviseButton;
    }
    // Remove permitTypeInfo as it's only used for display purposes and should not be sent to server
    let hasPermitTypeInfo = this.permit.hasOwnProperty('permitTypeInfo');
    if (hasPermitTypeInfo) {
      delete this.permit.permitTypeInfo;
    }
    this.permit.EXTENSION_DATE = moment(this.permit.EXTENSION_DATE).valueOf();
    this.permit.IS_EXTENDED = (this.permit.IS_EXTENDED == true || this.permit.IS_EXTENDED == 'true') ? 'true' : 'false';

    this.permit.PERMIT_DATE = moment(this.createForm.controls['startDate'].value).valueOf();
    this.permit.EXPIRY_DATE = moment(this.createForm.controls['endDate'].value).valueOf();

    let permitHeaderResponse: any = await this.dataService.modifyPermit(
      this.permit
    );
    let infoMsg = this.dataService.handleInfoMessage(permitHeaderResponse);
    if (permitHeaderResponse.type == ResultType.success) {
      this.progressbar = false;
      if (infoMsg && infoMsg?.length > 0) {
        this.errorMessage = infoMsg;
      } else { this.modalController.dismiss(this.permit, '', 'edit-permit-validity') }
    } else {
      this.progressbar = false;
      if (
        permitHeaderResponse.message &&
        permitHeaderResponse.message.length > 0
      ) {
        this.errorMessage =
          permitHeaderResponse.message;
      } else if (
        permitHeaderResponse.error &&
        permitHeaderResponse.error.length > 0
      ) {
        this.errorMessage =
          permitHeaderResponse.error;
      } else {
        this.errorMessage =
          'Error occured while updating permit, Please try again'
      }
    }
  }



  onStartDateChange(_event: any): void {
    const startDate = this.createForm.controls['startDate'].value;
    if (startDate) {
      this.updateEndDateRange(startDate);
      this.createForm.controls['endDate'].setValue(this.getDefaultEndDate(startDate)); // Set default end date (8 hours later)
    } else {
      this.minEndDate = null;
      this.maxEndDate = null;
    }
  }

  updateEndDateRange(startDate: Date): void {
    if (!startDate || isNaN(startDate.getTime())) {
      console.error("Invalid startDate provided:", startDate);
      this.minEndDate = null;
      this.maxEndDate = null;
      return;
    }

    // Set minimum to start date
    this.minEndDate = new Date(startDate);

    // Set maximum to exactly 8 hours from start date
    this.maxEndDate = new Date(startDate.getTime() + 8 * 60 * 60 * 1000);

    console.log("Date range updated:", {
      startDate: startDate,
      minEndDate: this.minEndDate,
      maxEndDate: this.maxEndDate
    });
  }

  formatDateToLocalISOString(date: Date): string {
    const offset = date.getTimezoneOffset() * 60000;
    return new Date(date.getTime() - offset).toISOString().slice(0, -1);
  }

  getDefaultEndDate(startDate: Date): Date {
    const endDate = new Date(startDate);
    endDate.setHours(startDate.getHours() + 8);
    return endDate;
  }

  onEndDateChange(_event: any): void {
    const endDate = this.createForm.controls['endDate'].value;
    const startDate = this.createForm.controls['startDate'].value;

    console.log("End date changed:", endDate);

    if (endDate && startDate) {
      const endDateTime = new Date(endDate);
      const startDateTime = new Date(startDate);
      const maxAllowedTime = new Date(startDateTime.getTime() + 8 * 60 * 60 * 1000);

      if (endDateTime > maxAllowedTime) {
        console.warn("End date exceeds 8-hour limit, resetting to maximum allowed time");
        this.createForm.controls['endDate'].setValue(maxAllowedTime);
      }
    }

    // Trigger validation
    this.createForm.controls['endDate'].markAsTouched();
    this.createForm.controls['endDate'].updateValueAndValidity();
  }

  // Custom validator to ensure end date is not more than 8 hours from start date
  endDateValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) {
      return null; // Don't validate if no value
    }

    const startDate = this.createForm?.get('startDate')?.value;
    if (!startDate) {
      return null; // Don't validate if no start date
    }

    const endDate = new Date(control.value);
    const startDateTime = new Date(startDate);
    const maxAllowedTime = new Date(startDateTime.getTime() + 8 * 60 * 60 * 1000); // 8 hours later

    if (endDate > maxAllowedTime) {
      return {
        maxDuration: {
          message: 'End date cannot be more than 8 hours from start date',
          maxAllowed: maxAllowedTime,
          actual: endDate
        }
      };
    }

    if (endDate < startDateTime) {
      return {
        minDate: {
          message: 'End date cannot be before start date',
          minAllowed: startDateTime,
          actual: endDate
        }
      };
    }

    return null;
  }
}
