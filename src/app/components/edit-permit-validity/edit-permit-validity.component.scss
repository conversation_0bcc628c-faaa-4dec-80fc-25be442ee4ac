ion-toolbar {
  --color: white;
}

/* Angular DateTime Picker Custom Styling */
:host ::ng-deep {
  .owl-dt-popup {
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }

  .owl-dt-container {
    border-radius: 8px;
  }

  .owl-dt-calendar-table .owl-dt-calendar-cell {
    border-radius: 4px;
  }

  .owl-dt-calendar-table .owl-dt-calendar-cell.owl-dt-calendar-cell-selected {
    background-color: var(--ion-color-primary);
    color: white;
  }

  .owl-dt-calendar-table .owl-dt-calendar-cell.owl-dt-calendar-cell-today {
    border: 2px solid var(--ion-color-primary);
  }

  .owl-dt-timer-box {
    border-radius: 4px;
    border: 1px solid var(--ion-color-step-300, #b3b3b3);
  }
}