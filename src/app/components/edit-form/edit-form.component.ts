import { ChangeDetectorRef, Component, Input, NgZone, OnInit } from '@angular/core';
import { BusyIndicatorService } from '../../services/busy-indicator.service';
import { HttpClient } from '@angular/common/http';
import { FormBuilder, FormGroup } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { Router, NavigationExtras } from '@angular/router';
import { Network } from '@awesome-cordova-plugins/network/ngx';
import { UnviredCordovaSDK, DbResult, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { ModalController, PopoverController, AlertController, Platform, LoadingController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { AppSpecificUtilityService } from '../../services/app-specific-utility.service';
import { AttachmentsService } from '../../services/attachments.service';
import { DataService } from '../../services/data.service';
import { HelperFunctionService } from '../../services/HelperFunction.service';
import { ShareDataService } from '../../services/ShareData.service';
import { Column, FORM_HEADER, FormComponent, PERMIT_FORM, PERMIT_HEADER, USER_CONTEXT_HEADER } from '../../data-models/data_classes';
import { Subscription } from 'rxjs';
import { AppConstants } from '../../shared/app-constants';
import { Camera, CameraOptions } from '@awesome-cordova-plugins/camera/ngx';
import * as moment from 'moment';
import * as hash from 'object-hash';
// Duplicate @Component decorator removed

declare let window: any;
declare let cordova: any;
declare const loadForm: any;
declare const submit: any;
declare const wizardSubmit: any;
declare const returnTempData: any;
declare const returnLastTempData: any;
declare const setLocation: any;

@Component({
  selector: 'app-edit-form',
  templateUrl: './edit-form.component.html',
  styleUrls: ['./edit-form.component.scss'],
})
export class EditFormComponent implements OnInit {
  public userRole: string;
  public isReport: boolean;
  public isShowProgressBar: boolean = false;
  public isPermitreadOnly: boolean = false;
  public priorities = [];
  public capturedSnapURL: string;
  public devicePlatform = 'browser';
  public tempPermitObj: any[] = [];
  public rcaList: any[] = [];
  public defaultSelectedRca: any[] = [];
  public requestUser: string = '';
  public reviewUser: string = '';
  public reviewForm: FormGroup;
  public otherReviews: any[] = [];
  public segmentValue: string = 'details';
  public stakeHoldersList: any[] = [];
  public initialStakeholderlist: any[] = [];
  public usersList: any[] = [];
  public permitDocs: any[] = [];
  public filteredInternalList: any[] = [];
  public filteredExternalList: any[] = [];
  public calculatedPercentage = 0;
  public isHybridNative: boolean;
  public setBarcode: any = false;
  public componentID: string = '';

  public locationComponentID: string = '';
  public attributesJson: any;
  public tempData: any;
  public isdisableFormCompletion: boolean = false;
  public formReadOnlyFlag: boolean = false;
  public buttonType: string;
  public formReadWriteFlag: boolean = false;
  public FirstTimeFlag: boolean = false;
  public currentUserDetails: any;
  public isAndroid: boolean = false;

  public formViewOnlyFlag: boolean = false;
  public translateJson: any;
  public componentObj: any;
  public cameraOpen: boolean = false;
  public form: FORM_HEADER;
  public formDescription: string = '';
  public masterData: any;
  public formSubmissionData: any;
  public markComplete: boolean = false;
  public completeFlag: boolean = false;
  public permitFormSubmissionData = {} as PERMIT_FORM;
  public permitHeaderData = {} as PERMIT_HEADER;
  public constants: AppConstants;
  public formLoaded = false;
  public facilityName: string = '';
  public permitForm = {} as PERMIT_FORM;
  public isFormFound: boolean = true;
  public isMobile: boolean = false;
  public isHideNewStakeHolderButton: boolean = false;
  public isloaded: boolean = false;
  disableTime: Date | null = null;
  isToggleEnabled: boolean = false; // Tracks if toggle should be enabled
  toggleState: boolean = false; // Tracks the state of the toggle
  extensionForm: FormGroup;

  approvalCompleted: boolean;
  rejectionCompleted: boolean;
  isModalOpen = false; // Prevent multiple modal openings
  addReason: boolean;

  public filteredUsers = [];
  cameraOptions: CameraOptions = {
    quality: 100,
    destinationType: this.camera.DestinationType.DATA_URL,
    encodingType: this.camera.EncodingType.JPEG,
    mediaType: this.camera.MediaType.PICTURE,
  };

  public minDate = new Date().toISOString();
  maxEndDate: string | null = null;
  minEndDate: string | null = null;

  public internalApprType: any = [];
  public externalApprType: any = [];

  stakeholderData: any;
  rejectedComment: string;
  stakeActionStatusArray: any[] = [];
  dataisLoading: boolean;
  resultErrorMessage: string;
  allUsers: any[] = [];

  showFillFormButton: boolean;
  previousSegmentValue: string = 'details';

  showSaveButton: boolean = false;
  // selectedStakeholder: any;

  @Input() userObject: any;
    @Input() formData: any;
    @Input() permit: any;
   @Input() subData: any;
   @Input() selectedStakeholder: any;
   @Input() actionStatus: any;
   public userInfo: any;
public company: any;

async ngOnInit() {

}


  async ionViewDidEnter() {
    console.log(this.actionStatus);
    let selectedPermit = localStorage.getItem('selectedPermit');

     
    if (selectedPermit) {
      this.permit = JSON.parse(selectedPermit);
    }
    console.log("the subdata and formdata-------------" ,this.actionStatus, this.subData , this.formData , this.selectedStakeholder)
    await this.getFormTempaltes();
    await this.createFormAndSetFormData()
    // let appr_exists =await this.searchForApprovalType(this.formData ,this.stakeholder)
    // console.log("the appr exists is " , appr_exists)
    if (this.formData ) {
      let val = await returnTempData();
      if (val) {
        let tempdata = val.tempData ? val.tempData : val.submission;
        console.log("tempdata" , tempdata)
        if (tempdata && tempdata.data) {
          this.formSubmissionData = tempdata.data;
        } else {
          this.formSubmissionData = tempdata;
        }
      }

        if (this.formData && this.formData.components) {
          await this.showForm();
        }
     

  
    } else {

      console.log("permits tab" , this.formData.components)
      // await this.actionButtons(status, index, stakeholder);
    }
    // await this.showForm();
   
  }

  // ionViewWillEnter() {
  //   console.log("the subdata and formdata-------------" , this.subData , this.formData)
  // }

  constructor(
    private modalController: ModalController,
    private unviredSDK: UnviredCordovaSDK,
    public dataService: DataService,
    public sanitizer: DomSanitizer,
    private shareData: ShareDataService,
    public formBuilder: FormBuilder,
    private loader: BusyIndicatorService,
    private appSpecificUtility: AppSpecificUtilityService,
    private utilityFunction: HelperFunctionService,
    public popoverCtrl: PopoverController,
    private platform: Platform,
    private router: Router,
    public loadingController: LoadingController,
    public attachmentService: AttachmentsService,
    private camera: Camera,
    private ngZone: NgZone,
    private alertController: AlertController,
    private translate: TranslateService,
  ) {
    
  }

  async cancel() {
    try {
      // Present the modal first if not already presented
     
        console.log('Form changes discarded');
        this.modalController.dismiss({ saved: false }); // Close the modal without saving
    } catch (error) {
      console.error('Error handling modal dismissal:', error);
    }
  }


  async showForm() {
    this.previousSegmentValue = this.segmentValue;
    this.showSaveButton = true;
    this.isloaded = true;
    if (!this.formLoaded) {
      this.displayPleaseWaitLoader()
      // this.formLoaded = true;
    } else {
      let val = await returnTempData();
      console.log('val in edit form', val);
      if (val) {
        let tempdata = val.tempData ? val.tempData : val.submission;
        console.log("tempdata in edit form", tempdata);
        if (tempdata && tempdata.data) {
          this.formSubmissionData = tempdata.data;
        } else {
          this.formSubmissionData = tempdata;
        }
      }
    }

    console.log("window.form", window.form);
    // 
    // await this.createFormAndSetFormData();
    // try {
    //   if (this.loader.isLoading) {
    //     await this.loader.dismissBusyIndicator();
    //   }
    // } catch (error) {
    //   console.error('Error dismissing loader:', error);
    // }
    // if (this.loader.isLoading) {
    //   await this.loader.dismissBusyIndicator();
    // }
  }

  async createFormAndSetFormData() {
    $('head').append(
      '<link rel="stylesheet" href="assets/js/formio/formio.full.min.css" type="text/css" />'
    );
    $('head').append(
      '<link rel="stylesheet" href="assets/css/responsive-grid.min.css" type="text/css" />'
    );
    await this.prepareFormResourcesAndSetFormData();
    this.subData = this.formSubmissionData;
    this.formReadOnlyFlag = false;
    // let loggedInUserIds = this.userObject.USER_ID;
    this.FirstTimeFlag = this.shareData.getNavigateFromCameraPage();
    
    if (!this.FirstTimeFlag) {
      this.FirstTimeFlag = true;

      this.company = this.appSpecificUtility.getCompany();
      console.log('company', this.company);
      this.appSpecificUtility.getLoggedInUserInfo().subscribe((data) => {
        this.userInfo = data[0];
        if (
          this.userInfo &&
          Object.keys(this.userInfo).length === 0 &&
          this.userInfo.constructor === Object
        ) {
        } else {
          this.currentUserDetails = this.userInfo;
        }
      });
      console.log('userInfo', this.userInfo);
      this.isAndroid = this.platform.is('android');
      let iosPlatform = this.platform.is('ios');
      setTimeout(async () => {
        if (this.formData && this.formData.components) {
          let components = this.formData.components;
          for (let i = 0; i < components.length; i++) {
            if (
              components[i].type === 'form' &&
              components[i].tags &&
              components[i].tags.length > 0
            ) {
              for (let j = 0; j < components[i].tags.length; j++) {
                if (components[i].tags[j] === 'FormViewOnly') {
                  this.formViewOnlyFlag = true;
                } else if (components[i].tags[j] === 'FormReadWrite') {
                  this.formReadWriteFlag = true;
                }
              }
            }
          }

          if (!this.formReadWriteFlag) {
            this.formViewOnlyFlag = true;
          }
        }
        this.unviredSDK.logDebug(
          'PermitDetailsComponent',
          'ionViewDidEnter()',
          'submission data = ' + JSON.stringify(this.subData)
        );
        let userData = await this.dataService.getData('USER_ROLE');

        if (userData) {
          this.loader.dismissBusyIndicator();
        }

        let userApprovalType = await this.dataService.getData(
          'USER_APPROVAL_TYPE'
        );
        let permitUserApprovalType = [];
        if (userApprovalType && userApprovalType.length > 0) {
          for (let i = 0; i < userApprovalType.length; i++) {
            permitUserApprovalType.push({
              APPR_TYPE: userApprovalType[i].APPR_TYPE,
            });
          }
        }
        let stakeholderDataForm;
        if (this.formReadOnlyFlag == false) {
        }

        if (this.formData) {
          this.isFormFound = true;
          let data = JSON.stringify(this.formData);
          data = data.replace(/@PERMIT_NO@/g, this.permit.PERMIT_NO);
          let getPermitDescriptionPipeResult: DbResult =
            await this.unviredSDK.dbExecuteStatement(
              `SELECT DESCRIPTION AS DESC FROM PERMIT_TYPE_HEADER WHERE PERMIT_TYPE='${this.permit.PERMIT_TYPE}'`
            );
          let description = getPermitDescriptionPipeResult?.data[0]?.DESC
            ? getPermitDescriptionPipeResult?.data[0]?.DESC
            : this.permit.PERMIT_TYPE;

          data = data.replace(/@PERMIT_TYPE@/g, description);
          data = data.replace(/@PERMIT_DESCRIPTION@/g, this.permit.DESCRIPTION);
          data = data.replace(/@PERMIT_STATUS@/g, this.permit.STATUS);
          const permitDate = new Date(this.permit.PERMIT_DATE);
          const options: any = {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          };
          const expiryDate = new Date(this.permit.EXPIRY_DATE);
          data = data.replace(
            /@PERMIT_DATE@/g,
            permitDate.toLocaleString('en-US', options)
          );
          data = data.replace(
            /@EXPIRY_DATE@/g,
            expiryDate.toLocaleString('en-US', options)
          );
          data = data.replace(/@FACILITY_ID@/g, this.facilityName);
          let getPermitDivisionNamePipeResult: DbResult =
            await this.unviredSDK.dbExecuteStatement(
              `SELECT NAME AS NAME FROM DIVISION_HEADER WHERE DIVISION_ID='${this.permit.DIVISION_ID}'`
            );
          let division = getPermitDivisionNamePipeResult?.data[0]?.NAME
            ? getPermitDivisionNamePipeResult?.data[0]?.NAME
            : this.permit.DIVISION_ID;
          data = data.replace(/@DIVISION_ID@/g, division);
          data = data.replace(
            /@AGENT_ID_EXT@/g,
            this.permit.AGENT_ID_EXT ? this.permit.AGENT_ID_EXT : ''
          );
          data = data.replace(
            /@AGENT_ID_INT@/g,
            this.permit.AGENT_ID_INT ? this.permit.AGENT_ID_INT : ''
          );
          data = data.replace(
            /@REQUESTED_BY@/g,
            this.permit.REQUESTED_BY ? this.permit.REQUESTED_BY : ''
          );
          let comments = this.permit.COMMENTS ? this.permit.COMMENTS : '';
          comments = comments.trim();
          data = data.replace(/@COMMENTS@/g, comments);
          if (
            this.selectedStakeholder != undefined &&
            this.selectedStakeholder != null &&
            this.formReadOnlyFlag == false
          ) {
            data = data.replace(/@ROLE@/g, this.selectedStakeholder.ROLE);
            data = data.replace(/@APPR_TYPE@/g, this.selectedStakeholder.APPR_TYPE);

          
              console.log('action button clicked');
              data = this.replaceApproverName(
                data,
                `${this.selectedStakeholder.APPR_TYPE}Name`,
                `${this.selectedStakeholder.USER_ID}`
              );
              let radioStatus;
              if (this.actionStatus == 'Approved') {
                radioStatus = 'approve';
              } else if (this.actionStatus == 'Rejected') {
                radioStatus = 'reject';
              }
              data = this.replaceApproverRadio(
                data,
                `${this.selectedStakeholder.APPR_TYPE}Radio`,
                `${radioStatus}`
              );
          
          }
        this.subData.RoleName = this.selectedStakeholder.ROLE;
              this.subData.ApprovalType = this.selectedStakeholder.APPR_TYPE;
          // if (this.formReadOnlyFlag == false) {
          //   if (
          //     this.actionButtonClicked == true &&
          //     this.segmentValue == 'form' &&
          //     this.selectedStakeholder != null
          //   ) {
          //     this.subData.RoleName = this.selectedStakeholder.ROLE;
          //     this.subData.ApprovalType = this.selectedStakeholder.APPR_TYPE;

          //     this.actionButtonClicked = false;
          //   } else {
          //     this.subData.RoleName = 'null';
          //     this.subData.ApprovalType = 'null';
          //     window.form.stakeholderDataForm = 'null';
          //   }
          // }

          this.formData = JSON.parse(data);
          if (this.formData && this.formData.components) {
            this.formData.components.forEach((component) => {
              this.stakeHoldersList.forEach((stakeholder) => {
                if (component.key === stakeholder.CONTEXT) {
                  this.showFillFormButton = true;
                } else {
                  this.showFillFormButton = false;
                }
              });
            });
          }


          this.subData.permitComments = comments;
          console.log("data isssssssssss" ,  this.formData,
            this.subData,
            this.formReadOnlyFlag,
            this.translateJson,
            this.userInfo,
            this.company,
            this.isAndroid,
            iosPlatform,
            !this.isHybridNative,
            'form',
            this.attributesJson,
            '<EMAIL>',
            userData,
            permitUserApprovalType,
            stakeholderDataForm);
          this.ngZone.runOutsideAngular(async () => {
          await loadForm(
              this.formData,
              this.subData,
              this.formReadOnlyFlag,
              this.translateJson,
              this.userInfo,
              this.company,
              this.isAndroid,
              iosPlatform,
              !this.isHybridNative,
              'form',
              this.attributesJson,
              '<EMAIL>',
              userData,
              permitUserApprovalType,
              stakeholderDataForm
            );
          });
        } else {
          this.isFormFound = false;
        }
      }, 1000);
      this.shareData.setFirstTimeFlag(true);
    } else {
      try {
        if (this.loader.isLoading) {
          await this.loader.dismissBusyIndicator();
        }
      } catch (error) {
        console.error('Error dismissing loader:', error);
      }
    }

    function formRenderingCompleteHandler(this: any, event) {
      this.unviredSDK.logDebug(
        'PermitDetailsComponent',
        'formRenderingCompleteHandler()',
        'Form Rendering is complete. Loading CSS..'
      );
      if (!this.formLoaded) {
        console.log('calling prepareTabulatorData');
        this.prepareTabulatorData();
        this.formLoaded = true;
      }
      this.unviredSDK.logDebug(
        'PermitDetailsComponent',
        'formRenderingCompleteHandler()',
        'Form Rendering is complete. Loading CSS Complete.'
      );
    }
    function formRenderingCompleteFromapprovalHandler(this: any, event) {
      console.log('formRenderingCompleteFromapprovalHandler called');
      this.prepareTabulatorData();
    }
    document.addEventListener(
      'FormRenderingComplete',
      formRenderingCompleteHandler.bind(this),
      false
    );

    document.addEventListener(
      'FormRenderingCompleteFromapproval',
      formRenderingCompleteFromapprovalHandler.bind(this),
      false
    );

    let component = this;
    setTimeout(async function () {
      document.addEventListener(
        'openBarcode',
        function (event: any) {
          component.componentObj = event.detail.compObj;
          component.setBarcode = true;
          component.componentID = event.detail.controlId;
        },
        false
      );
    }, 200);

    document.addEventListener(
      'smartDataEvent',
      async function (event: any) {
        const data = event.detail;
        component.subData =
          component.subData === '' ||
          component.subData === undefined ||
          component.subData === null
            ? {}
            : component.subData;
        component.subData = data;
        component.formSubmissionData = JSON.stringify(component.subData);
      },
      false
    );

    let that = this;
    setTimeout(async function () {
      document.addEventListener(
        'openCamera',
        function (event: any) {
          that.cameraOpen = true;
          const dataToSend: NavigationExtras = {
            queryParams: { id: event.detail.controlId },
          };
          that.router.navigate(['camera'], dataToSend);
        },
        false
      );
    }, 200);
    document.addEventListener(
      'displaySDCWorkflowError',
      this.displaySDCWorkflowErrorHandler.bind(this),
      true
    );
  }

  displaySDCWorkflowErrorHandler(event: any) {
    this.loader.showWorkFlowToast(event.detail.errorMsg);
  }

  async prepareFormResourcesAndSetFormData() {
    let data = this.shareData.getFormData();
    if (data) {
    } else {
      await this.getFormTempaltes();
      data = this.shareData.getFormData();
    }
    if (data === undefined || Object.keys(data).length == 0) {
      let FORM_IO_ENTITY = localStorage.getItem('FORM_IO_ENTITY');
      console.log('FORM_IO_ENTITY', FORM_IO_ENTITY);
      data =
        FORM_IO_ENTITY != '' && FORM_IO_ENTITY != undefined
          ? JSON.parse(FORM_IO_ENTITY)
          : '';
    }
    console.log('data in form -render', data);
    this.form = data.form;

    if (this.form && this.form.FORM_TITLE) {
      this.formDescription = this.form.FORM_TITLE;
    }
    let formDesign = this.utilityFunction.decodeUnicode(
      this.form ? this.form.TEMPLATE : ''
    );
    let formIOComponent = formDesign === '' ? '' : JSON.parse(formDesign);

    if (!this.isHybridNative) {
      this.masterData = this.shareData.getMasterData();
    }

    await this.getNestedFormsDetails(formIOComponent);

    this.unviredSDK.logInfo(
      'PermitDetailsComponent',
      'prepareFormResourcesAndSetFormData()',
      'calling getMasterdDataresource()'
    );
    await this.getMasterdDataresource(formIOComponent, 'dataSrc', 'masterdata');
    this.formData = formIOComponent;
    this.shareData.setFormIOComponent(this.formSubmissionData, this.formData);
  }

  async getNestedFormsDetails(obj: any) {
    var objects = [];
    for (var i in obj) {
      if (typeof obj[i] == 'object') {
        objects = objects.concat(this.getNestedFormsDetails(obj[i]));
      } else if (
        i == 'nestedFormComponenttype' &&
        obj['nestedFormComponenttype'] == 'smartNestedForm'
      ) {
        let selNestedFormVersion = obj.selNestedFormVersion;
        let selectedNestedFormId = obj.selectedNestedFormId;

        if (
          selNestedFormVersion != undefined &&
          selNestedFormVersion != '' &&
          selectedNestedFormId != undefined &&
          selectedNestedFormId != ''
        ) {
          let result = await this.dataService.getNestedformDetailsFromDB(
            selNestedFormVersion,
            selectedNestedFormId
          );
          if (result && result.type === ResultType.success) {
            if (result['data']) {
              obj.formDesign = JSON.parse(result['data']);
              await this.getMasterdDataresource(
                obj.formDesign,
                'dataSrc',
                'masterdata'
              );
            }
          }
        }
      }
    }
    return true;
  }

  async getMasterdDataresource(obj: any, key: any, val: any) {
    var objects = [];

    for (var i in obj) {
      if (typeof obj[i] == 'object') {
        objects = objects.concat(
          await this.getMasterdDataresource(obj[i], key, val)
        );
      } else if (i == key && obj[key] == val) {
        var val1 = obj['valueProperty'];
        var ress;
        if (obj.data && typeof obj.data == 'object' && obj.data != undefined) {
          if (obj.data['masterdata']) {
            ress = obj.data['masterdata'];
          }
        }
        var obj1 = [];

        if (ress != undefined && ress != '') {
        } else {
          this.unviredSDK.logInfo(
            'PermitDetailsComponent',
            'getMasterdDataresource()',
            'selected masterdata resource is empty'
          );
          obj1 = [];
        }
        var tmplt = obj['template'];
        if (tmplt != '') {
          var str1 = tmplt.substring(tmplt.lastIndexOf('.') + 1, tmplt.length);
          var str2 = str1.substring(0, str1.indexOf('}'), str1.length);
          var str3 = '<span>{{' + 'item.' + str2.trim() + '}}</span>';
          obj['template'] = str3;
        }
        var val = obj['valueProperty'];
        var valProp = '';
        if (val != '') {
          valProp = val.substring(val.lastIndexOf('.') + 1, val.length);
          obj['valueProperty'] = valProp;
        }
        str2 = str2.trim();
        obj1 = obj1.sort(function (a, b) {
          return a[str2] > b[str2] ? 1 : a[str2] < b[str2] ? -1 : 0;
        });
        obj.masterdata = obj1;
      }
    }
  }

  replaceApproverName(jsonText: string, keyName: string, userId: string): string {
    try {
      const obj = JSON.parse(jsonText) as { components: FormComponent[] };

      let traverse = (component: FormComponent): void => {
        if (component.key === keyName) {
          const contextName = keyName.replace('Name', '');

          if (!this.subData[contextName]) {
            this.subData[contextName] = {};
          }
          if (
            component.key === keyName &&
            component.defaultValue === '@ApproverName@'
          ) {
            component.defaultValue = userId;
            this.subData[contextName][keyName] = userId;
          } else if (
            component.key === keyName &&
            component.defaultValue == userId
          ) {
            component.defaultValue = '';
            this.subData[contextName][keyName] = '';
          } else if (
            component.key === keyName &&
            component.defaultValue === '@ApproverName@'
          ) {
            component.defaultValue = '';
            this.subData[contextName][keyName] = '';
          }
        }

        if (Array.isArray(component.components)) {
          component.components.forEach((child: FormComponent) =>
            traverse(child)
          );
        }

        if (component.columns) {
          component.columns.forEach((column: Column) => {
            if (Array.isArray(column.components)) {
              column.components.forEach((child: FormComponent) =>
                traverse(child)
              );
            }
          });
        }
      };

      if (Array.isArray(obj?.components)) {
        obj.components.forEach((component: FormComponent) => traverse(component));
      }

      return JSON.stringify(obj);
    } catch (error) {
      console.error('Error in replaceApproverInComponent:', error);
      return jsonText;
    }
  }

  replaceApproverRadio(jsonText: string, keyName: string, status: string): string {
    try {
      const obj = JSON.parse(jsonText) as { components: FormComponent[] };

      let traverse = (component: FormComponent): void => {
        if (component.key === keyName) {
          const contextName = keyName.replace('Radio', '');

          if (!this.subData[contextName]) {
            this.subData[contextName] = {};
          }
          if (component.key === keyName) {
            this.subData[contextName][keyName] = status;
          } else if (component.key === keyName) {
            this.subData[contextName][keyName] = '';
          } else if (component.key === keyName) {
            component.defaultValue = '';
          }
        }

        if (Array.isArray(component.components)) {
          component.components.forEach((child: FormComponent) =>
            traverse(child)
          );
        }

        if (component.columns) {
          component.columns.forEach((column: Column) => {
            if (Array.isArray(column.components)) {
              column.components.forEach((child: FormComponent) =>
                traverse(child)
              );
            }
          });
        }
      };

      if (Array.isArray(obj?.components)) {
        obj.components.forEach((component: FormComponent) => traverse(component));
      }

      return JSON.stringify(obj);
    } catch (error) {
      console.error('Error in replaceApproverInComponent:', error);
      return jsonText;
    }
  }

  async getFormTempaltes() {
    let getFormsResp: any = await this.dataService.getFormTemplates();
    if (getFormsResp.type == ResultType.success) {
    } else {
      console.log('error response on get templates');
      return { type: ResultType.error, error: getFormsResp.message };
    }
    if (this.isReport) {
      let formQuery = `SELECT * from FORM_HEADER`;
      let formQuerysResult = await this.unviredSDK.dbExecuteStatement(
        formQuery
      );
      if (formQuerysResult.type == ResultType.success) {
        if (
          formQuerysResult &&
          formQuerysResult.data &&
          formQuerysResult.data.length > 0
        ) {
          for (let i = 0; i < formQuerysResult.data.length; i++) {
            let formHeader = formQuerysResult.data[i];
            if (
              formHeader &&
              this.permitForm.FORM_ID === formHeader.FORM_ID
            ) {
              let data = {
                type: ResultType.success,
                error: '',
                form: formHeader,
              };
              this.shareData.setFormData(data);
            }
          }
        }
      } else {
        return { type: ResultType.error, error: formQuerysResult.message };
      }
    } else {
      let permitFormQuery = `SELECT * from PERMIT_FORM WHERE PERMIT_NO = '${this.permit.PERMIT_NO}' AND (P_MODE !='D' OR P_MODE IS NULL)`;
      let permitFormQuerysResult = await this.unviredSDK.dbExecuteStatement(
        permitFormQuery
      );
      if (permitFormQuerysResult.type == ResultType.success) {
        if (
          permitFormQuerysResult &&
          permitFormQuerysResult.data.length > 0
        ) {
          let formQuery = `SELECT * from FORM_HEADER`;
          let formQuerysResult = await this.unviredSDK.dbExecuteStatement(
            formQuery
          );
          if (formQuerysResult.type == ResultType.success) {
            if (
              formQuerysResult &&
              formQuerysResult.data &&
              formQuerysResult.data.length > 0
            ) {
              for (let i = 0; i < formQuerysResult.data.length; i++) {
                let formHeader = formQuerysResult.data[i];
                if (
                  formHeader &&
                  permitFormQuerysResult.data[0].FORM_ID ===
                    formHeader.FORM_ID
                ) {
                  let data = {
                    type: ResultType.success,
                    error: '',
                    form: formHeader,
                  };
                  this.shareData.setFormData(data);
                  await this.getPermitForm(formHeader.FORM_ID);
                }
              }
            }
          } else {
            return { type: ResultType.error, error: formQuerysResult.message };
          }
        }
      }
    }
  }

  async getPermitForm(formId) {
    let formquery = `select * from PERMIT_FORM WHERE PERMIT_NO = '${this.permit.PERMIT_NO}' AND FORM_ID = '${formId}' AND (P_MODE !='D' OR P_MODE IS NULL)`;
    let formqueryResult = await this.unviredSDK.dbExecuteStatement(
      formquery
    );
    if (formqueryResult && formqueryResult.data.length > 0) {
      for (let i = 0; i < formqueryResult.data.length; i++) {
        this.formSubmissionData = JSON.parse(formqueryResult.data[i].DATA);
      }
    }
  }








  

  async saveForm() {
    try {
      localStorage.setItem('renderBackFormDataChanged', 'true');
      if (!this.loader.isLoading) {
        await this.loader.showBusyIndicator(this.translate.instant('Saving Form...'), 'crescent');
      }

      console.log("the formdata display is " , this.formData)
      if (this.formData.display === 'wizard') {
        // FORM DATA TYPE WIZARD
        try {
          let val = await wizardSubmit();
          if (val.submission !== '') {
            let clientCompField = this.attributesJson
              ? this.attributesJson.filter((e) => {
                return e.key.trim() == 'client-complete-check-field';
              })
              : [];
            let completeFld: boolean = true;
            if (
              clientCompField.length > 0 &&
              val.submission &&
              val.submission.data &&
              clientCompField[0].value !== ''
            ) {
              completeFld = val.submission.data[clientCompField[0].value];
            }
            if (!completeFld && this.markComplete) {
              await this.confirmationAlert();
              // this.confirmationAlert(this.translate.instant('Form cannot be completed until all required fields are entered. Save instead?'));
            } else {
              let arr = [];
              let keys = Object.keys(val.submission.data);
              Object.values(val.submission.data).forEach((ele, i) => {
                if (ele === '') {
                  arr.push(keys[i]);
                }
              });
              let promptOnFormComplete: boolean = false;
              let promptOnFormCompleteData = this.attributesJson
                ? this.attributesJson.filter((e) => {
                  return e.key.trim() == 'prompt-on-form-complete';
                })
                : [];
              if (
                promptOnFormCompleteData &&
                promptOnFormCompleteData.length > 0 &&
                promptOnFormCompleteData[0]
              ) {
                promptOnFormComplete = promptOnFormCompleteData[0].value;
                if (promptOnFormComplete && this.markComplete) {
                  let msgFormComplete = this.attributesJson
                    ? this.attributesJson.filter((e) => {
                      return e.key.trim() == 'message-form-complete';
                    })
                    : [];
                  if (
                    msgFormComplete &&
                    msgFormComplete.length > 0 &&
                    msgFormComplete[0]
                  ) {
                    let res = await this.confirmationAlertForComplete(
                      msgFormComplete[0].value
                    );
                    console.log('res' + res);

                    if (res) {
                      if (!this.loader.isLoading) {
                        await this.loader.showBusyIndicator(
                          this.translate.instant('Completing Form...'),
                          'crescent'
                        );
                      }
                      await this.updateDataAndSendToServer(val.submission, 'M');
                    }
                  }
                } else {
                  if (!this.markComplete) {
                    await this.confirmationAlert();
                  } else {
                    await this.updateDataAndSendToServer(val.submission, 'M');
                  }
                }
              }
            }
          }
        } catch (err: any) {
          if (this.loader.isLoading) {
            this.unviredSDK.logInfo(
              'PermitDetailsComponent',
              'saveForm()',
              'Hiding Busy indicator...'
            );
            if (this.loader.isLoading) {
              await this.loader.dismissBusyIndicator();
            }
          }
          if (err.error !== '') {
            if (err.error === 'validation error') {
              if (this.completeFlag) {
                // if (this.taskUser.USER_ID === this.task.PRIMARY_USER) {
                // this.primaryUserValidationAlert(this.translate.instant('Please fill all mandatory fields to mark complete!'))
                // this.confirmationAlert(this.translate.instant('Please fill all mandatory fields to mark complete!'))
                // this.confirmationAlert(this.translate.instant('Form is partially filled. Still mark complete?'))
                await this.confirmationAlert();
                this.completeFlag = false;
                // } else {
                //   this.confirmationAlert(this.translate.instant('Form is not filled completely.  Proceed to mark complete?'));
                // }
              } else {
                await this.confirmationAlert();
                // this.confirmationAlert(this.translate.instant('Form cannot be completed until all required fields are entered. Save instead?'));
              }
            } else {
              let msg1 = '';
              if (err.error.length > 0) {
                for (var e11 = 0; e11 < err.error.length; e11++) {
                  msg1 = msg1 + '<br />' + err.error[e11].message;
                }
                msg1 = msg1.slice(6);
                this.loader.showToast(msg1);
              } else {
                this.loader.showToast(JSON.stringify(err.error));
              }
            }
          }
        }
      } else if (this.formData.display === 'form') {
        // FORM DATA TYPE IS FORM
        try {
          let val = await submit();
      
          if (val.submission !== '') {
            let clientCompField = this.attributesJson
              ? this.attributesJson.filter((e) => {
                return e.key.trim() == 'client-complete-check-field';
              })
              : [];
            let completeFld: boolean = true;
            if (
              clientCompField.length > 0 &&
              val.submission &&
              val.submission.data &&
              clientCompField[0].value !== ''
            ) {
              completeFld = val.submission.data[clientCompField[0].value];
            }
            if (!completeFld && this.markComplete) {
              await this.confirmationAlert();
              // this.confirmationAlert(this.translate.instant('Form cannot be completed until all required fields are entered. Save instead?'));
            } else {
              let arr = [];
              let keys = Object.keys(val.submission.data);
              Object.values(val.submission.data).forEach((ele, i) => {
                if (ele === '') {
                  arr.push(keys[i]);
                }
              });
              let promptOnFormComplete: boolean = false;
              let promptOnFormCompleteData = this.attributesJson
                ? this.attributesJson.filter((e) => {
                  return e.key.trim() == 'prompt-on-form-complete';
                })
                : [];
              if (
                promptOnFormCompleteData &&
                promptOnFormCompleteData.length > 0 &&
                promptOnFormCompleteData[0]
              ) {
                promptOnFormComplete = promptOnFormCompleteData[0].value;
                if (promptOnFormComplete && this.markComplete) {
                  let msgFormComplete = this.attributesJson
                    ? this.attributesJson.filter((e) => {
                      return e.key.trim() == 'message-form-complete';
                    })
                    : [];
                  if (
                    msgFormComplete &&
                    msgFormComplete.length > 0 &&
                    msgFormComplete[0]
                  ) {
                    let res = await this.confirmationAlertForComplete(
                      msgFormComplete[0].value
                    );
                    console.log('res' + res);
                    if (res) {
                      if (!this.loader.isLoading) {
                        await this.loader.showBusyIndicator(
                          this.translate.instant('Completing Form...'),
                          'crescent'
                        );
                      }
                      await this.updateDataAndSendToServer(val.submission, 'M');
                    }
                  }
                } else {
                  if (!this.markComplete) {
                    await this.confirmationAlert();
                  } else {
                    console.log('updateDataAndSendToServer at line 1173');
                    await this.updateDataAndSendToServer(val.submission, 'M');
                  }
                }
              } else {
                if (!this.markComplete) {
                  await this.confirmationAlert();
                } else {
                  console.log('updateDataAndSendToServer at line 1181');
                  await this.updateDataAndSendToServer(val.submission, 'M');
                }
              }
            }
          }
        } catch (err: any) {
          if (err.error !== '') {
            if (err.error === 'validation error') {
          
              let components = this.formData.components;
              let isApprovalform = false;

           
              for (let i = 0; i < components.length; i++) {
         
                if (
                  components[i].type === 'form' &&
                  components[i].tags &&
                  components[i].tags.length > 0
                ) {
              
                  for (let j = 0; j < components[i].tags.length; j++) {
                    if (components[i].tags[j] === 'Approval') {
                      isApprovalform = true;
                    }
                  }
                }
              }
              if (this.completeFlag) {
                this.completeFlag = false;
                await this.confirmationAlert();
              } else if (isApprovalform) {
                isApprovalform = false;
                await this.confirmationAlert();
              } else {
                await this.confirmationAlert();
              }
            } else {
              let msg = '';
              if (err.error.length > 0) {
                for (var e1 = 0; e1 < err.error.length; e1++) {
                  msg = msg + '<br />' + err.error[e1].message;
                }
                msg = msg.slice(6);
                this.loader.showToast(msg);
              } else {
                this.loader.showToast(JSON.stringify(err.error));
              }
              if (this.loader.isLoading) {
                this.unviredSDK.logInfo(
                  'PermitDetailsComponent',
                  'saveForm()',
                  'Hiding Busy indicator...'
                );
                if (this.loader.isLoading) {
                  await this.loader.dismissBusyIndicator();
                }
              }
            }
          }
        }
      } else {
        // other types of forms
        try {
          let val = await submit();
          if (val.submission !== '') {
            let clientCompField = this.attributesJson
              ? this.attributesJson.filter((e) => {
                return e.key.trim() == 'client-complete-check-field';
              })
              : [];
            let completeFld: boolean = true;
            if (
              clientCompField.length > 0 &&
              val.submission &&
              val.submission.data &&
              clientCompField[0].value !== ''
            ) {
              completeFld = val.submission.data[clientCompField[0].value];
            }
            if (!completeFld) {
              await this.confirmationAlert();
            } else {
              let arr = [];
              let keys = Object.keys(val.submission.data);
              Object.values(val.submission.data).forEach((ele, i) => {
                if (ele === '') {
                  arr.push(keys[i]);
                }
              });
              await this.updateDataAndSendToServer(val.submission, 'M');
            }
          }
        } catch (err: any) {
          if (this.loader.isLoading) {
            this.unviredSDK.logInfo(
              'PermitDetailsComponent',
              'saveForm()',
              'Hiding Busy indicator...'
            );
            if (this.loader.isLoading) {
              await this.loader.dismissBusyIndicator();
            }
          }
          if (err.error !== '') {
            if (err.error === 'validation error') {
              if (this.completeFlag) {
                await this.confirmationAlert();
                this.completeFlag = false;
              } else {
                await this.confirmationAlert();
              }
            } else {
              this.loader.showToast(err.error);
            }
          }
        }
      }
    } catch (error) {
      try {
        if (this.loader.isLoading) {
          await this.loader.dismissBusyIndicator();
        }
      } catch (error) {
        console.error('Error dismissing loader:', error);
      }
    }
  }


  async updateDataAndSendToServer(formSubmissionData: any, pMode: string) {
    try {
      let selectedPermit = localStorage.getItem('selectedPermit');

     
      if (selectedPermit) {
        this.permit = JSON.parse(selectedPermit);
      }
      this.permitHeaderData = this.permit;
      let fetchTaskSubmission: DbResult = await this.dataService.getFormDetails(
        this.permit.PERMIT_NO,
        this.form.FORM_ID
      );

      if (fetchTaskSubmission.type === ResultType.success) {
        let latestSubmissionDB: PERMIT_FORM[] = fetchTaskSubmission.data;
        let latestSubmission: PERMIT_FORM = latestSubmissionDB[0];
        let latestSubmissionTimestamp: number;
        console.log("formSubmissionData " , formSubmissionData , latestSubmissionDB[0])
        await this.goBackToLastLocation(formSubmissionData)
       
        await this.unviredSDK.unlockDataSender();
        if (this.loader.isLoading) {
          this.unviredSDK.logInfo(
            'PermitDetailsComponent',
            'updateDataAndSendToServer()',
            'Hiding Busy indicator...'
          );
          if (this.loader.isLoading) {
            await this.loader.dismissBusyIndicator();
          }
        }
      } else {
        await this.loader.showToast(
          this.translate.instant('Missing submission! try again later.')
        );
      }
    } catch (error) {
      this.unviredSDK.logError(
        'PermitDetailsComponent',
        'updateDataAndSendToServer()',
        'ERROR: ' + error
      );
    }
  }


  async getPartialFilledData() {
    // Get form data when form filled partially
    try {
      let partialFilledData = await returnTempData();
      let arr = [];
      let keys = Object.keys(partialFilledData.tempData.data);
      Object.values(partialFilledData.tempData.data).forEach((ele, i) => {
        if (ele === '') {
          arr.push(keys[i]);
        }
      });
    } catch (error) {
      this.unviredSDK.logError(
        'PermitDetailsComponent',
        'getPartialFilledData()',
        'ERROR: ' + error
      );
    }
  }

  async confirmationAlertForComplete(msg: string): Promise<boolean> {
    let resolveFunction: (confirm: boolean) => void;
    let promise = new Promise<boolean>((resolve) => {
      resolveFunction = resolve;
    });

    const alert = await this.alertController.create({
      header: this.translate.instant('Confirmation'),
      message: msg,
      animated: true,
      backdropDismiss: false,
      buttons: [
        {
          text: this.translate.instant('No'),
          role: 'cancel',
          handler: () => {
            resolveFunction(false);
          },
        },
        {
          text: this.translate.instant('Yes'),
          handler: () => {
            resolveFunction(true);
          },
        },
      ],
    });
    try {
      if (this.loader.isLoading) {
        this.unviredSDK.logInfo(
          'PermitDetailsComponent',
          'confirmationAlertForComplete()',
          'Hiding Busy indicator...'
        );
        if (this.loader.isLoading) {
          await this.loader.dismissBusyIndicator();
        }
      }
    } catch (error) {
      console.error('Error dismissing loader:', error);
    }
   
    await alert.present();
    return promise;
  }



  async confirmationAlert() {
    // Partial Save event
    await this.getPartialFilledData();

    if (this.markComplete) {
      let messages = this.translate.instant(
        'Form cannot be completed until all required fields are entered. Save instead?'
      );
      const alert = await this.alertController.create({
        header: this.translate.instant('Confirmation'),
        message: messages,
        animated: true,
        backdropDismiss: false,
        buttons: [
          {
            text: this.translate.instant('No'),
            role: 'cancel',
            handler: () => { },
          },
          {
            text: this.translate.instant('Yes'),
            handler: async () => {
              await this.draftForm('yes');
            },
          },
        ],
      });
      if (this.loader.isLoading) {
        this.unviredSDK.logInfo(
          'PermitDetailsComponent',
          'confirmationAlert()',
          'Hiding Busy indicator...'
        );
        if (this.loader.isLoading) {
          await this.loader.dismissBusyIndicator();
        }
      }
      await alert.present();
    } else {
      // Save
      // show promt when prompt-on-form-save is configured else save form
      let promptOnFormSave: boolean = false;
      let msg = '';
      if (!this.markComplete) {
        let promptOnFormSaveData = this.attributesJson
          ? this.attributesJson.filter((e) => {
            return e.key.trim() == 'prompt-on-form-save';
          })
          : [];
        if (
          promptOnFormSaveData &&
          promptOnFormSaveData.length > 0 &&
          promptOnFormSaveData[0]
        ) {
          promptOnFormSave = promptOnFormSaveData[0].value;
          if (promptOnFormSave) {
            let msgFormSave = this.attributesJson
              ? this.attributesJson.filter((e) => {
                return e.key.trim() == 'message-form-save';
              })
              : [];
            if (msgFormSave && msgFormSave.length > 0 && msgFormSave[0]) {
              msg = msgFormSave[0].value;
            }
          }
        }
      }
      if (promptOnFormSave) {
        let messages = '';
        if (msg && msg !== '' && msg.length > 0) {
          messages = msg;
        }
        if (this.calculatedPercentage === 100) {
          await this.draftForm('yes');
        } else {
          const alert = await this.alertController.create({
            header: this.translate.instant('Confirmation'),
            message: messages,
            animated: true,
            backdropDismiss: false,
            buttons: [
              {
                text: this.translate.instant('No'),
                role: 'cancel',
                handler: () => { },
              },
              {
                text: this.translate.instant('Yes'),
                handler: async () => {
                  await this.draftForm('yes');
                },
              },
            ],
          });
          if (this.loader.isLoading) {
            this.unviredSDK.logInfo(
              'PermitDetailsComponent',
              'confirmationAlert()',
              'Hiding Busy indicator...'
            );
            if (this.loader.isLoading) {
              await this.loader.dismissBusyIndicator();
            }
          }
          await alert.present();
        }
      } else {
      
     
        await this.draftForm('yes');
      }
    }
  }


    async draftForm(mode: any) {
      try {
        if (!this.loader.isLoading) {
          await this.loader.showBusyIndicator(
            this.translate.instant('Saving Form...'),
            'crescent'
          );
        }
        let val = await returnTempData();
        if (mode === 'yes') {
          let tempData = val.tempData ? val.tempData : val.submission;
          await this.updateDataAndSendToServer(tempData, 'A');
        } else {
          if (val.tempData !== '') {
            this.updateDataOnDraftForm(val.tempData);
          } else {
            let clientCompField = this.attributesJson
              ? this.attributesJson.filter((e) => {
                return e.key.trim() == 'client-complete-check-field';
              })
              : [];
            let completeFld =
              clientCompField.length > 0 &&
                val.submission &&
                val.submission.data !== '' &&
                clientCompField[0].value !== ''
                ? val.submission.data[clientCompField[0].value]
                : true;
            if (!completeFld) {
              this.updateDataOnDraftForm(val.submission);
            }
          }
        }
      } catch (error) {
        if (this.loader.isLoading) {
          this.unviredSDK.logInfo(
            'PermitDetailsComponent',
            'draftForm()',
            'Hiding Busy indicator...'
          );
          if (this.loader.isLoading) {
            await this.loader.dismissBusyIndicator();
          }
        }
        this.unviredSDK.logError(
          'PermitDetailsComponent',
          'draftForm()',
          'ERROR: ' + error
        );
      }
    }
    async updateDataOnDraftForm(data: any) {
      await this.updateDB(data);
      await this.unviredSDK.unlockDataSender();
      if (this.loader.isLoading) {
        this.unviredSDK.logInfo(
          'PermitDetailsComponent',
          'draftForm()',
          'Hiding Busy indicator...'
        );
        if (this.loader.isLoading) {
          await this.loader.dismissBusyIndicator();
        }
      }
      // }
    }
  
    async updateDB(submission: any) {
      console.log("update db called")
      let dbResponseForTaskSubmission = {} as DbResult;
      try {
        // this.permitFormSubmissionData.DATA = JSON.stringify(submission.data);
        try {
          let permitform: DbResult = await this.unviredSDK.dbExecuteStatement(`SELECT * FROM PERMIT_FORM WHERE PERMIT_NO = '${this.permitHeaderData.PERMIT_NO}' AND FORM_ID = '${this.form.FORM_ID}'`);
          console.log("permitform" + permitform)
          if (permitform.type === ResultType.success) {
            if (permitform?.data?.length > 0) {
              let permitFormDBRes = permitform.data[0]
              this.permitHeaderData.COMMENTS = (submission.data) ? submission.data.permitComments : this.permitHeaderData.COMMENTS;
              permitFormDBRes.DATA = JSON.stringify(submission.data);
              if (this.markComplete) {
                permitFormDBRes.PARTIAL_FLAG = AppConstants.NO;
                permitFormDBRes.COMPLETED = AppConstants.YES;
              } else {
                permitFormDBRes.PARTIAL_FLAG = AppConstants.YES;
                permitFormDBRes.COMPLETED = AppConstants.NO;
              }
  
              permitFormDBRes.CHANGED_ON = moment().valueOf();
              permitFormDBRes.P_MODE = 'M';
              permitFormDBRes.OBJECT_STATUS = 2;
              permitFormDBRes.FID = this.permit.LID;
              dbResponseForTaskSubmission = await this.unviredSDK.dbInsertOrUpdate('PERMIT_FORM', permitFormDBRes, false);
              if (dbResponseForTaskSubmission.type === ResultType.success) {
                let a = `SELECT * FROM PERMIT_FORM WHERE PERMIT_NO = '${this.permit.PERMIT_NO}'`;
                let permitUpdateQueryResult = await this.unviredSDK.dbExecuteStatement(
                  a
                );
                // console.log("permit form after updated " + JSON.stringify(permitUpdateQueryResult));
              }
              await this.showResponseWithToaster(
                dbResponseForTaskSubmission,
                this.loader.isLoading
              );
  
              // Update task_submission properties
              // this.permitFormSubmissionData.PERMIT_NO = 
              //   this.permitHeaderData.PERMIT_NO;
              // this.permitFormSubmissionData.FORM_NAME = this.form.FORM_NAME;
              // this.permitFormSubmissionData.FORM_TITLE = this.form.FORM_TITLE;
              // this.permitFormSubmissionData.FORM_VERSION = this.form.FORM_VERSION;
              // this.permitFormSubmissionData.FORM_ID = this.form.FORM_ID;
  
              // this.permitFormSubmissionData.FORM_GUID = this.permitFormSubmissionData.FORM_GUID? this.permitFormSubmissionData.FORM_GUID : this.unviredSDK
              //   .guid()
              //   .replace(/-/g, '');
              // // this.permitFormSubmissionData.OBJECT_STATUS = this.constants.OBJECT_STATUS.MODIFY;
              // this.permitFormSubmissionData.LID = this.permitFormSubmissionData.LID
              //   ? this.permitFormSubmissionData.LID
              //   : this.utilityFunction.generateUUID();
              // // this.permitFormSubmissionData.OBJECT_STATUS = this.constants.OBJECT_STATUS.ADD;
              // this.permitFormSubmissionData.FID = this.permitHeaderData.LID
              //   ? this.permitHeaderData.LID
              //   : '';
              // if (this.markComplete) {
              //   this.permitFormSubmissionData.PARTIAL_FLAG = AppConstants.NO;
              //   this.permitFormSubmissionData.COMPLETED = AppConstants.YES;
              // } else {
              //   this.permitFormSubmissionData.PARTIAL_FLAG = AppConstants.YES;
              //   this.permitFormSubmissionData.COMPLETED = AppConstants.NO;
              // }
              // this.permitFormSubmissionData.CHANGED_ON = moment().valueOf();
              // let userContext: any = localStorage.getItem('userContext');
              // let user = '';
              // if (userContext) {
              //   userContext = JSON.parse(userContext);
              // }
  
              // if (userContext?.USER_CONTEXT_HEADER) {
              //   user =
              //     userContext?.USER_CONTEXT_HEADER?.FIRST_NAME +
              //     ' ' +
              //     userContext?.USER_CONTEXT_HEADER?.LAST_NAME;
              // }
              // this.permitFormSubmissionData.CHANGED_BY = user;
  
              // this.permitFormSubmissionData.OBJECT_STATUS = 2;
              // this.permitFormSubmissionData.P_MODE = pMode;
              // let res = await this.checkPermitFormDuplicate(this.form.FORM_ID);
              // if (res && res.data.length > 0) {
              //   this.permitFormSubmissionData.P_MODE = 'M';
              //   this.permitFormSubmissionData.OBJECT_STATUS = 2;
              //   let deleteQuery = `DELETE FROM PERMIT_FORM WHERE PERMIT_NO = '${this.permit.PERMIT_NO}' AND FORM_ID = '${this.form.FORM_ID}'`;
              //   await this.unviredSDK.dbExecuteStatement(deleteQuery);
              // }else{
              //   this.permitFormSubmissionData.P_MODE = 'A';
              //   this.permitFormSubmissionData.OBJECT_STATUS = 2;
              // }
              // dbResponseForTaskSubmission = await this.unviredSDK.dbInsert('PERMIT_FORM', this.permitFormSubmissionData, false);
              // let permitStakeHolderUpdateQuery = `UPDATE PERMIT_FORM SET OBJECT_STATUS = 2,CHANGED_BY = '${user}', P_MODE='M', PARTIAL_FLAG='X', COMPLETED='', FORM_GUID = '${this.unviredSDK
              //   .guid()
              //   .replace(/-/g, '')}' WHERE PERMIT_NO = '${
              //   this.permitFormSubmissionData.PERMIT_NO
              // }' AND FORM_ID = '${this.permitFormSubmissionData.FORM_ID}'`;
              // let res = await this.unviredSDK.dbExecuteStatement(
              //   permitStakeHolderUpdateQuery
              // );
  
              // dbResponseForTaskSubmission = await this.dataService.updatePermiform(this.permitFormSubmissionData, this.permitFormSubmissionData.PERMIT_NO, this.permitFormSubmissionData.FORM_ID)
              // dbResponseForTaskSubmission = await this.unviredSDK.dbUpdate(this.constants.PERMIT_FORM, this.permitFormSubmissionData, `PERMIT_NO = '${this.permitFormSubmissionData.PERMIT_NO}' AND FORM_ID = '${this.permitFormSubmissionData.FORM_ID}'`);
              // await this.showResponseWithToaster(
              //   dbResponseForTaskSubmission,
              //   this.loader.isLoading
              // );
  
            } else {
              // zero records found
            }
          } else {
            // error resp
          }
        } catch (error) {
          this.unviredSDK.logError(
            'PermitDetailsComponent',
            'updateDB()',
            'Caught Error: ' + JSON.stringify(error)
          );
          if (this.loader.isLoading) {
            this.unviredSDK.logInfo(
              'PermitDetailsComponent',
              'updateDB()',
              'Hiding Busy indicator...'
            );
            if (this.loader.isLoading) {
              await this.loader.dismissBusyIndicator();
            }
          }
          await this.loader.showAlert(
            'Error',
            this.translate.instant('Record not inserted!')
          );
        }
      } catch (error: any) {
        this.unviredSDK.logError(
          'PermitDetailsComponent',
          'updateDB()',
          'Caught Error: ' + JSON.stringify(error)
        );
        if (this.loader.isLoading) {
          this.unviredSDK.logInfo(
            'PermitDetailsComponent',
            'updateDB()',
            'Hiding Busy indicator...'
          );
          if (this.loader.isLoading) {
            await this.loader.dismissBusyIndicator();
          }
        }
        await this.loader.showAlert('Error', error);
      }
    }


    async showResponseWithToaster(response: any, busyindicator?: boolean) {
      if (response.message && response.message.trim().length !== 0) {
        if (busyindicator) {
          this.unviredSDK.logInfo(
            'PermitDetailsComponent',
            'showResponseWithToaster()',
            'Hiding Busy indicator...'
          );
          if (this.loader.isLoading) {
            await this.loader.dismissBusyIndicator();
          }
        }
        this.loader.showToast(response.message);
      } else if (response.error && response.error.trim().length !== 0) {
        if (busyindicator) {
          this.unviredSDK.logInfo(
            'PermitDetailsComponent',
            'showResponseWithToaster()',
            'Hiding Busy indicator...'
          );
          if (this.loader.isLoading) {
            await this.loader.dismissBusyIndicator();
          }
        }
        // this.toastr.error('', response.error, { timeOut: 2000 });
      }
    }
    

    async goBackToLastLocation(formsubmissiondata: any) {
        let isFormDataChanged: boolean = false;
        let lastChanges
        try {
          let lastFormData = await returnLastTempData();
          if(lastFormData.error == 'validation error'){
            console.log("the selected stakeholder in lastchanges is" , )
            
            lastChanges = JSON.stringify(lastFormData.tempData.data)
            console.log("lastChanges" , lastChanges);
        
     
            console.log("lastChanges" , lastChanges);
            // console.log("lastChanges" , lastChanges);
            // let permitformUpdateQuery = `UPDATE PERMIT_FORM SET PARTIAL_FLAG = ${AppConstants.YES} WHERE PERMIT_NO ='${this.permit.PERMIT_NO}'`;
          
            // let permitformUpdateResult = await this.unviredSDK.dbExecuteStatement(
            //   permitformUpdateQuery
            // );
            // if (permitformUpdateResult.type == ResultType.success) {
            //   // this.unviredSDK.dbExportWebData()
            //   console.log("permitformUpdateResult" + permitformUpdateResult);
            // } else {
            //   console.log("FAILURE - update permit form details in DB")
            // }
         
          } else {
    
               this.selectedStakeholder.CONTEXT = 'FORM_COMPLETED'
            const stakeholderIndex = this.stakeHoldersList.findIndex(
              stakeholder => stakeholder.ROW_ID === this.selectedStakeholder.ROW_ID ,
           
            );
          
            if (stakeholderIndex !== -1) {
              // this.stakeHoldersList[stakeholderIndex].CONTEXT = 'FORM_COMPLETED';
              // this.actionButtons(this.stakeHolderActionStatus , this.stakeHoldersList[stakeholderIndex].ROW_ID , this.stakeHoldersList[stakeholderIndex])
              console.log("Updated stakeholder:", this.stakeHoldersList[stakeholderIndex]);
            //  alert('FORM_COMPLETED')
            }
           
            
            let stakeHolderUpdateQuery = `UPDATE PERMIT_STAKEHOLDER SET P_MODE = 'M' , CONTEXT = 'FORM_COMPLETED', OBJECT_STATUS = 2 WHERE PERMIT_NO = '${this.permit.PERMIT_NO}' AND ROW_ID = '${this.selectedStakeholder.ROW_ID}'`;
            let stakeHolderUpdateQueryResult = await this.unviredSDK.dbExecuteStatement(
              stakeHolderUpdateQuery
            );
            if (
              stakeHolderUpdateQueryResult &&
              stakeHolderUpdateQueryResult.data
            ) {
              console.log("stakeHolderUpdateQueryResult" , stakeHolderUpdateQueryResult.data)
  
            }
            lastChanges = JSON.stringify(lastFormData.submission.data);
            console.log("lastChanges" , lastChanges);
    
            let hashValueWithLastChanges = hash(lastChanges);
    
            let data = localStorage.getItem("initialFormData");
            let hashValueWithInitialData = hash(data);
            let diff = hashValueWithLastChanges.localeCompare(hashValueWithInitialData);
            isFormDataChanged = (diff === 0) ? false : true;

            this.modalController.dismiss({ saved: true }); // Close the modal and pass data
          }
        
         
    
        } catch (error) {
          this.unviredSDK.logError('FormRendererPage', 'returnLastTempData()', 'ERROR: ' + error);
        }
        if (isFormDataChanged && (!this.formReadOnlyFlag)) {
          // CHANGE IN submission DATA
          console.log(" data changed")
          await this.updateDB(
            formsubmissiondata
          );
        } else {
          // NO CHANGE IN DATA
          console.log(" no data changed in form submission")
        }
      }


      prepareTabulatorData() {
        try {
          if (
            $('.table').find('th').length > 0 ||
            $('.list-group-header').length > 0
          ) {
            this.alignTableContents();
            $('head').append(
              '<link rel="stylesheet" href="assets/css/responsive-grid.min.css" type="text/css" />'
            );
          }
        } catch (err) {
          console.log('err ' + err);
        }
      }
    
      alignTableContents() {
        let activeMode = false;
        if (this.platform.is('mobile') && $(window).width() < 760) {
          activeMode = true;
        }
        insertContents();
        flexTable();
    
        window.onresize = () => {
          if (
            $('.table').find('th').length > 0 ||
            $('.list-group-header').length > 0
          ) {
            flexTable();
          }
        };
        (window).on('orientationchange resize', function () {
          console.log("orienattion change")
          if (
            $('.table').find('th').length > 0 ||
            $('.list-group-header').length > 0
          ) {
            flexTable();
          }
        });
    
        function flexTable() {
          if ($(window).width() < 760 || activeMode) {
            // window is less than 768px
            showTableContents();
          } else {
            $('.list-group-header').show();
            $('.datagrid-table').each(function (tabBorderedVal) {
              $('.table').each(function (tabBorderedVal) {
                if ($(this).find('table').length == 0) {
                  $(this).find('.table-bordered-thead').show();
                  $(this).find('thead').show();
                  $(this).find('tr').removeClass('datagrid-table-row');
                  $(this).find('tr').find('td').removeClass('datagrid-table-data');
                }
              });
            });
          }
        }
    
        function showTableContents() {
          $('.list-group-header').hide();
          $('.datagrid-table').each(function (tabBorderedVal) {
            $('.table').each(function (val) {
              if ($(this).find('table').length == 0) {
                $(this).find('.table-bordered-thead').show();
                $(this).find('thead').hide();
                if ($(window).width() < 760) {
                  $(this).find('tr').addClass('datagrid-table-row');
                  $(this).find('tr').find('td').addClass('datagrid-table-data');
                  $('.table').css('table-layout', 'fixed');
                } else {
                  $('.table').each(function (tabBorderedVal) {
                    if ($(this).find('table').length == 0) {
                      $(this).find('tr').removeClass('datagrid-table-row');
                      $(this)
                        .find('tr')
                        .find('td')
                        .removeClass('datagrid-table-data');
                    }
                  });
    
                  $('.datagridcard').each(function (datagridcardVal) {
                    $('.datagrid-table').each(function (tabBorderedVal) {
                      if ($(this).find('table').length == 0) {
                        $(this).find('tr').addClass('datagrid-table-row');
                        $(this).find('tr').addClass('col-md-4');
                        $(this).find('tr').addClass('col-lg-3');
    
                        $(this)
                          .find('tr')
                          .find('td')
                          .addClass('datagrid-table-data');
                        $('.datagrid-table-row').css('border', 'none');
                        $('.datagrid-table-row').css('margin-top', '10px');
                      }
                    });
                  });
                }
              }
            });
          });
    
          $('.table').each(function (value) {
            if ($(this).find('table').length == 0) {
              var tabledata = $(this).find('td');
              $(tabledata).each(function (val) {
                if (
                  $(this)[0].children &&
                  $(this)[0].children[1] &&
                  $(this)[0].children[1].nodeName != undefined &&
                  $(this)[0].children[1].nodeName == 'BUTTON'
                ) {
                  if ($(this)[0].innerText.includes('Add')) {
                    $(this).find('.table-bordered-thead').hide();
                  }
                }
              });
            }
            $(this)
              .find('tfoot')
              .each(function (val) {
                $(this).find('.table-bordered-thead').hide();
              });
          });
        }
    
        $(document)
          .off()
          .on('click', '.btn', (e) => {
            if ($(window).width() < 760) {
              if (e && e.currentTarget) {
                var val = e.currentTarget.getAttribute('ref');
                var name = e.currentTarget.getAttribute('name');
    
                if (val != null) {
                  if (val.includes('editgrid')) {
                    $('.list-group-header').hide();
                  } else if (
                    val.includes('addRow') ||
                    val.includes('removeRow') ||
                    (name && name.includes('updateTask')) ||
                    (name && name.includes('resetTask'))
                  ) {
                    $('.list-group-header').hide();
                    $('table').find('thead').hide();
                    insertContents();
                    flexTable();
                  }
                } else if (
                  e.currentTarget.nodeName === 'BUTTON' &&
                  (e.currentTarget.outerHTML.includes('fa-edit') ||
                    e.currentTarget.outerHTML.includes('fa-trash'))
                ) {
                  if ($('.list-group-header').length > 0) {
                    $('.list-group-header').hide();
                  }
                }
              }
            }
          });
    
        function insertContents(this: any) {
          if ($(window).width() < 760) {
            $('.datagrid-table').each(function (tabBorderedVal) {
              $('.table').each(function (obj) {
                if ($(this).find('table').length == 0) {
                  let $pathis = $(this);
                  // fetch the table heading and map with table row entry
                  let tHeadings = $pathis.find('th');
                  $pathis.find('tr').each(function (trObj) {
                    $(this).addClass('table-row');
                    let $thisObj = $(this);
                    $thisObj.find('td').each(function (tdObj) {
                      let classList: any;
                      let $this = $(this);
                      if (
                        $(this)[0] &&
                        $(this)[0].firstElementChild &&
                        $(this)[0].firstElementChild.classList
                      ) {
                        classList = $(this)[0].firstElementChild.classList;
                      }
                      if (
                        classList &&
                        (classList.contains('formio-button-remove-row') ||
                          classList.contains('formio-button-add-row'))
                      ) {
                      } else {
                        $this.addClass('table-td');
                        if (
                          $(this).find('.table-bordered-thead').length == 0 &&
                          tHeadings[tdObj] != undefined
                        ) {
                          let text = tHeadings[tdObj].innerText;
                          $this.append(
                            '<div class="table-bordered-thead">' + text + '</div> '
                          );
                          $('.formio-component-label-hidden').css('order', '2');
                          $('.table-bordered-thead').hide();
                        } else if (
                          $(this).find('.table-bordered-thead').length > 0 &&
                          tHeadings[tdObj] != undefined
                        ) {
                          $('.formio-component-label-hidden').css('order', '2');
                        }
                      }
                    });
                  });
                }
              });
            });
    
            $('.table').each(function (value) {
              if ($(this).find('table').length == 0) {
                var tabledata = $(this).find('td');
                $(tabledata).each(function (val) {
                  if (
                    $(this)[0].children &&
                    $(this)[0].children[1] &&
                    $(this)[0].children[1].nodeName != undefined &&
                    $(this)[0].children[1].nodeName == 'BUTTON'
                  ) {
                    if ($(this)[0].innerText.includes('Add')) {
                      $(this).find('.table-bordered-thead').hide();
                    }
                  }
                });
              }
            });
    
            $(this)
              .find('tfoot')
              .each(function (val) {
                $(this).find('.table-bordered-thead').hide();
              });
          }
        }
      }


      
  async checkAllFieldsAreField(stakeholder: any){
    let val = await returnTempData();
    if (val) {
      let tempdata = val.tempData ? val.tempData : val.submission;
      // Check if context exists and validate its values
      if (tempdata && tempdata.data && tempdata.data[stakeholder.CONTEXT]) {
        let contextData = tempdata.data[stakeholder.CONTEXT];
        console.log("context data & temp data" , contextData , tempdata)
        let hasEmptyValues = false;

        // Check all values in the context object
        Object.keys(contextData).forEach(key => {
          if (!contextData[key] || contextData[key] === '') {
            hasEmptyValues = true;
          }
        });

        // if (hasEmptyValues) {
        //   const alert = await this.alertController.create({
        //     header: 'Form Incomplete',
        //     message: 'Please fill all required fields in the form before proceeding.',
        //     buttons: ['OK']
        //   });
        //   await alert.present();
        //   return;
        // }else{
        //   const alert = await this.alertController.create({
        //     header: 'Form Completed',
        //     message: 'All fields are filled',
        //     buttons: ['OK']
        //   });
        //   await alert.present();
        //   return;
        // }
      }
  }
 }

 searchForApprovalType(formDataComp: any, appr_type: string): boolean {
  try {
    let hasMatchingApprovalType = false;

    // Recursive function to traverse the component tree
    const traverse = (component: FormComponent): void => {
      // Check if this is the component we're looking for
      if (component.key === appr_type) {
        hasMatchingApprovalType = true;
        return;
      }

      // Handle nested components array
      if (Array.isArray(component.components)) {
        component.components.forEach(child => traverse(child));
      }

      // Handle columns structure
      if (component.columns) {
        component.columns.forEach(column => {
          if (Array.isArray(column.components)) {
            column.components.forEach(child => traverse(child));
          }
        });
      }
    };

    // Start traversal from the root components array
    if (Array.isArray(formDataComp?.components)) {
      formDataComp.components.forEach(component => traverse(component));
    }

    return hasMatchingApprovalType;

  } catch (error) {
    console.error("Error in searchForApprovalType:", error);
    return false; // Return false on error
  }
}


async displayPleaseWaitLoader() {
  const loading = await this.loadingController.create({
    message: this.translate.instant('Please wait') + '...',
    backdropDismiss: false,
  });
  await loading.present();
}


}





