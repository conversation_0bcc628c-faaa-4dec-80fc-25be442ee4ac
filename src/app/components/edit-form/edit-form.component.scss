:host {
    display: flex;
    flex-direction: column;
    height: 100%;
    
    ion-header {
      flex-shrink: 0;
    }
  
    ion-content {
      flex: 1 1 auto;
      overflow: hidden;
      position: relative;
    }
  
    ion-footer {
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
      width: 100%;
      z-index: 999;
      background: var(--ion-background-color);
    }
  }
  
  .content-area {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .scroll-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    -webkit-overflow-scrolling: touch;
    background: #ffffff;
  
    #formio {
      min-height: 100%;
      margin-bottom: 16px;
    }
  }
  
  // Custom scrollbar styling
  :host ::ng-deep {
    .scroll-container::-webkit-scrollbar {
      width: 6px;
    }
  
    .scroll-container::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
  
    .scroll-container::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }
  
    .scroll-container::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }


  ion-footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    // ion-toolbar {
    //   --background: #ffffff;
    //   border-top: 1px solid #dedede;
    //   padding: 8px 16px;
    // }
    .save-button {
      --padding-start: 24px;
      --padding-end: 24px;
      margin-right: 16px;
    }
  }