import { Component, OnInit, Input, Output, EventEmitter, ViewChild, ElementRef, AfterViewInit, NgZone, ChangeDetectorRef } from '@angular/core';
import { ModalController } from '@ionic/angular';

@Component({
  selector: 'app-location-picker',
  templateUrl: './location-picker.component.html',
  styleUrls: ['./location-picker.component.scss'],
})
export class LocationPickerComponent implements OnInit, AfterViewInit {
  @ViewChild('mapContainer', { static: false }) mapContainer: ElementRef;
  @Input() initialLatitude: number = 0;
  @Input() initialLongitude: number = 0;
  @Input() facilityBoundary: string = '';
  @Input() facilityName: string = '';
  @Output() locationSelected = new EventEmitter<{latitude: number, longitude: number}>();

  map: google.maps.Map;
  marker: google.maps.Marker;
  boundaryPolygon: google.maps.Polygon;
  selectedLocation: {latitude: number | null, longitude: number | null} = {latitude: null, longitude: null};
  isLocationWithinBoundary: boolean = false;
  showOutsideBoundaryWarning: boolean = false;
  facilityNameOverlay: google.maps.OverlayView = null;

  constructor(
    private modalController: ModalController,
    private zone: NgZone,
    private changeDetectorRef: ChangeDetectorRef
  ) { }

  ngOnInit() {
    console.log('LocationPickerComponent initialized with:', {
      initialLatitude: this.initialLatitude,
      initialLongitude: this.initialLongitude,
      facilityBoundary: this.facilityBoundary ? 'Present' : 'Not present'
    });

    // Initialize with input values if provided
    if (this.initialLatitude && this.initialLongitude) {
      this.selectedLocation = {
        latitude: this.initialLatitude,
        longitude: this.initialLongitude
      };
      console.log('Initial location set:', this.selectedLocation);
    } else {
      console.log('No initial coordinates provided');
    }
  }

  ngAfterViewInit() {
    this.initMap();
  }

  initMap() {
    console.log('Initializing map');

    // Create map with default options
    this.map = new google.maps.Map(this.mapContainer.nativeElement, {
      zoom: 15,
      mapTypeId: google.maps.MapTypeId.ROADMAP,
      mapTypeControl: true,
      streetViewControl: true,
      fullscreenControl: true
    });

    // Add a listener to know when the map is fully loaded
    google.maps.event.addListenerOnce(this.map, 'idle', () => {
      console.log('Map is fully loaded and ready');
    });

    // Display facility boundary if provided
    if (this.facilityBoundary) {
      this.displayFacilityBoundary();
    } else {
      // If no boundary, use initial coordinates or get current location
      const center = this.initialLatitude && this.initialLongitude
        ? { lat: this.initialLatitude, lng: this.initialLongitude }
        : { lat: 0, lng: 0 };

      this.map.setCenter(center);

      // Try to get user's current location if no initial coordinates
      if (!this.initialLatitude && !this.initialLongitude) {
        this.getCurrentLocation();
      }
    }

    // Only add a marker and set coordinates if initial values are provided
    if (this.initialLatitude && this.initialLongitude) {
      // Use provided coordinates
      const initialPosition = {
        lat: this.initialLatitude,
        lng: this.initialLongitude
      };
      console.log('Using initial coordinates:', initialPosition);

      // Set map center
      this.map.setCenter(initialPosition);

      // Add marker
      this.marker = new google.maps.Marker({
        position: initialPosition,
        map: this.map,
        draggable: true,
        animation: google.maps.Animation.DROP
      });

      // Update selected location
      this.updateSelectedLocation(initialPosition.lat, initialPosition.lng);
    } else {
      console.log('No initial coordinates, leaving fields empty');

      // Don't add a marker yet - wait for user to click on the map
      this.marker = null;

      // Leave the selected location as null
      this.selectedLocation = { latitude: null, longitude: null };
    }

    // Add map click listener to create or move marker
    google.maps.event.addListener(this.map, 'click', (event) => {
      console.log('Map click event triggered');
      const lat = event.latLng.lat();
      const lng = event.latLng.lng();

      console.log('Click coordinates:', { lat, lng });

      // Create marker if it doesn't exist
      if (!this.marker) {
        this.marker = new google.maps.Marker({
          position: event.latLng,
          map: this.map,
          draggable: true,
          animation: google.maps.Animation.DROP
        });

        // Add drag listener to the newly created marker
        google.maps.event.addListener(this.marker, 'dragend', (dragEvent) => {
          console.log('Marker drag event triggered');
          const dragLat = dragEvent.latLng.lat();
          const dragLng = dragEvent.latLng.lng();

          console.log('Drag end coordinates:', { dragLat, dragLng });
          this.updateSelectedLocation(dragLat, dragLng);

          // Force Angular to detect changes
          this.runChangeDetection();
        });
      } else {
        // Move existing marker
        this.marker.setPosition(event.latLng);
      }

      // Update selected location
      this.updateSelectedLocation(lat, lng);

      // Force Angular to detect changes
      this.runChangeDetection();
    });
  }

  // Display facility boundary on the map
  displayFacilityBoundary() {
    console.log('displayFacilityBoundary called');

    // Clear any existing marker
    if (this.marker) {
      this.marker.setMap(null);
      this.marker = null;
    }

    if (!this.facilityBoundary) {
      console.warn('No facility boundary provided');
      return;
    }

    console.log('Facility boundary WKT:', this.facilityBoundary);

    try {
      // Convert WKT to polygon coordinates
      const coordinates = this.wktToCoordinates(this.facilityBoundary);

      console.log('Parsed coordinates count:', coordinates.length);

      if (coordinates.length > 0) {
        console.log('Creating polygon with coordinates:', coordinates);

        // Create polygon for the facility boundary
        this.boundaryPolygon = new google.maps.Polygon({
          paths: coordinates,
          strokeColor: '#3880ff',
          strokeOpacity: 0.8,
          strokeWeight: 2,
          fillColor: '#3880ff',
          fillOpacity: 0.2,
          map: this.map,
          clickable: false // Make polygon not intercept clicks
        });

        // Fit map to boundary with margins
        try {
          const bounds = new google.maps.LatLngBounds();
          coordinates.forEach(coord => bounds.extend(coord));

          // Check if bounds are valid
          if (!bounds.isEmpty()) {
            this.map.fitBounds(bounds);
            console.log('Map fitted to bounds:', bounds.toString());

            // Calculate center of polygon for facility name label
            const center = this.calculatePolygonCenter(coordinates);

            // Add facility name label
            this.addFacilityNameLabel(center);

            // Add a small padding to ensure the polygon is fully visible
            google.maps.event.addListenerOnce(this.map, 'bounds_changed', () => {
              try {
                const currentZoom = this.map.getZoom();
                console.log('Current zoom after fitting bounds:', currentZoom);

                if (currentZoom && currentZoom > 3) {
                  // Zoom out slightly to add margin, but not too far
                  const newZoom = Math.max(currentZoom - 1, 3);
                  this.map.setZoom(newZoom);
                  console.log('Adjusted zoom to:', newZoom);
                }
              } catch (error) {
                console.error('Error adjusting zoom:', error);
              }
            });
          } else {
            console.warn('Bounds are empty, using default center and zoom');
            this.map.setCenter({ lat: 37.0902, lng: -95.7129 });
            this.map.setZoom(10);
          }
        } catch (error) {
          console.error('Error fitting bounds:', error);
        }

        // Add click listener to the polygon for debugging
        google.maps.event.addListener(this.boundaryPolygon, 'click', (event) => {
          console.log('Polygon clicked at:', { lat: event.latLng.lat(), lng: event.latLng.lng() });
        });
      } else {
        console.warn('No valid coordinates parsed from WKT');
      }
    } catch (error) {
      console.error('Error displaying facility boundary:', error);
    }
  }

  // Convert WKT to coordinates
  wktToCoordinates(wkt: string): google.maps.LatLngLiteral[] {
    const coordinates: google.maps.LatLngLiteral[] = [];

    if (!wkt || !wkt.startsWith('POLYGON')) {
      console.warn('Invalid WKT format or empty string:', wkt);
      return coordinates;
    }

    try {
      console.log('Parsing WKT:', wkt);

      // Extract coordinates from WKT format
      const coordsString = wkt.substring(wkt.indexOf('((') + 2, wkt.indexOf('))'));
      const coordPairs = coordsString.split(',');

      console.log('Extracted coordinate pairs:', coordPairs);

      coordPairs.forEach((pair, index) => {
        const trimmedPair = pair.trim();
        const parts = trimmedPair.split(' ');

        // WKT format is typically LONGITUDE LATITUDE (x y)
        // Make sure we have exactly two parts
        if (parts.length !== 2) {
          console.warn(`Invalid coordinate pair at index ${index}:`, trimmedPair);
          return; // Skip this pair
        }

        const lngStr = parts[0];
        const latStr = parts[1];

        // Parse with high precision
        const lng = parseFloat(parseFloat(lngStr).toFixed(6));
        const lat = parseFloat(parseFloat(latStr).toFixed(6));

        if (isNaN(lat) || isNaN(lng)) {
          console.warn(`Invalid coordinate values at index ${index}:`, { latStr, lngStr });
          return; // Skip this pair
        }

        coordinates.push({ lat, lng });
      });

      console.log('Parsed coordinates:', coordinates);
    } catch (error) {
      console.error('Error parsing WKT:', error);
    }

    return coordinates;
  }

  // Check if a location is within the facility boundary
  checkLocationWithinBoundary(lat: number | null, lng: number | null): boolean {
    if (lat === null || lng === null) {
      this.isLocationWithinBoundary = false;
      return false;
    }

    if (!this.boundaryPolygon) {
      // If no boundary polygon, assume location is valid
      this.isLocationWithinBoundary = true;
      return true;
    }

    try {
      const point = new google.maps.LatLng(lat, lng);

      // Debug information
      console.log('Checking if point is within boundary:', { lat, lng });

      // Try using isLocationOnEdge first with a small tolerance
      const onEdge = google.maps.geometry.poly.isLocationOnEdge(point, this.boundaryPolygon, 1e-9);

      // Then check if it's contained within the polygon
      const isInside = google.maps.geometry.poly.containsLocation(point, this.boundaryPolygon);

      console.log('Location check results:', { onEdge, isInside });

      // Consider the point valid if it's either inside or on the edge
      this.isLocationWithinBoundary = isInside || onEdge;

      // Invert the result for testing - TEMPORARY FIX
      // this.isLocationWithinBoundary = !this.isLocationWithinBoundary;

      return this.isLocationWithinBoundary;
    } catch (error) {
      console.error('Error checking if location is within boundary:', error);
      // Default to allowing the location if there's an error
      this.isLocationWithinBoundary = true;
      return true;
    }
  }

  getCurrentLocation() {
    console.log('Getting current location');

    // Default position (center of the US)
    const defaultPosition = { lat: 37.0902, lng: -95.7129 };

    if (navigator.geolocation) {
      try {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            try {
              const pos = {
                lat: position.coords.latitude,
                lng: position.coords.longitude
              };
              console.log('Got current position:', pos);

              if (this.map && this.marker) {
                this.map.setCenter(pos);
                this.marker.setPosition(pos);
                this.updateSelectedLocation(pos.lat, pos.lng);
              } else {
                console.error('Map or marker not initialized');
              }
            } catch (error) {
              console.error('Error processing geolocation result:', error);
              this.useDefaultPosition();
            }
          },
          (error) => {
            console.log('Geolocation error:', error);
            this.useDefaultPosition();
          },
          {
            enableHighAccuracy: true,
            timeout: 5000,
            maximumAge: 0
          }
        );
      } catch (error) {
        console.error('Error calling geolocation API:', error);
        this.useDefaultPosition();
      }
    } else {
      console.log('Error: Your browser doesn\'t support geolocation.');
      this.useDefaultPosition();
    }
  }

  // Helper method when geolocation fails
  useDefaultPosition() {
    console.log('Geolocation failed, not setting any default position');

    // Center map on a reasonable default but don't set a marker or coordinates
    if (this.map) {
      // Center on US by default, but don't create a marker
      this.map.setCenter({ lat: 37.0902, lng: -95.7129 });
      this.map.setZoom(4); // Zoom out to show a larger area
    }

    // Clear any existing marker
    if (this.marker) {
      this.marker.setMap(null);
      this.marker = null;
    }

    // Clear the selected location
    this.updateSelectedLocation(null, null);
  }

  updateSelectedLocation(lat: number | null, lng: number | null) {
    console.log('Updating selected location:', { lat, lng });

    if (lat === null || lng === null) {
      this.selectedLocation = {
        latitude: null,
        longitude: null
      };
      console.log('Selected location set to null');
      return;
    }

    // Format to 6 decimal places for display
    const formattedLat = parseFloat(lat.toFixed(6));
    const formattedLng = parseFloat(lng.toFixed(6));

    this.selectedLocation = {
      latitude: formattedLat,
      longitude: formattedLng
    };

    console.log('Selected location updated:', this.selectedLocation);

    // Check if the selected location is within the facility boundary
    this.checkLocationWithinBoundary(formattedLat, formattedLng);

    // Force change detection
    this.zone.run(() => {
      this.changeDetectorRef.detectChanges();
    });
  }

  confirmLocation() {
    console.log('Confirm location clicked');

    // Check if a location has been selected
    if (this.selectedLocation.latitude === null || this.selectedLocation.longitude === null) {
      console.warn('No location selected, cannot confirm');
      return;
    }

    // Check if the location is within the boundary
    if (this.boundaryPolygon && !this.isLocationWithinBoundary) {
      console.warn('Location is outside boundary, cannot confirm');
      return;
    }

    // Location is valid, emit and dismiss
    this.locationSelected.emit(this.selectedLocation);
    this.modalController.dismiss(this.selectedLocation);
  }

  cancel() {
    this.modalController.dismiss();
  }

  // Force Angular to detect changes
  runChangeDetection() {
    this.zone.run(() => {
      console.log('Forcing change detection');
      this.changeDetectorRef.detectChanges();
    });
  }

  /**
   * Calculate the center point of a polygon
   * @param coordinates Array of coordinates that make up the polygon
   * @returns The center point of the polygon
   */
  calculatePolygonCenter(coordinates: google.maps.LatLngLiteral[]): google.maps.LatLngLiteral {
    if (coordinates.length === 0) {
      return { lat: 0, lng: 0 };
    }

    // Calculate the centroid of the polygon
    let lat = 0;
    let lng = 0;

    coordinates.forEach(coord => {
      lat += coord.lat;
      lng += coord.lng;
    });

    return {
      lat: lat / coordinates.length,
      lng: lng / coordinates.length
    };
  }

  /**
   * Adds a facility name label to the map at the center of the polygon
   * @param center The center point of the polygon
   */
  addFacilityNameLabel(center: google.maps.LatLngLiteral) {
    // Remove any existing overlay
    if (this.facilityNameOverlay) {
      this.facilityNameOverlay.setMap(null);
      this.facilityNameOverlay = null;
    }

    // Create a custom overlay for the facility name
    class FacilityNameOverlay extends google.maps.OverlayView {
      private div: HTMLElement;
      private position: google.maps.LatLng;
      private text: string;
      private color: string;

      constructor(position: google.maps.LatLng, text: string, color: string, map: google.maps.Map) {
        super();
        this.position = position;
        this.text = text;
        this.color = color;
        this.setMap(map);
      }

      override onAdd() {
        // Create the label div
        const div = document.createElement('div');
        div.className = 'facility-name-label';
        div.style.position = 'absolute';
        div.style.backgroundColor = this.color;
        div.style.color = 'white';
        div.style.padding = '6px 10px';
        div.style.borderRadius = '4px';
        div.style.fontWeight = 'bold';
        div.style.fontSize = '14px';
        div.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.3)';
        div.style.zIndex = '1000';
        div.style.pointerEvents = 'none'; // Allow clicks to pass through to the map
        div.style.opacity = '0.9';
        div.style.textAlign = 'center';
        div.style.minWidth = '100px';
        div.style.whiteSpace = 'nowrap';
        div.innerHTML = this.text;

        // Add the label to the overlay's layer
        const panes = this.getPanes();
        panes.overlayLayer.appendChild(div);

        this.div = div;
      }

      override draw() {
        if (!this.div) return;

        // Position the label at the center of the facility
        const overlayProjection = this.getProjection();
        const position = overlayProjection.fromLatLngToDivPixel(this.position);

        // Center the label
        this.div.style.left = (position.x - (this.div.offsetWidth / 2)) + 'px';
        this.div.style.top = (position.y - (this.div.offsetHeight / 2)) + 'px';
      }

      override onRemove() {
        if (this.div) {
          this.div.parentNode.removeChild(this.div);
          delete this.div;
        }
      }
    }

    // Use the provided facility name or a default
    const displayName = this.facilityName || 'Facility Boundary';

    // Create the facility name overlay
    this.facilityNameOverlay = new FacilityNameOverlay(
      new google.maps.LatLng(center.lat, center.lng),
      displayName,
      '#3880ff', // Use the same color as the polygon
      this.map
    );
  }

  // Helper method to check if a point is inside the polygon
  isPointInPolygon(lat: number | null, lng: number | null): boolean {
    if (lat === null || lng === null) {
      return false;
    }

    if (!this.boundaryPolygon) {
      console.log('No boundary polygon defined, allowing any location');
      return true; // If no boundary, allow any location
    }

    try {
      const point = new google.maps.LatLng(lat, lng);

      // Debug information
      console.log('Checking if point is within boundary:', { lat, lng });

      // TEMPORARY FIX: Always return true for testing
      return true;

      /*
      // Check if the point is inside the polygon or on its edge
      const isInside = google.maps.geometry.poly.containsLocation(point, this.boundaryPolygon);
      const onEdge = google.maps.geometry.poly.isLocationOnEdge(point, this.boundaryPolygon, 1e-9);

      console.log('Point in polygon check results:', { isInside, onEdge });

      return isInside || onEdge;
      */
    } catch (error) {
      console.error('Error in isPointInPolygon:', error);
      return true; // Default to allowing the location if there's an error
    }
  }
}
