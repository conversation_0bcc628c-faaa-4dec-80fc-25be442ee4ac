/* Modal header styling */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #3f51b5;
  color: white;
  padding: 15px 20px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.modal-header .title {
  font-size: 18px;
  font-weight: 500;
}

.modal-header .close-button {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  margin: 0;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

/* Modal content styling */
.modal-content {
  padding: 0;
  padding-bottom: 70px; /* Add extra padding to prevent content from being hidden by footer */
  overflow-y: auto;
  max-height: calc(100vh - 130px); /* Account for header and footer */
}

/* Map container styling */
.map-container {
  width: 100%;
  height: 520px; /* Increased height by 40% from 300px */
  margin-bottom: 16px;
  border-radius: 0;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Map instructions styling */
.map-instructions {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 8px 12px;
  border-radius: 8px;
  position: absolute;
  top: 25px;
  left: 25px;
  z-index: 1000;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-width: 90%;
}

.map-instructions p {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 5px;
}

.map-instructions p + p {
  margin-top: 5px;
}

.map-instructions ion-icon {
  color: #3f51b5;
  font-size: 18px;
}

.map-instructions .boundary-note {
  font-size: 12px;
  color: #f44336;
}

.map-instructions .boundary-note ion-icon {
  color: #f44336;
}

/* Coordinates container styling */
.coordinates-container {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  margin-top: 10px;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.coordinate-field {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.coordinate-field label {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 4px;
  color: #555;
}

.coordinate-field input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
  background-color: white;
  height: 36px;
}

.coordinate-field input.has-value {
  border-color: #3880ff;
  background-color: rgba(56, 128, 255, 0.1);
  color: #3880ff;
  font-weight: bold;
}

/* Footer buttons styling */
.footer-buttons {
  display: flex;
  justify-content: flex-end;
  padding: 10px 15px;
  background-color: white;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 1000;
  box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.1);
  border-top: 1px solid #e0e0e0;
}

.cancel-button, .confirm-button {
  border: none;
  color: white;
  padding: 8px 20px;
  border-radius: 20px;
  font-weight: 500;
  cursor: pointer;
  margin-left: 10px;
  min-width: 80px;
}

.cancel-button {
  background-color: #f44336;
}

.confirm-button {
  background-color: #4caf50;
}

.confirm-button:disabled {
  background-color: #a5d6a7;
  cursor: not-allowed;
}

/* Boundary warning styling */
.boundary-warning {
  background-color: rgba(255, 193, 7, 0.1);
  border-left: 3px solid #f44336;
  padding: 10px 12px;
  border-radius: 4px;
  margin-bottom: 15px;
  font-size: 14px;
  color: #f44336;
  display: flex;
  align-items: center;
  gap: 8px;
}

.boundary-warning ion-icon {
  font-size: 18px;
}

/* Facility name label styling */
::ng-deep .facility-name-label {
  transition: all 0.2s ease-in-out;
  transform-origin: center;
  user-select: none;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-family: var(--ion-font-family, inherit);
}

/* Make facility name labels more visible on hover */
::ng-deep .facility-name-label:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}
