<div class="modal-header">
  <div class="title">Select Location</div>
  <button class="close-button" (click)="cancel()">✕</button>
</div>

<div class="modal-content">
  <div class="map-instructions">
    <p><ion-icon name="information-circle-outline"></ion-icon> Click on the map or drag the marker to set the location</p>
    <p *ngIf="boundaryPolygon" class="boundary-note"><ion-icon name="navigate-outline"></ion-icon> Location must be within the highlighted facility boundary</p>
  </div>

  <div #mapContainer id="map" class="map-container"></div>

  <div class="coordinates-container">
    <div class="coordinate-field">
      <label>Latitude</label>
      <input type="text" [value]="selectedLocation.latitude !== null ? selectedLocation.latitude : ''"
             placeholder="No location selected" readonly
             [ngClass]="{'has-value': selectedLocation.latitude !== null}">
    </div>

    <div class="coordinate-field">
      <label>Longitude</label>
      <input type="text" [value]="selectedLocation.longitude !== null ? selectedLocation.longitude : ''"
             placeholder="No location selected" readonly
             [ngClass]="{'has-value': selectedLocation.longitude !== null}">
    </div>
  </div>

  <!-- Warning message when location is outside boundary -->
  <div class="boundary-warning" *ngIf="(!isLocationWithinBoundary && boundaryPolygon) || showOutsideBoundaryWarning">
    <ion-icon name="warning-outline"></ion-icon>
    Location must be within the facility boundary
  </div>
</div>

<div class="footer-buttons">
  <button class="cancel-button" (click)="cancel()">Cancel</button>
  <button class="confirm-button"
          [disabled]="selectedLocation.latitude === null || selectedLocation.longitude === null"
          (click)="confirmLocation()">
    Confirm Location
  </button>
</div>
