// Main container for the stakeholder modal
.stakeholder-modal-container {
  height: 100vh;
  max-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// Content wrapper that takes remaining space and allows scrolling
.stakeholder-content-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
  margin: 0;

  // Ensure content sections have proper spacing
  > div {
    min-height: fit-content;
  }
}

ion-select {
  margin-bottom: 15px;
}

.headerFont {
  font-weight: 600;
}

.structureDropdownPadding {
  margin-left: 1%;
}

.structureDropdownLabel {
  color: #00629b;
  padding-bottom: 5px;
}

.contentRow {
  display: flex;
  align-items: center;
}

.main-grid {
  display: flex;
  flex-direction: column;
}

.displayCertificate {
  color: blue;
  text-decoration: underline;
}

.searchbar {
  padding-bottom: 0px;
  min-height: 52px;
  margin-bottom: 1px;
}

.selected {
  --background: #e8edf5;
  color: #00629b;

  ion-icon {
      color: #00629b;
  }
}
