<div class="stakeholder-modal-container">
  <ion-header>
    <ion-toolbar color="primary">
      <ion-title style="font-size: large;"> {{ editPartner ? 'Edit Partner Role' : 'Add New Partner Role' }}</ion-title>
      <ion-buttons slot="end" mode="ios" style="padding-right: 10px;">
        <ion-button (click)="cancel()">
          <ion-icon slot="icon-only" name="close-outline" style="font-size: 24px;padding-right: 5px;"></ion-icon>
        </ion-button>
      </ion-buttons>
    </ion-toolbar>

    <ion-toolbar>
      <ion-select style="margin: 10px 0px 10px 0px;" mode="md" label="Role" label-placement="fixed" fill="outline"
        interface="popover" [(ngModel)]="selectedRole" *ngIf="!editPartner" (ionChange)="roleChange()">
        <ion-select-option [value]="role" *ngFor="let role of rolesList">{{role |
          UppercaseToLowerCaseFilter}}</ion-select-option>
      </ion-select>

      <ion-item class="selected" mode="ios" id="open-modal" lines="full" *ngIf="editPartner">
        <ion-icon slot="start" name="ribbon-outline"></ion-icon>
        <ion-label>
          <h2>Role</h2>
          <p> {{ stakeData.APPR_TYPE ? stakeData.ROLE + ' - ' + stakeData.APPR_TYPE : stakeData.ROLE }}</p>
        </ion-label>
      </ion-item>

      <ion-searchbar placeholder="Search" class="searchbar" mode="ios" [(ngModel)]="searchTerm"
        (ionInput)="filterSkillsAndNamesBasedOnSearch()"></ion-searchbar>
    </ion-toolbar>
  </ion-header>

  <div class="stakeholder-content-wrapper">

<div *ngIf="!editPartner && !isMobile" style="--background: #f6f7f8;--offset-top: 0px !important;">
  <ion-grid class="main-grid" style="font-weight: bold;padding-bottom: 0px;">
    <ion-card mode="md" style="cursor: pointer;
      border-radius: 10px;
      box-shadow: none;
      border: 1px solid #dee3ea;">
      <ion-row class="headerFont" style="padding: 8px;">
        <ion-col size="2">User</ion-col>
        <ion-col size="2">Skills</ion-col>
        <ion-col size="2">Rating</ion-col>
        <ion-col size="2" style="text-align: center;">External</ion-col>
        <ion-col size="4">Certificate</ion-col>
      </ion-row>
    </ion-card>
  </ion-grid>
  <ion-grid class="main-grid" style="padding-top: 0px;">
    <ion-card (click)="selectUser(skillsData.user_id)" *ngFor="let skillsData of skillsData" mode="md" style="cursor: pointer;
      border-radius: 10px;
      box-shadow: none;
      border: 1px solid #dee3ea;
      margin-top: 5px;
      margin-bottom: 5px;">
      <ion-row class="contentRow" style="align-items: center !important;padding: 8px;">
        <!-- Username Column -->
        <ion-col size="2">{{ skillsData.username }}</ion-col>

        <!-- Skills Column -->
        <ion-col size="2">
          <ng-container *ngFor="let userSkill of skillsData.skills">
            <div>{{ userSkill.skill }}</div>
          </ng-container>
        </ion-col>

        <!-- Rating Column -->
        <ion-col size="2">
          <ng-container *ngFor="let userSkill of skillsData.skills">
            <div>
              <ng-container *ngFor="let i of getStarArray(userSkill.rating)">
                <ion-icon [name]="i === 'full' ? 'star' : (i === 'half' ? 'star-half' : 'star-outline')"
                  color="warning"></ion-icon>
              </ng-container>
            </div>
          </ng-container>
        </ion-col>
        <!-- External/Status Column -->
        <ion-col size="2" style="display: flex; justify-content: center; align-items: center;">
          <!-- <ion-icon *ngIf="skillsData.External === 'False'"  style="color: #ff3333; font-size: 25px;" name="close"></ion-icon> -->
          <ion-icon *ngIf="skillsData.External === 'True'" style="color: green; font-size: 25px; text-align: center;"
            name="checkmark"></ion-icon>
        </ion-col>

        <!-- Certificates Column -->
        <ion-col size="4">
          <ng-container *ngFor="let userSkill of skillsData.skills">
            <ng-container *ngFor="let image of userSkill.certificates ; index as j" style="text-overflow: ellipsis;">
              <div *ngIf="image?.docItem?.FILE_NAME">
                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'EXCEL'"
                  class="displayCertificate" src="../../../assets/images/excel.png"
                  (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                  {{ image.docItem.FILE_NAME }}
                </a>

                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'PPT'"
                  class="displayCertificate" src="../../../assets/images/powerpoint.png"
                  (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                  {{ image.docItem.FILE_NAME }}
                </a>
                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'WORD'"
                  class="displayCertificate" src="../../../assets/images/word.png"
                  (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                  {{ image.docItem.FILE_NAME }}
                </a>
                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'DOCUMENT'"
                  class="displayCertificate" src="../../../assets/images/word.png"
                  (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                  {{ image.docItem.FILE_NAME }}
                </a>
                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'PDF'"
                  class="displayCertificate" src="../../../assets/images/pdf.png"
                  (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                  {{ image.docItem.FILE_NAME }}
                </a>
                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'TEXT'"
                  class="displayCertificate" src="../../../assets/images/text.png"
                  (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                  {{ image.docItem.FILE_NAME }}
                </a>
                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'CSV'"
                  class="displayCertificate" src="../../../assets/images/csv.png"
                  (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                  {{ image.docItem.FILE_NAME }}
                </a>

                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'URL'"
                  class="displayCertificate" [href]="image.docItem.FILE_NAME" target="_blank" rel="noopener noreferrer"
                  (click)="$event.stopPropagation()">

                  {{ image.docItem.TITLE }}
                </a>


                <span *ngIf="image?.DOC_TYPE === 'IMAGE'" class="displayCertificate"
                  (click)="$event.stopPropagation(); image?.docItem ?  attachmentService.imageInFullscreen(image, 'Certificate Image: ', j) : attachmentService.editImage(image, j)">
                  {{ image?.docItem?.FILE_NAME }}
                </span>
              </div>
            </ng-container>
          </ng-container>
        </ion-col>
      </ion-row>
    </ion-card>
  </ion-grid>
</div>

<div *ngIf="editPartner && isMobile" style="--background: #f6f7f8;--offset-top: 0px !important;">
  <ion-card mode="md" style="cursor: pointer;
  border-radius: 10px;
  box-shadow: none;
  border: 1px solid #dee3ea;" *ngFor="let skillsData of skillsData" (click)="selectUser(skillsData.user_id)">
    <ion-grid>
      <ion-row>
        <ion-col size="4">User</ion-col>
        <ion-col size="8">
          <ion-note color="primary">{{skillsData.username}}</ion-note></ion-col>
      </ion-row>
      <ion-row>
        <ion-col size="4">Skills</ion-col>
        <ion-col size="8">
          <ng-container *ngFor="let userSkill of skillsData.skills">
            <ion-note color="primary">{{userSkill.skill}}</ion-note> <br>
          </ng-container>
        </ion-col>
      </ion-row>

      <ion-row>
        <ion-col size="4">Rating</ion-col>
        <ion-col size="8">
          <ng-container *ngFor="let userSkill of skillsData.skills">
            <div>
              <ng-container *ngFor="let i of getStarArray(userSkill.rating)">
                <ion-icon [name]="i === 'full' ? 'star' : (i === 'half' ? 'star-half' : 'star-outline')"
                  color="warning"></ion-icon>
              </ng-container>
            </div>
          </ng-container>
        </ion-col>
      </ion-row>

      <ion-row>
        <ion-col size="4">External</ion-col>
        <ion-col size="8">
          <ion-icon *ngIf="skillsData.External === 'True'" style="color: green; font-size: 25px;  text-align: center;"
            name="checkmark"></ion-icon>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col size="4">Certificate</ion-col>
        <ion-col size="8">
          <ng-container *ngFor="let userSkill of skillsData.skills">

            <ng-container *ngFor="let image of userSkill.certificates ; index as j">
              <div *ngIf="image?.docItem?.FILE_NAME">
                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'EXCEL'"
                  class="displayCertificate" src="../../../assets/images/excel.png"
                  (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                  <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                </a>

                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'PPT'"
                  class="displayCertificate" src="../../../assets/images/powerpoint.png"
                  (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                  <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                </a>

                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'WORD'"
                  class="displayCertificate" src="../../../assets/images/word.png"
                  (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                  <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                </a>

                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'DOCUMENT'"
                  class="displayCertificate" src="../../../assets/images/word.png"
                  (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                  <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                </a>

                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'PDF'"
                  class="displayCertificate" src="../../../assets/images/pdf.png"
                  (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                  <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                </a>

                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'TEXT'"
                  class="displayCertificate" src="../../../assets/images/text.png"
                  (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                  <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                </a>

                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'CSV'"
                  class="displayCertificate" src="../../../assets/images/csv.png"
                  (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                  <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                </a>

                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'URL'"
                  class="displayCertificate" [href]="image.docItem.FILE_NAME" target="_blank" rel="noopener noreferrer"
                  (click)="$event.stopPropagation()">
                  <span class="file-link"> {{ image.docItem.TITLE }}</span>
                </a>

                <span *ngIf="image?.DOC_TYPE === 'IMAGE'" class="displayCertificate file-link"
                  (click)="$event.stopPropagation(); image?.docItem ?  attachmentService.imageInFullscreen(image, 'Certificate Image: ', j) : attachmentService.editImage(image, j)">
                  {{ image?.docItem?.FILE_NAME }}
                </span>
              </div>

            </ng-container>
          </ng-container>
        </ion-col>
      </ion-row>
    </ion-grid>
  </ion-card>
</div>
  <div *ngIf="!editPartner && isMobile" style="--background: #f6f7f8;--offset-top: 0px !important;">

    <ion-card mode="md" style="cursor: pointer;
  border-radius: 10px;
  box-shadow: none;
  border: 1px solid #dee3ea;" *ngFor="let skillsData of skillsData" (click)="selectUser(skillsData.user_id)">
      <ion-grid>
        <ion-row>
          <ion-col size="4">User</ion-col>
          <ion-col size="8">
            <ion-note color="primary">{{skillsData.username}}</ion-note></ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="4">Skills</ion-col>
          <ion-col size="8">
            <ng-container *ngFor="let userSkill of skillsData.skills">
              <ion-note color="primary">{{userSkill.skill}}</ion-note> <br>
            </ng-container>
          </ion-col>
        </ion-row>

        <ion-row>
          <ion-col size="4">Rating</ion-col>
          <ion-col size="8">
            <ng-container *ngFor="let userSkill of skillsData.skills">
              <div>
                <ng-container *ngFor="let i of getStarArray(userSkill.rating)">
                  <ion-icon [name]="i === 'full' ? 'star' : (i === 'half' ? 'star-half' : 'star-outline')"
                    color="warning"></ion-icon>
                </ng-container>
              </div>
            </ng-container>
          </ion-col>
        </ion-row>

        <ion-row>
          <ion-col size="4">External</ion-col>
          <ion-col size="8">
            <ion-icon *ngIf="skillsData.External === 'True'" style="color: green; font-size: 25px;  text-align: center;"
              name="checkmark"></ion-icon>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="4">Certificate</ion-col>
          <ion-col size="8">
            <ng-container *ngFor="let userSkill of skillsData.skills">

              <ng-container *ngFor="let image of userSkill.certificates ; index as j">
                <div *ngIf="image?.docItem?.FILE_NAME">
                  <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'EXCEL'"
                    class="displayCertificate" src="../../../assets/images/excel.png"
                    (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                    <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                  </a>

                  <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'PPT'"
                    class="displayCertificate" src="../../../assets/images/powerpoint.png"
                    (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                    <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                  </a>

                  <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'WORD'"
                    class="displayCertificate" src="../../../assets/images/word.png"
                    (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                    <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                  </a>

                  <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'DOCUMENT'"
                    class="displayCertificate" src="../../../assets/images/word.png"
                    (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                    <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                  </a>

                  <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'PDF'"
                    class="displayCertificate" src="../../../assets/images/pdf.png"
                    (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                    <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                  </a>

                  <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'TEXT'"
                    class="displayCertificate" src="../../../assets/images/text.png"
                    (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                    <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                  </a>

                  <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'CSV'"
                    class="displayCertificate" src="../../../assets/images/csv.png"
                    (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                    <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                  </a>

                  <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'URL'"
                    class="displayCertificate" [href]="image.docItem.FILE_NAME" target="_blank"
                    rel="noopener noreferrer" (click)="$event.stopPropagation()">
                    <span class="file-link"> {{ image.docItem.TITLE }}</span>
                  </a>

                  <span *ngIf="image?.DOC_TYPE === 'IMAGE'" class="displayCertificate file-link"
                    (click)="$event.stopPropagation(); image?.docItem ?  attachmentService.imageInFullscreen(image, 'Certificate Image: ', j) : attachmentService.editImage(image, j)">
                    {{ image?.docItem?.FILE_NAME }}
                  </span>
                </div>

              </ng-container>
            </ng-container>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-card>

  </div>
  <div *ngIf="editPartner && !isMobile" style="--background: #f6f7f8;--offset-top: 0px !important;">
    <ion-grid class="main-grid" style="font-weight: bold;padding-bottom: 0px;">
      <ion-card mode="md" style="cursor: pointer;
        border-radius: 10px;
        box-shadow: none;
        border: 1px solid #dee3ea;">
        <ion-row class="headerFont" style="padding: 8px;">
          <ion-col size="2">User</ion-col>
          <ion-col size="2">Skills</ion-col>
          <ion-col size="2">Rating</ion-col>
          <ion-col size="2" style="text-align: center;">External</ion-col>
          <ion-col size="4">Certificate</ion-col>
        </ion-row>
      </ion-card>
    </ion-grid>


    <ion-grid class="main-grid" style="padding-top: 0px;">
      <ion-card (click)="selectUser(skillsData.user_id)" *ngFor="let skillsData of skillsData" mode="md" style="cursor: pointer;
      border-radius: 10px;
      box-shadow: none;
      border: 1px solid #dee3ea;
      margin-top: 5px;
      margin-bottom: 5px;">
        <ion-row class="contentRow" style="align-items: center !important;padding: 8px;">
          <!-- Username Column -->
          <ion-col size="2">{{ skillsData.username }}</ion-col>

          <!-- Skills Column -->
          <ion-col size="2">
            <ng-container *ngFor="let userSkill of skillsData.skills">
              <div>{{ userSkill.skill }}</div>
            </ng-container>
          </ion-col>

          <!-- Rating Column -->
          <ion-col size="2">
            <ng-container *ngFor="let userSkill of skillsData.skills">
              <div>
                <ng-container *ngFor="let i of getStarArray(userSkill.rating)">
                  <ion-icon [name]="i === 'full' ? 'star' : (i === 'half' ? 'star-half' : 'star-outline')"
                    color="warning"></ion-icon>
                </ng-container>
              </div>
            </ng-container>
          </ion-col>
          <!-- External/Status Column -->
          <ion-col size="2" style="display: flex; justify-content: center; align-items: center;">
            <!-- <ion-icon *ngIf="skillsData.External === 'False'"  style="color: #ff3333; font-size: 25px;" name="close"></ion-icon> -->
            <ion-icon *ngIf="skillsData.External === 'True'" style="color: green; font-size: 25px;  text-align: center;"
              name="checkmark"></ion-icon>
          </ion-col>

          <!-- Certificates Column -->
          <ion-col size="4">
            <ng-container *ngFor="let userSkill of skillsData.skills">

              <ng-container *ngFor="let image of userSkill.certificates ; index as j">
                <div *ngIf="image?.docItem?.FILE_NAME">
                  <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'EXCEL'"
                    class="displayCertificate" src="../../../assets/images/excel.png"
                    (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                    <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                  </a>

                  <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'PPT'"
                    class="displayCertificate" src="../../../assets/images/powerpoint.png"
                    (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                    <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                  </a>

                  <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'WORD'"
                    class="displayCertificate" src="../../../assets/images/word.png"
                    (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                    <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                  </a>

                  <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'DOCUMENT'"
                    class="displayCertificate" src="../../../assets/images/word.png"
                    (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                    <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                  </a>

                  <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'PDF'"
                    class="displayCertificate" src="../../../assets/images/pdf.png"
                    (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                    <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                  </a>

                  <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'TEXT'"
                    class="displayCertificate" src="../../../assets/images/text.png"
                    (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                    <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                  </a>

                  <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'CSV'"
                    class="displayCertificate" src="../../../assets/images/csv.png"
                    (click)="$event.stopPropagation(); attachmentService.downloadFile(image)">
                    <span class="file-link">{{ image.docItem.FILE_NAME }}</span>
                  </a>

                  <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'URL'"
                    class="displayCertificate" [href]="image.docItem.FILE_NAME" target="_blank"
                    rel="noopener noreferrer" (click)="$event.stopPropagation()">
                    <span class="file-link"> {{ image.docItem.TITLE }}</span>
                  </a>

                  <span *ngIf="image?.DOC_TYPE === 'IMAGE'" class="displayCertificate file-link"
                    (click)="$event.stopPropagation(); image?.docItem ?  attachmentService.imageInFullscreen(image, 'Certificate Image: ', j) : attachmentService.editImage(image, j)">
                    {{ image?.docItem?.FILE_NAME }}
                  </span>
                </div>

              </ng-container>
            </ng-container>
          </ion-col>
        </ion-row>
      </ion-card>
    </ion-grid>
  </div>

  </div> <!-- Close stakeholder-content-wrapper -->
</div> <!-- Close stakeholder-modal-container -->