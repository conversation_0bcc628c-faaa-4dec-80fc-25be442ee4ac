import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';

import { AddNewPermitStakeHolderComponent } from './add-new-permit-stake-holder.component';

describe('AddNewPermitStakeHolderComponent', () => {
  let component: AddNewPermitStakeHolderComponent;
  let fixture: ComponentFixture<AddNewPermitStakeHolderComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ AddNewPermitStakeHolderComponent ],
      imports: [IonicModule.forRoot()]
    }).compileComponents();

    fixture = TestBed.createComponent(AddNewPermitStakeHolderComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
