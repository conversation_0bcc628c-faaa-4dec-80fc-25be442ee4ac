<ion-header mode="ios">
  <ion-toolbar color="primary" mode="ios">
    <ion-title>Edit Agents</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="cancel()"> <ion-icon slot="icon-only" name="close"></ion-icon></ion-button>
    </ion-buttons>
  </ion-toolbar>
  <ion-progress-bar type="indeterminate" *ngIf="progressbar"></ion-progress-bar>
</ion-header>
<ion-content style="--padding-top: 0px; --padding-bottom: 16px; --padding-start: 16px; --padding-end: 16px;">
  <div style="padding-top: 16px;">
    <ion-select style="--padding-start: 16px !important;min-height: 43px;" interface="popover" fill="outline"
      [(ngModel)]="permit.AGENT_ID_INT" placeholder="Select Agent Internal" label-placement="stacked">
      <div slot="label">Select Agent Internal</div>
      <ion-select-option value="" class="selectClearOption">Select</ion-select-option>
      <ion-select-option *ngFor="let internal of agentsInternalList"
        [value]="internal.AGENT_ID">{{internal.NAME}}</ion-select-option>
    </ion-select>

    <ion-select style="--padding-start: 16px !important;min-height: 43px;margin-top: 16px;" interface="popover" fill="outline"
      [(ngModel)]="permit.AGENT_ID_EXT" label-placement="stacked" placeholder="Select Agent External">
      <div slot="label">Select Agent External</div>
      <ion-select-option value="" class="selectClearOption">Select</ion-select-option>
      <ion-select-option *ngFor="let external of agentsExternalList"
        [value]="external.AGENT_ID">{{external.NAME}}</ion-select-option>
    </ion-select>

    <div style="margin-top: 16px;text-align: center;" *ngIf="errorMessage.length > 0">
      <ion-text style="color: indianred;" class="textCenter">
        <span>{{errorMessage}}</span>
      </ion-text>
    </div>
  </div>
</ion-content>

<ion-footer mode="ios">
  <ion-toolbar mode="ios">
    <ion-button slot="end" color="danger" mode="md" (click)="cancel()">Cancel</ion-button>
    <ion-button slot="end" color="success" mode="md" [disabled]="!permit.AGENT_ID_INT && !permit.AGENT_ID_EXT" (click)="save()">Save</ion-button>
  </ion-toolbar>
</ion-footer>