import { Component, OnInit } from '@angular/core';
import { ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { ModalController } from '@ionic/angular';
import { AGENT_HEADER } from 'src/app/data-models/data_classes';
import * as moment from 'moment';
import { DataService } from 'src/app/services/data.service';

@Component({
  selector: 'app-edit-permit-agents',
  templateUrl: './edit-permit-agents.component.html',
  styleUrls: ['./edit-permit-agents.component.scss'],
})
export class EditPermitAgentsComponent implements OnInit {
  public errorMessage: string = '';
  public progressbar: boolean = false;
  public agentsExternalList: AGENT_HEADER[] = [];
  public agentsInternalList: AGENT_HEADER[] = [];
  public permit:any;

  constructor(public unviredSDK: UnviredCordovaSDK, public modalController: ModalController, public dataService: DataService) { }

  async ngOnInit() {
    await this.getAgentsInternal();
    await this.getAgentsExternal();
  }

  async getAgentsInternal() {
    let headerName = 'AGENT_HEADER';
    let getAgentsExternalQuery = `SELECT * FROM ${headerName} WHERE IS_INTERNAL = 'true' AND IS_ACTIVE = 'true' ORDER BY NAME ASC`;
    await this.unviredSDK
      .dbExecuteStatement(getAgentsExternalQuery)
      .then(async (result: any) => {
        if (result.type == ResultType.success) {
          if (result.data.length > 0) {
            this.agentsInternalList = result.data;
          }
        }
      });
  }

  async getAgentsExternal() {
    let headerName = 'AGENT_HEADER';
    let getAgentsExternalQuery = `SELECT * FROM ${headerName} WHERE IS_INTERNAL = 'false' AND IS_ACTIVE = 'true' ORDER BY NAME ASC`;
    await this.unviredSDK
      .dbExecuteStatement(getAgentsExternalQuery)
      .then(async (result: any) => {
        if (result.type == ResultType.success) {
          if (result.data.length > 0) {
            this.agentsExternalList = result.data;
          }
        }
      });
  }

  cancel() {
    this.modalController.dismiss(false, '', 'edit-permit-agenets')
  }

  async save() {
    this.progressbar = true;
    let permitUpdateQuery = `UPDATE PERMIT_HEADER SET P_MODE = 'M', OBJECT_STATUS = 2 WHERE PERMIT_NO = '${this.permit.PERMIT_NO}'`;
    let permitUpdateQueryResult = await this.unviredSDK.dbExecuteStatement(
      permitUpdateQuery
    );
    if (permitUpdateQueryResult.type == ResultType.success) {
      this.permit.P_MODE = 'M';
      this.permit.OBJECT_STATUS = 2;
    }
    let isAvailableButton = this.permit.hasOwnProperty('isShowDetailsButton');
    if (isAvailableButton) {
      delete this.permit.isShowDetailsButton;
    }
    let isShowReviseButton = this.permit.hasOwnProperty('isShowReviseButton');
    if (isShowReviseButton) {
      delete this.permit.isShowReviseButton;
    }
    // Remove permitTypeInfo as it's only used for display purposes and should not be sent to server
    let hasPermitTypeInfo = this.permit.hasOwnProperty('permitTypeInfo');
    if (hasPermitTypeInfo) {
      delete this.permit.permitTypeInfo;
    }
    this.permit.EXTENSION_DATE = moment(this.permit.EXTENSION_DATE).valueOf();
    this.permit.IS_EXTENDED = (this.permit.IS_EXTENDED == true || this.permit.IS_EXTENDED == 'true') ? 'true' : 'false';

    let permitHeaderResponse: any = await this.dataService.modifyPermit(
      this.permit
    );
    let infoMsg = this.dataService.handleInfoMessage(permitHeaderResponse);
    if (permitHeaderResponse.type == ResultType.success) {
      this.progressbar = false;
      if (infoMsg && infoMsg?.length > 0) {
        this.errorMessage = infoMsg;
      } else { this.modalController.dismiss(this.permit, '', 'edit-permit-agenets') }
    } else {
      this.progressbar = false;
      if (
        permitHeaderResponse.message &&
        permitHeaderResponse.message.length > 0
      ) {
        this.errorMessage =
          permitHeaderResponse.message;
      } else if (
        permitHeaderResponse.error &&
        permitHeaderResponse.error.length > 0
      ) {
        this.errorMessage =
          permitHeaderResponse.error;
      } else {
        this.errorMessage =
          'Error occured while updating permit, Please try again'
      }
    }
  }
}
