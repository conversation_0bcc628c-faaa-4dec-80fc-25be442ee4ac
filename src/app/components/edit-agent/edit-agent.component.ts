import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { Al<PERSON><PERSON>ontroller, ModalController, LoadingController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { AGENT_FACILITY, AGENT_HEADER } from 'src/app/data-models/data_classes';
import { DataService } from 'src/app/services/data.service';
import { BusyIndicatorService } from 'src/app/services/busy-indicator.service';

@Component({
  selector: 'app-edit-agent',
  templateUrl: './edit-agent.component.html',
  styleUrls: ['./edit-agent.component.scss'],
})
export class EditAgentComponent implements OnInit {
  @Input() agentsData: any;
  // @Input() facility: AGENT_FACILITY;
  public isUpdatingContent: boolean = false;
  public isUpdatingFacility: boolean = true;
  public isInternal: boolean = false;
  public isActive: boolean = false;
  public facilities: any;
  public selectedFacility: any = {};
  public modifiedFacilityHeader: any;
  public pageHeading: string;
  public displayError: string = '';
  public selectedFacilityArr = [];
  public defaultSelectedFacilities = [];
  public progressbar: boolean = false;
  public selectedFacilities: string[] = [];

  // Validation flags
  public emailValid: boolean = true;
  public phoneValid: boolean = true;
  public emailErrorMessage: string = '';
  public phoneErrorMessage: string = '';

  constructor(
    public modalCtrl: ModalController,
    private dataService: DataService,
    public translate: TranslateService,
    public alertController: AlertController,
    public unviredSDK: UnviredCordovaSDK,
    private loadingController: LoadingController,
    private busyIndicatorService: BusyIndicatorService,
    private cdr: ChangeDetectorRef
  ) {}

  async ngOnInit() {
    if (this.agentsData) {
      if (Object.keys(this.agentsData).length > 0) {
        this.isUpdatingContent = true;
        this.isInternal = this.agentsData.IS_INTERNAL == 'true' ? true : false;
        console.log('isInternal', this.isInternal);
        this.isActive = this.agentsData.IS_ACTIVE == 'true' ? true : false;
        console.log('isActive', this.isActive);
      }
    } else {
      this.agentsData = {};
      this.isActive = true;
      // this.isInternal = true;
      // this.facility = {} as AGENT_FACILITY;
    }
    this.pageHeading = this.isUpdatingContent ? 'Update Agent' : 'Add Agent';
    this.facilities = await this.dataService.getData('FACILITY_HEADER');
    console.log('Facility:', this.facilities);
    this.selectedFacility = this.agentsData.FACILITY_ID
      ? this.facilities.find(
        (facility) => facility.FACILITY_ID == this.agentsData.FACILITY_ID
      )
      : this.facilities[0];
    console.log('facility from agent', this.selectedFacility);
    this.isUpdatingFacility = this.agentsData.FACILITY_ID ? true : false;
    let selFaci = await this.dataService.getData(
      'AGENT_FACILITY',
      `AGENT_ID = '${this.agentsData?.AGENT_ID}'`
    );
    // Initialize selectedFacilities array
    this.selectedFacilities = [];

    for (let ff = 0; ff < selFaci?.length; ff++) {
      for (let ff1 = 0; ff1 < this.facilities.length; ff1++) {
        if (selFaci[ff].FACILITY_ID == this.facilities[ff1].FACILITY_ID) {
          this.facilities[ff1].isChecked = true;
          this.defaultSelectedFacilities.push(this.facilities[ff1]);
          // Add to selectedFacilities array for multi-select
          this.selectedFacilities.push(this.facilities[ff1].FACILITY_ID);
        }
      }
    }

    // Initial validation
    if (this.agentsData.EMAIL) {
      this.validateEmail();
    }

    if (this.agentsData.PHONE) {
      this.validatePhoneNumber();
    }
  }

  // Get facility name by ID
  getFacilityName(facilityId: string): string {
    const facility = this.facilities.find(f => f.FACILITY_ID === facilityId);
    return facility ? `${facility.FACILITY_ID} - ${facility.NAME}` : facilityId;
  }

  // Remove facility from selection
  removeFacility(facilityId: string) {
    this.selectedFacilities = this.selectedFacilities.filter(id => id !== facilityId);
    this.updateFacilityChecks();
  }

  // Update facility checks based on selected facilities
  updateFacilityChecks() {
    // Reset all facility checks
    this.facilities.forEach(facility => {
      facility.isChecked = false;
    });

    // Set isChecked based on selectedFacilities
    if (this.selectedFacilities && this.selectedFacilities.length > 0) {
      this.selectedFacilities.forEach(facilityId => {
        const facility = this.facilities.find(f => f.FACILITY_ID === facilityId);
        if (facility) {
          facility.isChecked = true;
        }
      });
    }
  }

  // Format phone number as xxx-xxx-xxxx
  formatPhoneNumber(event: any) {
    const input = event.target.value.replace(/\D/g, ''); // Remove all non-digits
    let formatted = '';

    if (input.length <= 3) {
      formatted = input;
    } else if (input.length <= 6) {
      formatted = `${input.slice(0, 3)}-${input.slice(3)}`;
    } else {
      formatted = `${input.slice(0, 3)}-${input.slice(3, 6)}-${input.slice(6, 10)}`;
    }

    // Update the model value with the formatted value
    this.agentsData.PHONE = formatted;

    // Validate phone number
    this.validatePhoneNumber();

    this.cdr.detectChanges();
  }

  // Validate email format
  validateEmail() {
    // Regular expression for email validation
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

    if (!this.agentsData.EMAIL) {
      this.emailValid = false;
      this.emailErrorMessage = 'Email is required';
    } else if (!emailRegex.test(this.agentsData.EMAIL)) {
      this.emailValid = false;
      this.emailErrorMessage = 'Please enter a valid email address';
    } else {
      this.emailValid = true;
      this.emailErrorMessage = '';
    }

    return this.emailValid;
  }

  // Validate phone number format
  validatePhoneNumber() {
    // Check if phone number is in the correct format (xxx-xxx-xxxx)
    const phoneRegex = /^\d{3}-\d{3}-\d{4}$/;

    if (!this.agentsData.PHONE) {
      this.phoneValid = false;
      this.phoneErrorMessage = 'Phone number is required';
    } else if (!phoneRegex.test(this.agentsData.PHONE) && this.agentsData.PHONE.length >= 12) {
      this.phoneValid = false;
      this.phoneErrorMessage = 'Please enter a valid phone number (xxx-xxx-xxxx)';
    } else if (this.agentsData.PHONE.length < 12) {
      this.phoneValid = false;
      this.phoneErrorMessage = 'Phone number must be 10 digits';
    } else {
      this.phoneValid = true;
      this.phoneErrorMessage = '';
    }

    return this.phoneValid;
  }

  async addOrUpdateAgent(selectedAgent) {
    try {
      this.displayError = '';

      // Validate email and phone before proceeding
      const isEmailValid = this.validateEmail();
      const isPhoneValid = this.validatePhoneNumber();

      if (!isEmailValid || !isPhoneValid) {
        this.displayError = 'Please correct the validation errors before saving.';
        return;
      }

      this.progressbar = true;

      // Update facility checks before getting selected facilities
      this.updateFacilityChecks();

      let selFacilities = this.facilities?.filter(
        (facility) => facility?.isChecked == true
      );
      let tempArr = [];

      if (selFacilities?.length > 0) {
        let AgentObject = {} as AGENT_HEADER;
        AgentObject.AGENT_ID = selectedAgent.AGENT_ID;
        AgentObject.NAME = selectedAgent.NAME;
        AgentObject.CONTACT = selectedAgent.CONTACT;
        AgentObject.PHONE = selectedAgent.PHONE;
        AgentObject.EMAIL = selectedAgent.EMAIL;
        AgentObject.ADDRESS = selectedAgent.ADDRESS;
        AgentObject.P_MODE = this.isUpdatingContent ? 'M' : 'A';
        AgentObject.OBJECT_STATUS = this.isUpdatingContent ? 2 : 1;
        AgentObject.IS_INTERNAL = this.isInternal.toString();
        AgentObject.IS_ACTIVE = this.isActive.toString();
        AgentObject.LID = this.isUpdatingContent ? selectedAgent.ALID : this.unviredSDK.guid().replace(/-/g, '');
        let updatedAgentDetails = { AGENT_HEADER: AgentObject };
        console.log('agent PA call data', updatedAgentDetails);

        await this.displayPleaseWaitLoader(
          this.translate.instant('Please wait, Agent data is being updated')
        );

        let res = await this.unviredSDK.dbInsertOrUpdate(
          'AGENT_HEADER',
          AgentObject,
          true
        );

        let selFacilities = this.facilities?.filter(
          (facility) => facility?.isChecked == true
        );
        let tempArr = [];

        if (selFacilities?.length > 0) {
          // selected facilities from list
          let included = this.defaultSelectedFacilities.filter(
            (item) => !selFacilities.includes(item)
          );
          let notIncluded = selFacilities.filter(
            (item) => !this.defaultSelectedFacilities.includes(item)
          );

          if (included?.length > 0) {
            for (let f = 0; f < included.length; f++) {
              this.modifiedFacilityHeader = {} as AGENT_FACILITY;
              this.modifiedFacilityHeader.P_MODE = 'D';
              this.modifiedFacilityHeader.OBJECT_STATUS = 2;
              this.modifiedFacilityHeader.AGENT_ID = selectedAgent.AGENT_ID;
              this.modifiedFacilityHeader.FACILITY_ID = included[f].FACILITY_ID;
              this.modifiedFacilityHeader.FID = AgentObject.LID;
              tempArr.push(this.modifiedFacilityHeader);
              await this.unviredSDK.dbInsertOrUpdate(
                'AGENT_FACILITY',
                this.modifiedFacilityHeader,
                false
              );
            }
          }

          if (notIncluded?.length > 0) {
            for (let f = 0; f < notIncluded.length; f++) {
              this.modifiedFacilityHeader = {} as AGENT_FACILITY;
              this.modifiedFacilityHeader.P_MODE = 'A';
              this.modifiedFacilityHeader.OBJECT_STATUS = 1;
              this.modifiedFacilityHeader.AGENT_ID = selectedAgent.AGENT_ID;
              this.modifiedFacilityHeader.FACILITY_ID = notIncluded[f].FACILITY_ID;
              this.modifiedFacilityHeader.FID = AgentObject.LID;
              tempArr.push(this.modifiedFacilityHeader);
              let res = await this.unviredSDK.dbInsertOrUpdate(
                'AGENT_FACILITY',
                this.modifiedFacilityHeader,
                false
              );
              console.log(res)
            }
          }

          let result: any = await this.dataService.modifyAgent(
            updatedAgentDetails,
            this.isUpdatingContent
          );
          console.log('agent update response at agent modal', result);
          if (result) {
            this.displayError = result;
          } else {
            this.closeModal();
          }
        }
      } else {
        this.presentAlert();
      }
    } catch (error) {
      console.error('Error updating agent:', error);
      this.displayError = 'An error occurred while updating the agent';
    } finally {
      this.progressbar = false;
      await this.busyIndicatorService.dismissBusyIndicator();
    }
  }

  async displayPleaseWaitLoader(message: string) {
    await this.busyIndicatorService.showBusyIndicator(message, 'crescent');
  }

  closeModal() {
    this.modalCtrl.dismiss();
  }

  async presentAlert() {
    const alert = await this.alertController.create({
      header: 'Alert',
      message: 'At-least one facility should be selected',
      buttons: ['Close'],
    });

    await alert.present();
  }
}
