import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { ModalController } from '@ionic/angular';

@Component({
  selector: 'app-add-permit-comments',
  templateUrl: './add-permit-comments.component.html',
  styleUrls: ['./add-permit-comments.component.scss'],
})
export class AddPermitCommentsComponent implements OnInit {
  comments: any;
  public errorMessage: string = '';
  public createForm: FormGroup;

  constructor(public modalController: ModalController) {
    this.createForm = new FormGroup({
      comments: new FormControl(this.comments),
    });
  }

  ngOnInit() { this.createForm.controls['comments'].setValue(this.comments)}

  cancel() { this.modalController.dismiss(false, '', 'edit-permit-comments') }

  save() { this.modalController.dismiss(this.createForm.controls['comments'].value, '', 'edit-permit-comments') }
}
