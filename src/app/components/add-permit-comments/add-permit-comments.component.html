<ion-header mode="ios">
  <ion-toolbar color="primary" mode="ios">
    <ion-title>Add Comments</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="cancel()"> <ion-icon slot="icon-only" name="close"></ion-icon></ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content style="--padding-top: 0px; --padding-bottom: 16px; --padding-start: 16px; --padding-end: 16px;">
  <div style="padding-top: 16px;">
    <form [formGroup]="createForm">
      <ion-textarea mode="md" labelPlacement="floating" fill="outline" label="Comments" placeholder="Enter Comments"
        [autoGrow]="true" formControlName="comments" autofocus rows="10" maxlength="1000">
      </ion-textarea>
    </form>
  </div>
</ion-content>

<ion-footer mode="ios">
  <ion-toolbar mode="ios">
    <ion-button slot="end" color="danger" mode="md" (click)="cancel()">Cancel</ion-button>
    <ion-button slot="end" color="success" mode="md" [disabled]="!createForm.valid" (click)="save()">Save</ion-button>
  </ion-toolbar>
</ion-footer>