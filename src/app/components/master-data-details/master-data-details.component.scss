ion-toolbar {
  --color: white;
}

ion-card {
  margin: 0 0 10px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

/* Form layout */
.form-container {
  display: flex;
  flex-direction: column;
  gap: 5px;
  max-width: 100%;
  padding: 15px;
}

.form-row {
  display: flex;
  gap: 20px;
  width: 100%;
  margin-bottom: 5px;
}

.form-group {
  flex: 1;
  min-width: 0;
}

/* Validation styling */
.validation-error {
  color: var(--ion-color-danger);
  font-size: 12px;
  margin-left: 16px;
  margin-bottom: 8px;
}

:host ::ng-deep ion-input.ion-invalid {
  --border-color: var(--ion-color-danger) !important;
}

:host ::ng-deep ion-input.ion-valid {
  --border-color: var(--ion-color-success) !important;
}

/* Floating label styling */
ion-item {
  --border-color: #ced4da;
  --border-radius: 4px;
  --background: transparent;
  margin-bottom: 5px;
}

ion-label {
  font-weight: 500;
  color: #00629b !important;
}

.error {
  text-align: center;
  color: #d63232;
  margin-top: 10px;
}

/* Toggle styling */
.toggle-item {
  --min-height: 35px;
  font-weight: 500;
  --background: rgba(63, 81, 181, 0.05);
  margin-bottom: 8px;
}

.toggle-label {
  font-size: 14px !important;
  color: #00629b !important;
}

/* Approval badges styling */
.approval-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
  margin-bottom: 10px;
}

.approval-badge {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.approval-badge ion-icon {
  margin-left: 5px;
  font-size: 14px;
  cursor: pointer;
}

/* Facility divisions styling */
.facility-divisions-div {
  max-height: 35vh;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  overflow-y: auto;
  width: 100%;
}

.margin-top-10 {
  margin-top: 0;
}

.ion-list-item {
  --min-height: 0px;
  height: 35px;
}

.selected {
  --background: rgba(63, 81, 181, 0.15);
  color: #3880ff;
}

.selected>i {
  color: white;
  font-weight: 540;
}

.sub-title-p-tag {
  padding-left: 8px;
  padding-top: 8px;
  margin-bottom: 1rem;
  color: #00629b;
  font-weight: 500;
}

/* Legacy styles kept for compatibility */
.margin-bottom {
  margin-bottom: 2%;
}

.structureDropdownLabel {
  color: #00629b;
  padding-bottom: 10px;
}

.spacer {
  padding-top: 1px !important;
}

/* Footer styling */
ion-footer {
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

ion-footer ion-button {
  margin-right: 8px;
}

/* Content padding to avoid footer overlap */
ion-content {
  --padding-bottom: 60px;
}