ion-toolbar {
    padding-inline: 10px;
    padding-top: 0px;
    padding-bottom: 0px;
    font-size: 1.1rem;
    font-weight: 600;
    letter-spacing: 0.0125em;
  
  }
  
  .refreshButton {
    // --border-color: #868686 !important;
    --border-width: 1px !important;
    --border-radius: 8px !important;
    font-weight: 600 !important;
  }

  .date-control {
    --border-color: var(--ion-color-step-300, #b3b3b3);
    --border-radius: 4px;
    --border-width: 1px;
    --min-height: 43px;
  }

  ion-col {
    margin-top: 10px;
  }

  .textCenter {
    text-align: center;
}

.date-popover-content {
  width: fit-content !important;
}

/* Angular DateTime Picker Custom Styling */
:host ::ng-deep {
  .owl-dt-popup {
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }

  .owl-dt-container {
    border-radius: 8px;
  }

  .owl-dt-calendar-table .owl-dt-calendar-cell {
    border-radius: 4px;
  }

  .owl-dt-calendar-table .owl-dt-calendar-cell.owl-dt-calendar-cell-selected {
    background-color: var(--ion-color-primary);
    color: white;
  }

  .owl-dt-calendar-table .owl-dt-calendar-cell.owl-dt-calendar-cell-today {
    border: 2px solid var(--ion-color-primary);
  }

  .owl-dt-timer-box {
    border-radius: 4px;
    border: 1px solid var(--ion-color-step-300, #b3b3b3);
  }

  .owl-dt-control-button {
    background-color: var(--ion-color-primary);
    color: white;
    border-radius: 4px;
    border: none;
    padding: 8px 16px;
    font-weight: 500;
  }

  .owl-dt-control-button:hover {
    background-color: var(--ion-color-primary-shade);
  }

  .owl-dt-container-buttons {
    border-top: 1px solid var(--ion-color-step-200, #e0e0e0);
    padding: 12px 16px;
  }
}