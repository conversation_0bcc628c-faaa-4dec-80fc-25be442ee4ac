import { Component, OnInit } from '@angular/core';
import * as markerjs2 from 'markerjs2';
import { ModalController } from '@ionic/angular';

@Component({
  selector: 'app-annotate-image',
  templateUrl: './annotate-image.component.html',
  styleUrls: ['./annotate-image.component.scss'],
})
export class AnnotateImageComponent implements OnInit {

  public image: any;

  constructor(private modalcontroller: ModalController) { }

  ngOnInit() {}

  ionViewDidEnter() {
    this.editImage();
  }

  annotateImages(img) {
    new markerjs2.Activator.addKey('MJS2-P013-M397-4100');
    let markerArea = new markerjs2.MarkerArea(img);
    markerArea.uiStyleSettings.toolbarBackgroundColor = '#303f9f';
    markerArea.uiStyleSettings.toolbarBackgroundHoverColor = '#111f7e';
    markerArea.addRenderEventListener((imgURL) => {
      this.image = imgURL;
      this.modalcontroller.dismiss(this.image, '', 'annotateImage');
    });
    markerArea.addCloseEventListener(() => {
      this.modalcontroller.dismiss(false, '', 'annotateImage');
    });
    markerArea.show();
  }

  editImage() {
    let img = document.getElementsByClassName("imagefull-display");
    this.annotateImages(img[0]);
  }

}
