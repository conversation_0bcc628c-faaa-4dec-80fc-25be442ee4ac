import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import {
  AlertController,
  LoadingController,
  ModalController,
  NavController,
  Platform,
} from '@ionic/angular';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-image-full-screen',
  templateUrl: './image-full-screen.component.html',
  styleUrls: ['./image-full-screen.component.scss'],
})
export class ImageFullScreenComponent implements OnInit, OnDestroy {
  sliderOptions = {
    zoom: {
      maxRatio: 3,
    },
  };

  public imagePath: any;
  public imageName: any;
  unregisterBackAction;
  private subscriptions = new Subscription();

  constructor(
    private modalcontroller: ModalController,
    private platform: Platform,
    public router: Router,
    public navCtrl: NavController,
    public alertController: AlertController,
    public loadingController: LoadingController
  ) {}
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  ngOnInit() {
    let subs = this.platform.backButton.subscribeWithPriority(999, async () => {
      if (this.router.url === '/permits') {
        if (this.modalcontroller.getTop()) {
          const modal = await this.modalcontroller.getTop();
          if (modal) {
            this.modalcontroller.dismiss();
            return;
          }
          const dialog = await this.alertController.getTop();
          if (dialog) {
            dialog.dismiss();
            return;
          }
          const loader = await this.loadingController.getTop();
          if (loader) {
            loader.dismiss();
            return;
          } else {
            this.navCtrl.pop();
          }
        }
      } else if(this.router.url == '/users'){

          if (this.modalcontroller.getTop()) {
            const modal = await this.modalcontroller.getTop();
            if (modal) {
              this.modalcontroller.dismiss();
              return;
            }
            const dialog = await this.alertController.getTop();
            if (dialog) {
              dialog.dismiss();
              return;
            }
            const loader = await this.loadingController.getTop();
            if (loader) {
              loader.dismiss();
              return;
            } else {
              this.navCtrl.pop();
            }
          }
        
      }
    });
    this.subscriptions.add(subs);
  }

  closeModal() {
    this.modalcontroller.dismiss();
  }
}
