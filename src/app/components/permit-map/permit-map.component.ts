import { Component, OnInit, Input, OnChanges, SimpleChanges, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { PermitDetailsComponent } from '../permit-details/permit-details.component';
import { DataService } from 'src/app/services/data.service';

interface PermitMarker {
  position: google.maps.LatLngLiteral;
  title: string;
  permitNo: string;
  status: string;
  description: string;
  permitType: string;
  permitTypeIcon?: string; // Font Awesome icon for permit type
  permitTypeColor?: string; // Color for permit type
  startDate: string;
  endDate: string;
  requestedBy: string;
  permitData: any; // Full permit data for details view
  facilityBoundary?: string; // Facility boundary in WKT format
  facilityId?: string; // Facility ID
  facilityName?: string; // Facility name for display
  markerElement?: any; // Reference to the marker element (can be either AdvancedMarkerElement or Marker)
  boundaryPolygon?: google.maps.Polygon; // Reference to the facility boundary polygon
}

@Component({
  selector: 'app-permit-map',
  templateUrl: './permit-map.component.html',
  styleUrls: ['./permit-map.component.scss'],
})
export class PermitMapComponent implements OnInit, OnChanges, AfterViewInit {
  @ViewChild('mapContainer', { static: false }) mapContainer: ElementRef;
  @Input() permits: any[] = [];
  @Input() activeFilters: string[] = [];

  map: google.maps.Map;
  markers: any[] = []; // Can be either AdvancedMarkerElement or Marker
  infoWindow: google.maps.InfoWindow;
  mapInitialized = false;
  permitMarkers: PermitMarker[] = [];
  useAdvancedMarkers = false; // Flag to check if Advanced Markers are available
  facilityPolygons: google.maps.Polygon[] = []; // Array to store facility boundary polygons

  // Default center (can be updated based on user's location or first permit)
  center: google.maps.LatLngLiteral = { lat: 0, lng: 0 };

  // Map styling options
  mapOptions: google.maps.MapOptions = {
    zoom: 15, // Default zoom level (will be adjusted based on markers)
    maxZoom: 20,
    minZoom: 3,
    mapTypeId: google.maps.MapTypeId.ROADMAP,
    mapTypeControl: false, // We'll add our own custom layer control
    mapTypeControlOptions: {
      position: google.maps.ControlPosition.TOP_RIGHT
    },
    streetViewControl: true,
    fullscreenControl: true,
    zoomControl: true,
    gestureHandling: 'cooperative',
    clickableIcons: false,
    disableDefaultUI: false
  };

  // Available map types for the layer control
  mapTypes = [
    { id: google.maps.MapTypeId.ROADMAP, name: 'Map', icon: 'map' },
    { id: google.maps.MapTypeId.SATELLITE, name: 'Satellite', icon: 'satellite' },
    { id: google.maps.MapTypeId.HYBRID, name: 'Hybrid', icon: 'layers' },
    { id: google.maps.MapTypeId.TERRAIN, name: 'Terrain', icon: 'mountain' }
  ];

  // Map tools
  mapTools = [
    { id: 'measure', name: 'Measure distance', icon: 'ruler' }
  ];

  // Measurement mode state
  isMeasuring = false;
  measurementPath: google.maps.Polyline | null = null;
  measurementMarkers: google.maps.Marker[] = [];
  measurementPoints: google.maps.LatLng[] = [];

  constructor(
    private modalController: ModalController,
    private dataService: DataService
  ) {
    this.infoWindow = new google.maps.InfoWindow();
  }

  ngOnInit() {
    // Initial setup
  }

  ngAfterViewInit() {
    // Initialize the map after the view is ready
    this.initMap();
  }

  ngOnChanges(changes: SimpleChanges) {
    // When permits or filters change, update the markers
    if ((changes['permits'] || changes['activeFilters']) && this.mapInitialized) {
      this.updateMarkers();
    }
  }

  initMap() {
    if (!this.mapContainer) {
      console.error('Map container not found');
      return;
    }

    console.log('Initializing map');

    // Set a default center if none is provided
    if (this.center.lat === 0 && this.center.lng === 0) {
      // Default to a reasonable location (e.g., center of US)
      this.center = { lat: 37.0902, lng: -95.7129 };
    }

    // Create the map
    try {
      this.map = new google.maps.Map(
        this.mapContainer.nativeElement,
        {
          ...this.mapOptions,
          center: this.center
        }
      );

      console.log('Map created successfully');

      // Add a listener to know when the map is fully loaded
      google.maps.event.addListenerOnce(this.map, 'idle', () => {
        console.log('Map is fully loaded and ready');
      });

      // Add listener for zoom changes to ensure markers remain visible
      google.maps.event.addListener(this.map, 'zoom_changed', () => {
        const currentZoom = this.map.getZoom();
        console.log(`Map zoom changed to: ${currentZoom}`);

        // If markers disappear at certain zoom levels, we can adjust their size here
        if (currentZoom < 10) {
          // Make markers larger at lower zoom levels
          this.adjustMarkerSize(48);
          this.adjustFacilityLabelSize('16px');
        } else if (currentZoom < 15) {
          // Medium size for medium zoom levels
          this.adjustMarkerSize(36);
          this.adjustFacilityLabelSize('14px');
        } else {
          // Normal size for high zoom levels
          this.adjustMarkerSize(24);
          this.adjustFacilityLabelSize('12px');
        }
      });

      // Force using regular markers for now to avoid deprecation warnings
      this.useAdvancedMarkers = false;
      console.log('Using regular markers (Advanced Markers disabled)');

      // Add custom layer control
      this.addLayerControl();

      this.mapInitialized = true;
      this.updateMarkers();
    } catch (error) {
      console.error('Error initializing map:', error);
    }
  }

  async updateMarkers() {
    // Clear existing markers and polygons
    this.clearMarkers();
    this.permitMarkers = [];

    if (!this.permits || this.permits.length === 0) {
      console.log('No permits to display on map');
      return;
    }

    console.log(`Processing ${this.permits.length} permits for map display`);

    // Process permits to create markers
    for (const permit of this.permits) {
      let position = null;
      let facilityBoundary = null;

      // Get structure details to find lat/long from TAG field
      // The PERMIT_HEADER->TAG field is a reference to STRUCTURE_HEADER->TAG
      console.log(`Looking up structure details for permit ${permit.PERMIT_NO} with TAG: ${permit.TAG}`);
      const structureDetails = await this.getStructureDetails(permit.TAG);

      if (structureDetails && structureDetails.LATITUDE && structureDetails.LONGITUDE) {
        console.log(`Found coordinates for permit ${permit.PERMIT_NO}: lat=${structureDetails.LATITUDE}, lng=${structureDetails.LONGITUDE}`);

        // Ensure coordinates are numbers
        const lat = typeof structureDetails.LATITUDE === 'number'
          ? structureDetails.LATITUDE
          : parseFloat(structureDetails.LATITUDE);

        const lng = typeof structureDetails.LONGITUDE === 'number'
          ? structureDetails.LONGITUDE
          : parseFloat(structureDetails.LONGITUDE);

        if (!isNaN(lat) && !isNaN(lng)) {
          position = {
            lat: lat,
            lng: lng
          };
          console.log(`Converted coordinates for permit ${permit.PERMIT_NO}: lat=${lat}, lng=${lng}`);
        } else {
          console.error(`Invalid coordinates for permit ${permit.PERMIT_NO}: lat=${structureDetails.LATITUDE}, lng=${structureDetails.LONGITUDE}`);
        }
      } else {
        console.log(`No coordinates found for permit ${permit.PERMIT_NO} with TAG: ${permit.TAG}`);
      }

      // Get facility boundary regardless of whether we have a position from structure
      if (permit.FACILITY_ID) {
        try {
          const facilityQuery = `SELECT FACILITY_ID, BOUNDARY, NAME FROM FACILITY_HEADER WHERE FACILITY_ID = '${permit.FACILITY_ID}'`;
          console.log(`Executing facility query: ${facilityQuery}`);
          const facilityResult = await this.dataService.executeQuery(facilityQuery);

          console.log(`Facility query result for ${permit.FACILITY_ID}:`, facilityResult);

          if (facilityResult && facilityResult.length > 0) {
            // Store the facility boundary for drawing later
            if (facilityResult[0].BOUNDARY) {
              facilityBoundary = facilityResult[0].BOUNDARY;
              // Store the facility name for display
              permit.FACILITY_NAME = facilityResult[0].NAME || permit.FACILITY_ID;
              console.log(`Found boundary for facility ${permit.FACILITY_ID} (${permit.FACILITY_NAME}):`, facilityBoundary);
            } else {
              console.log(`No boundary found for facility ${permit.FACILITY_ID}`);
              // Still store the facility name even if no boundary
              permit.FACILITY_NAME = facilityResult[0].NAME || permit.FACILITY_ID;
            }

            // If we don't have a position yet, use the facility center
            if (!position && facilityBoundary) {
              // Use the wktToCoordinates method to parse the boundary
              const coordinates = this.wktToCoordinates(facilityBoundary);

              if (coordinates.length > 0) {
                // Calculate center of polygon as average of all points
                let totalLat = 0;
                let totalLng = 0;

                coordinates.forEach(coord => {
                  totalLat += coord.lat;
                  totalLng += coord.lng;
                });

                position = {
                  lat: totalLat / coordinates.length,
                  lng: totalLng / coordinates.length
                };

                console.log(`Calculated facility center for permit ${permit.PERMIT_NO}:`, position);
              }
            }
          }
        } catch (error) {
          console.error('Error getting facility boundary:', error);
        }
      }

      // Get permit type information
      let permitTypeInfo = null;
      try {
        permitTypeInfo = await this.getPermitTypeInfo(permit.PERMIT_TYPE);
        console.log(`Permit type info for ${permit.PERMIT_TYPE}:`, permitTypeInfo);
      } catch (error) {
        console.error(`Error getting permit type info for ${permit.PERMIT_TYPE}:`, error);
      }

      // If we found a valid position, create a marker
      if (position && !isNaN(position.lat) && !isNaN(position.lng)) {
        const marker: PermitMarker = {
          position: position,
          title: permit.PERMIT_NO,
          permitNo: permit.PERMIT_NO,
          status: permit.STATUS,
          description: permit.DESCRIPTION,
          permitType: permit.PERMIT_TYPE,
          permitTypeIcon: permitTypeInfo?.ICON || 'question-circle',
          permitTypeColor: this.getColorHex(permitTypeInfo?.COLOR, permit.PERMIT_TYPE),
          startDate: permit.FORMATTED_START_DATE || permit.PERMIT_DATE,
          endDate: permit.FORMATTED_END_DATE || permit.EXPIRY_DATE,
          requestedBy: permit.REQUESTED_BY,
          permitData: permit,
          facilityBoundary: facilityBoundary,
          facilityId: permit.FACILITY_ID,
          facilityName: permit.FACILITY_NAME || permit.FACILITY_ID
        };

        this.permitMarkers.push(marker);
      }
    }

    // If we have markers, fit the map to show all markers and facility boundaries
    if (this.permitMarkers.length > 0) {
      // Create bounds object to fit all markers and facility boundaries
      const bounds = new google.maps.LatLngBounds();

      // Track if we have any facility boundaries
      let hasFacilityBoundaries = false;

      // First, add all facility boundary coordinates to the bounds
      this.permitMarkers.forEach(markerInfo => {
        if (markerInfo.facilityBoundary) {
          hasFacilityBoundaries = true;
          const coordinates = this.wktToCoordinates(markerInfo.facilityBoundary);
          coordinates.forEach(coord => {
            bounds.extend(coord);
          });
        }
      });

      // If no facility boundaries, use marker positions
      if (!hasFacilityBoundaries) {
        this.permitMarkers.forEach(markerInfo => {
          bounds.extend(markerInfo.position);
        });
      }

      // Center the map to fit all boundaries/markers
      this.map.fitBounds(bounds);

      // Add padding to ensure facilities are fully visible with good margins
      google.maps.event.addListenerOnce(this.map, 'bounds_changed', () => {
        // Get the current zoom level
        const currentZoom = this.map.getZoom();

        // Zoom out slightly to add margin around the facilities
        if (currentZoom) {
          // Zoom out by 1 level to add margin
          this.map.setZoom(currentZoom - 1);
        }

        console.log(`Adjusted zoom level to provide margin around facilities: ${this.map.getZoom()}`);
      });

      // If we only have one marker and no facility boundary, set an appropriate zoom level
      if (this.permitMarkers.length === 1 && !hasFacilityBoundaries) {
        this.center = this.permitMarkers[0].position;
        this.map.setCenter(this.center);
        this.map.setZoom(15); // Higher zoom level for single marker
      }

      console.log(`Map centered to fit ${this.permitMarkers.length} markers and their facility boundaries`);
    }

    // Create the markers on the map
    this.createMarkers();
  }

  createMarkers() {
    console.log(`Creating markers for ${this.permitMarkers.length} permits`);

    // Force using regular markers only
    this.useAdvancedMarkers = false;

    // First, draw all facility boundaries
    this.drawFacilityBoundaries();

    this.permitMarkers.forEach((markerInfo, index) => {
      console.log(`Creating marker ${index + 1}/${this.permitMarkers.length} at position:`, markerInfo.position);

      let marker: any; // Will be a regular Marker

      try {
        // Create a custom SVG with a pin shape using the permit type's color
        const markerSvg = `
          <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64">
            <path d="M32 2 C18 2 8 12 8 26 C8 40 32 62 32 62 C32 62 56 40 56 26 C56 12 46 2 32 2 Z"
                  fill="${markerInfo.permitTypeColor}"
                  stroke="white"
                  stroke-width="2"/>
            <circle cx="32" cy="26" r="10" fill="white"/>
          </svg>
        `;

        // Create a data URL from the SVG
        const svgUrl = 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(markerSvg);

        marker = new google.maps.Marker({
          position: markerInfo.position,
          map: this.map,
          title: markerInfo.title,
          animation: google.maps.Animation.DROP,

          // Use the SVG icon with a larger size for better visibility
          icon: {
            url: svgUrl,
            scaledSize: new google.maps.Size(64, 64),
            anchor: new google.maps.Point(32, 62) // Bottom center of the pin
          },

          // Make sure the marker stays visible at all zoom levels
          optimized: false,
          zIndex: 999
        });

        // Add click listener to show info window
        marker.addListener('click', () => {
          this.showInfoWindowLegacy(marker, markerInfo);
        });

        console.log(`Successfully created marker ${index + 1} with permit type ${markerInfo.permitType}`);
      } catch (error) {
        console.error(`Error creating marker ${index + 1}:`, error);
      }

      if (marker) {
        // Store reference to the marker in the markerInfo
        markerInfo.markerElement = marker;
        this.markers.push(marker);
      }
    });
  }

  /**
   * Draw facility boundaries on the map
   */
  drawFacilityBoundaries() {
    // Clear any existing polygons first
    if (this.facilityPolygons) {
      this.facilityPolygons.forEach(polygon => {
        polygon.setMap(null);
      });
      this.facilityPolygons = [];
    } else {
      this.facilityPolygons = [];
    }

    // Draw boundaries for each UNIQUE facility
    console.log('Drawing facility boundaries for permits');

    // Create a map to store unique facilities and their boundaries
    let uniqueFacilities = new Map<string, {
      boundary: string,
      permitNos: string[],
      permitTypeColors: string[],
      facilityName?: string // Add facility name property
    }>();

    // Create a map of facility IDs to colors to ensure different facilities have different colors
    const facilityColors = new Map<string, string>();
    const predefinedColors = [
      '#3880ff', // Blue
      '#2dd36f', // Green
      '#eb445a', // Red
      '#ffce00', // Yellow
      '#92949c', // Gray
      '#3dc2ff', // Light Blue
      '#5260ff', // Indigo
      '#f46b45', // Orange
      '#8b5cf6', // Purple
      '#10b981', // Emerald
      '#6366f1', // Violet
      '#f59e0b'  // Amber
    ];

    // Collect all unique facilities
    this.permitMarkers.forEach(markerInfo => {
      if (markerInfo.facilityId && markerInfo.facilityBoundary) {
        if (!uniqueFacilities.has(markerInfo.facilityId)) {
          // First time seeing this facility
          uniqueFacilities.set(markerInfo.facilityId, {
            boundary: markerInfo.facilityBoundary,
            permitNos: [markerInfo.permitNo],
            permitTypeColors: [markerInfo.permitTypeColor],
            facilityName: markerInfo.facilityName || markerInfo.facilityId
          });
        } else {
          // Already seen this facility, just add the permit number
          const facility = uniqueFacilities.get(markerInfo.facilityId);
          facility.permitNos.push(markerInfo.permitNo);
          facility.permitTypeColors.push(markerInfo.permitTypeColor);
        }
      }
    });

    console.log(`Found ${uniqueFacilities.size} unique facilities`);

    // Assign colors to unique facility IDs
    let colorIndex = 0;
    uniqueFacilities.forEach((facility, facilityId) => {
      facilityColors.set(facilityId, predefinedColors[colorIndex % predefinedColors.length]);
      colorIndex++;
    });

    // Draw each unique facility boundary only once
    uniqueFacilities.forEach((facility, facilityId) => {
      console.log(`Processing facility ${facilityId} with ${facility.permitNos.length} permits:`, facility.permitNos.join(', '));
      try {
        // Convert WKT to polygon coordinates
        const coordinates = this.wktToCoordinates(facility.boundary);

        if (coordinates.length > 0) {
          // Get the color for this facility
          const facilityColor = facilityColors.get(facilityId) || '#3880ff';

          // Create polygon for the facility boundary with more visible styling
          const polygon = new google.maps.Polygon({
            paths: coordinates,
            strokeColor: facilityColor,
            strokeOpacity: 0.8, // Semi-transparent stroke
            strokeWeight: 2, // Moderate stroke weight
            fillColor: facilityColor,
            fillOpacity: 0.2, // More transparent fill
            map: this.map,
            zIndex: 1, // Below markers
            clickable: false // Disable clicking on facility areas
          });

          // Add the polygon to our array for tracking
          this.facilityPolygons.push(polygon);

          // Calculate center of polygon for facility name label
          let totalLat = 0;
          let totalLng = 0;
          coordinates.forEach(coord => {
            totalLat += coord.lat;
            totalLng += coord.lng;
          });
          const center = {
            lat: totalLat / coordinates.length,
            lng: totalLng / coordinates.length
          };

          // Create a facility name label using a custom overlay
          // Create a custom label class that extends OverlayView
          class FacilityNameOverlay extends google.maps.OverlayView {
            private div: HTMLElement;
            private position: google.maps.LatLng;
            private text: string;
            private color: string;

            constructor(position: google.maps.LatLng, text: string, color: string, map: google.maps.Map) {
              super();
              this.position = position;
              this.text = text;
              this.color = color;
              this.setMap(map);
            }

            override onAdd() {
              // Create the label div
              const div = document.createElement('div');
              div.className = 'facility-name-label';
              div.style.position = 'absolute';
              div.style.backgroundColor = this.color;
              div.style.color = 'white';
              div.style.padding = '6px 10px';
              div.style.borderRadius = '4px';
              div.style.fontWeight = 'bold';
              div.style.fontSize = '14px';
              div.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.3)';
              div.style.zIndex = '1000';
              div.style.pointerEvents = 'none'; // Allow clicks to pass through to the map
              div.style.opacity = '0.9';
              div.style.textAlign = 'center';
              div.style.minWidth = '100px';
              div.style.whiteSpace = 'nowrap';
              div.innerHTML = this.text;

              // Add the label to the overlay's layer
              const panes = this.getPanes();
              panes.overlayLayer.appendChild(div);

              this.div = div;
            }

            override draw() {
              if (!this.div) return;

              // Position the label at the center of the facility
              const overlayProjection = this.getProjection();
              const position = overlayProjection.fromLatLngToDivPixel(this.position);

              // Center the label
              this.div.style.left = (position.x - (this.div.offsetWidth / 2)) + 'px';
              this.div.style.top = (position.y - (this.div.offsetHeight / 2)) + 'px';
            }

            override onRemove() {
              if (this.div) {
                this.div.parentNode.removeChild(this.div);
                delete this.div;
              }
            }
          }

          // Create the facility name overlay
          new FacilityNameOverlay(
            new google.maps.LatLng(center.lat, center.lng),
            facility.facilityName || facilityId,
            facilityColor,
            this.map
          );



          console.log(`Drew facility boundary for facility ${facilityId} with ${facility.permitNos.length} permits`);
        }
      } catch (error) {
        console.error(`Error drawing facility boundary for facility ${facilityId}:`, error);
      }
    });
  }

  /**
   * Convert WKT polygon string to Google Maps coordinates
   * @param wkt WKT polygon string (POLYGON((lng lat, lng lat, ...)))
   * @returns Array of LatLngLiteral coordinates
   */
  private wktToCoordinates(wkt: string): google.maps.LatLngLiteral[] {
    if (!wkt) {
      console.error('Empty WKT string');
      return [];
    }

    // Handle different possible WKT formats
    let coordsString = '';

    if (wkt.startsWith('POLYGON((') && wkt.endsWith('))')) {
      coordsString = wkt.substring(9, wkt.length - 2);
    } else if (wkt.startsWith('POLYGON ((') && wkt.endsWith('))')) {
      coordsString = wkt.substring(10, wkt.length - 2);
    } else if (wkt.startsWith('POLYGON(') && wkt.endsWith(')')) {
      coordsString = wkt.substring(8, wkt.length - 1);
    } else {
      console.error('Unrecognized WKT format:', wkt);
      return [];
    }

    try {
      // Extract coordinates from WKT string
      const coordPairs = coordsString.split(',').map(pair => pair.trim());

      console.log('Parsed coordinate pairs:', coordPairs);

      // Convert to LatLngLiteral objects
      // IMPORTANT: WKT format is typically LONGITUDE LATITUDE (x y), but Google Maps uses LATITUDE LONGITUDE
      const coordinates = coordPairs.map(pair => {
        const parts = pair.split(' ').filter(part => part.trim() !== '');

        if (parts.length < 2) {
          console.error('Invalid coordinate pair:', pair);
          return null;
        }

        // WKT format is typically LONGITUDE LATITUDE (x y), but Google Maps uses LATITUDE LONGITUDE
        // So we need to swap the coordinates
        const lng = parseFloat(parts[0]);
        const lat = parseFloat(parts[1]);

        if (isNaN(lat) || isNaN(lng)) {
          console.error('Invalid coordinate values:', parts);
          return null;
        }

        return { lat, lng };
      }).filter(coord => coord !== null);

      console.log('Converted coordinates:', coordinates);

      return coordinates;
    } catch (error) {
      console.error('Error converting WKT to coordinates:', error);
      return [];
    }
  }

  private clearMarkers() {
    console.log(`Clearing ${this.markers.length} markers from map`);

    // Remove all markers from the map
    this.markers.forEach((marker, index) => {
      try {
        if (this.useAdvancedMarkers) {
          marker.map = null; // This is how you remove an AdvancedMarkerElement from the map
        } else {
          marker.setMap(null); // This is how you remove a regular Marker from the map
        }
        console.log(`Cleared marker ${index + 1}`);
      } catch (error) {
        console.error(`Error clearing marker ${index + 1}:`, error);
      }
    });

    // No need to clear facility boundaries here as they're handled in drawFacilityBoundaries

    this.markers = [];
  }

  private showInfoWindow(marker: any, markerInfo: PermitMarker) {
    // Create info window content
    const content = this.createInfoWindowContent(markerInfo);

    this.infoWindow.setContent(content);

    // For AdvancedMarkerElement, we need to get the position
    const position = marker.position as google.maps.LatLng;
    this.infoWindow.setPosition(position);
    this.infoWindow.open(this.map);

    // Add event listener for the "View Details" button
    this.addViewDetailsListener(markerInfo);
  }

  private showInfoWindowLegacy(marker: any, markerInfo: PermitMarker) {
    // Create info window content
    const content = this.createInfoWindowContent(markerInfo);

    this.infoWindow.setContent(content);
    this.infoWindow.open(this.map, marker);

    // Add event listener for the "View Details" button
    this.addViewDetailsListener(markerInfo);
  }

  private createInfoWindowContent(markerInfo: PermitMarker): string {
    // Format the status as a badge similar to the permits list
    const statusClass = this.getStatusClass(markerInfo.status);
    const statusLabel = this.getStatusLabel(markerInfo.status);

    // Format dates in the "hh:mm Mon dd, yyyy" format
    const formattedStartDate = this.formatDateForDisplay(markerInfo.startDate);
    const formattedEndDate = this.formatDateForDisplay(markerInfo.endDate);

    return `
      <div class="info-window" style="padding: 0 0 8px 0;">
        <div class="info-window-header" style="display: flex; align-items: center; margin-bottom: 12px;">
          <div class="permit-type-icon" style="background-color: ${markerInfo.permitTypeColor}; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 10px; flex-shrink: 0;">
            <i class="fas fa-${markerInfo.permitTypeIcon}" style="color: white; font-size: 12px;"></i>
          </div>
          <div style="display: flex; align-items: center; flex-grow: 1; overflow: hidden;">
            <h3 style="margin: 0; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${markerInfo.permitNo}</h3>
            <div class="status-badge outline-badge ${statusClass}" style="margin-left: 8px; flex-shrink: 0;">${statusLabel}</div>
          </div>
        </div>

        <p style="margin: 0 0 12px; color: #4b5563; font-size: 14px;">${markerInfo.description}</p>

        <div style="display: flex; align-items: center; margin-bottom: 12px; background-color: #f3f4f6; padding: 8px; border-radius: 6px;">
          <i class="fas fa-clock" style="color: #6b7280; margin-right: 8px;"></i>
          <div style="font-size: 13px; color: #4b5563;">
            ${formattedStartDate} - ${formattedEndDate}
          </div>
        </div>

        <p style="margin: 0 0 12px; font-size: 13px;"><strong>Requested By:</strong> ${markerInfo.requestedBy}</p>

        <button id="view-details" class="view-details-btn" style="background-color: var(--ion-color-primary, #3880ff); color: white; border: none; border-radius: 4px; padding: 8px 12px; width: 100%; font-weight: 500; cursor: pointer; margin-bottom: 4px;">View Details</button>
      </div>
    `;
  }

  private getStatusClass(status: string): string {
    switch (status) {
      case 'OPEN':
        return 'orange';
      case 'IN_REVIEW':
        return 'orange';
      case 'APPROVED':
        return 'light-Green';
      case 'ISSUED':
        return 'darak-Green';
      case 'CLOSED':
        return 'darak-Green';
      case 'CANCELLED':
      case 'REJECTED':
      case 'EXPIRED':
        return 'light-Grey';
      case 'EXTENDED':
        return 'darak-Green';
      default:
        return 'orange';
    }
  }

  private getStatusLabel(status: string): string {
    switch (status) {
      case 'OPEN':
        return 'Open';
      case 'IN_REVIEW':
        return 'In Review';
      case 'APPROVED':
        return 'Approved';
      case 'ISSUED':
        return 'Issued';
      case 'CLOSED':
        return 'Closed';
      case 'CANCELLED':
        return 'Cancelled';
      case 'REJECTED':
        return 'Rejected';
      case 'EXPIRED':
        return 'Expired';
      case 'EXTENDED':
        return 'Extended';
      default:
        return status;
    }
  }

  private formatDateForDisplay(dateString: string): string {
    if (!dateString) return '-';

    try {
      const date = new Date(dateString);

      // Format time as HH:MM
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const timeStr = `${hours}:${minutes}`;

      // Format date as "MMM DD, YYYY"
      const month = date.toLocaleString('en-US', { month: 'short' });
      const day = date.getDate();
      const year = date.getFullYear();

      return `${timeStr} ${month} ${day}, ${year}`;
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString; // Return original string if parsing fails
    }
  }

  private addViewDetailsListener(markerInfo: PermitMarker): void {
    google.maps.event.addListenerOnce(this.infoWindow, 'domready', () => {
      document.getElementById('view-details').addEventListener('click', () => {
        this.showPermitDetails(markerInfo.permitData);
      });
    });
  }

  /**
   * Create a custom HTML marker with Font Awesome icon
   * @param iconName Font Awesome icon name (without the 'fa-' prefix)
   * @param color Background color for the marker
   * @returns HTML element for the marker
   */
  private createCustomMarkerElement(iconName: string, color: string): HTMLElement {
    // Create a container element
    const container = document.createElement('div');
    container.className = 'custom-marker-container';

    // Create the marker element
    const marker = document.createElement('div');
    marker.className = 'custom-marker';
    marker.style.backgroundColor = color;

    // Create the icon element
    const icon = document.createElement('i');
    icon.className = `fas fa-${iconName}`;

    // Assemble the elements
    marker.appendChild(icon);
    container.appendChild(marker);

    return container;
  }

  private getMarkerColor(status: string): string {
    // Define colors for different statuses
    let color = '#3880ff'; // Default blue

    switch (status) {
      case 'OPEN':
        color = '#3880ff'; // Blue
        break;
      case 'IN_REVIEW':
        color = '#ffce00'; // Yellow
        break;
      case 'APPROVED':
        color = '#2dd36f'; // Green
        break;
      case 'ISSUED':
        color = '#2dd36f'; // Green
        break;
      case 'CLOSED':
        color = '#92949c'; // Gray
        break;
      case 'CANCELLED':
        color = '#eb445a'; // Red
        break;
      case 'REJECTED':
        color = '#eb445a'; // Red
        break;
      case 'EXPIRED':
        color = '#eb445a'; // Red
        break;
      case 'EXTENDED':
        color = '#3dc2ff'; // Light blue
        break;
    }

    return color;
  }

  /**
   * Adjusts the size of all markers on the map
   * @param size The new size in pixels
   */
  private adjustMarkerSize(size: number) {
    if (!this.markers || this.markers.length === 0) return;

    console.log(`Adjusting ${this.markers.length} markers to size ${size}px`);

    // Update each marker with the new size
    this.markers.forEach((marker, index) => {
      try {
        if (index < this.permitMarkers.length) {
          const markerInfo = this.permitMarkers[index];

          // Create a new pin SVG with the updated size
          const markerSvg = `
            <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 64 64">
              <path d="M32 2 C18 2 8 12 8 26 C8 40 32 62 32 62 C32 62 56 40 56 26 C56 12 46 2 32 2 Z"
                    fill="${markerInfo.permitTypeColor}"
                    stroke="white"
                    stroke-width="2"/>
              <circle cx="32" cy="26" r="10" fill="white"/>
            </svg>
          `;

          // Create a data URL from the SVG
          const svgUrl = 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(markerSvg);

          // Update the marker's icon
          marker.setIcon({
            url: svgUrl,
            scaledSize: new google.maps.Size(size, size),
            anchor: new google.maps.Point(size/2, size * 0.97) // Bottom center of the pin
          });
        }
      } catch (error) {
        console.error(`Error adjusting marker ${index} size:`, error);
      }
    });
  }

  /**
   * Adjusts the font size of all facility name labels on the map
   * @param fontSize The new font size (e.g., '14px')
   */
  private adjustFacilityLabelSize(fontSize: string) {
    // Find all facility name labels in the DOM
    const facilityLabels = document.querySelectorAll('.facility-name-label');

    console.log(`Adjusting ${facilityLabels.length} facility labels to font size ${fontSize}`);

    // Update each label's font size
    facilityLabels.forEach(label => {
      try {
        (label as HTMLElement).style.fontSize = fontSize;
      } catch (error) {
        console.error('Error adjusting facility label size:', error);
      }
    });
  }

  /**
   * Adds a custom layer control to the map
   */
  private addLayerControl() {
    if (!this.map) return;

    // Create the main button container
    const mainButtonDiv = document.createElement('div');
    mainButtonDiv.className = 'map-control-button';
    mainButtonDiv.style.backgroundColor = 'white';
    mainButtonDiv.style.borderRadius = '8px';
    mainButtonDiv.style.margin = '10px';
    mainButtonDiv.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.3)';
    mainButtonDiv.style.width = '40px';
    mainButtonDiv.style.height = '40px';
    mainButtonDiv.style.display = 'flex';
    mainButtonDiv.style.alignItems = 'center';
    mainButtonDiv.style.justifyContent = 'center';
    mainButtonDiv.style.cursor = 'pointer';
    mainButtonDiv.style.zIndex = '1';

    // Create the main button icon
    const mainButtonIcon = document.createElement('i');
    mainButtonIcon.className = 'fas fa-layer-group';
    mainButtonIcon.style.fontSize = '18px';
    mainButtonIcon.style.color = '#555';
    mainButtonDiv.appendChild(mainButtonIcon);

    // Create the dropdown menu container (initially hidden)
    const dropdownMenu = document.createElement('div');
    dropdownMenu.className = 'map-control-dropdown';
    dropdownMenu.style.backgroundColor = 'white';
    dropdownMenu.style.borderRadius = '8px';
    dropdownMenu.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.3)';
    dropdownMenu.style.position = 'absolute';
    dropdownMenu.style.top = '50px';
    dropdownMenu.style.right = '10px';
    dropdownMenu.style.width = '180px';
    dropdownMenu.style.display = 'none';
    dropdownMenu.style.flexDirection = 'column';
    dropdownMenu.style.zIndex = '2';
    dropdownMenu.style.overflow = 'hidden';

    // Create the menu header
    const header = document.createElement('div');
    header.style.padding = '8px 12px';
    header.style.backgroundColor = '#f5f5f5';
    header.style.borderBottom = '1px solid #e0e0e0';
    header.style.fontWeight = 'bold';
    header.style.fontSize = '14px';
    header.style.color = '#333';
    header.textContent = 'Map Options';
    dropdownMenu.appendChild(header);

    // Create section header for map types
    const mapTypesHeader = document.createElement('div');
    mapTypesHeader.style.padding = '6px 12px';
    mapTypesHeader.style.backgroundColor = '#f9f9f9';
    mapTypesHeader.style.fontSize = '12px';
    mapTypesHeader.style.color = '#666';
    mapTypesHeader.style.fontWeight = 'bold';
    mapTypesHeader.textContent = 'Map Type';
    dropdownMenu.appendChild(mapTypesHeader);

    // Create buttons for each map type
    this.mapTypes.forEach(mapType => {
      const button = document.createElement('button');
      button.style.padding = '8px 12px';
      button.style.border = 'none';
      button.style.borderBottom = '1px solid #e0e0e0';
      button.style.backgroundColor = this.map.getMapTypeId() === mapType.id ? '#e0e0e0' : 'white';
      button.style.cursor = 'pointer';
      button.style.width = '100%';
      button.style.textAlign = 'left';
      button.style.fontSize = '13px';
      button.style.transition = 'background-color 0.2s';
      button.style.display = 'flex';
      button.style.alignItems = 'center';

      // Create icon element
      const icon = document.createElement('i');
      icon.className = `fas fa-${mapType.icon}`;
      icon.style.marginRight = '8px';
      icon.style.fontSize = '14px';
      icon.style.color = '#555';
      button.appendChild(icon);

      // Create text element
      const text = document.createElement('span');
      text.textContent = mapType.name;
      button.appendChild(text);

      // Add hover effect
      button.onmouseover = () => {
        if (this.map.getMapTypeId() !== mapType.id) {
          button.style.backgroundColor = '#f0f0f0';
        }
      };

      button.onmouseout = () => {
        if (this.map.getMapTypeId() !== mapType.id) {
          button.style.backgroundColor = 'white';
        }
      };

      // Add click handler to change map type
      button.onclick = () => {
        // Update map type
        this.map.setMapTypeId(mapType.id);

        // Update button styles
        const mapTypeButtons = dropdownMenu.querySelectorAll('.map-type-button');
        mapTypeButtons.forEach(btn => {
          (btn as HTMLElement).style.backgroundColor = 'white';
        });
        button.style.backgroundColor = '#e0e0e0';

        // Close the dropdown
        dropdownMenu.style.display = 'none';
      };

      button.className = 'map-type-button';
      dropdownMenu.appendChild(button);
    });

    // Create section header for tools
    const toolsHeader = document.createElement('div');
    toolsHeader.style.padding = '6px 12px';
    toolsHeader.style.backgroundColor = '#f9f9f9';
    toolsHeader.style.fontSize = '12px';
    toolsHeader.style.color = '#666';
    toolsHeader.style.fontWeight = 'bold';
    toolsHeader.textContent = 'Tools';
    dropdownMenu.appendChild(toolsHeader);

    // Create buttons for each tool
    this.mapTools.forEach(tool => {
      const button = document.createElement('button');
      button.style.padding = '8px 12px';
      button.style.border = 'none';
      button.style.borderBottom = '1px solid #e0e0e0';
      button.style.backgroundColor = this.isMeasuring && tool.id === 'measure' ? '#e0e0e0' : 'white';
      button.style.cursor = 'pointer';
      button.style.width = '100%';
      button.style.textAlign = 'left';
      button.style.fontSize = '13px';
      button.style.transition = 'background-color 0.2s';
      button.style.display = 'flex';
      button.style.alignItems = 'center';

      // Create icon element
      const icon = document.createElement('i');
      icon.className = `fas fa-${tool.icon}`;
      icon.style.marginRight = '8px';
      icon.style.fontSize = '14px';
      icon.style.color = '#555';
      button.appendChild(icon);

      // Create text element
      const text = document.createElement('span');
      text.textContent = tool.name;
      button.appendChild(text);

      // Add hover effect
      button.onmouseover = () => {
        if (!(this.isMeasuring && tool.id === 'measure')) {
          button.style.backgroundColor = '#f0f0f0';
        }
      };

      button.onmouseout = () => {
        if (!(this.isMeasuring && tool.id === 'measure')) {
          button.style.backgroundColor = 'white';
        }
      };

      // Add click handler for the tool
      button.onclick = () => {
        if (tool.id === 'measure') {
          this.toggleMeasurementTool();
          button.style.backgroundColor = this.isMeasuring ? '#e0e0e0' : 'white';
        }

        // Close the dropdown
        dropdownMenu.style.display = 'none';
      };

      button.className = 'map-tool-button';
      dropdownMenu.appendChild(button);
    });

    // Remove border from last button
    const buttons = dropdownMenu.querySelectorAll('button');
    if (buttons.length > 0) {
      (buttons[buttons.length - 1] as HTMLElement).style.borderBottom = 'none';
    }

    // Toggle dropdown visibility when main button is clicked
    mainButtonDiv.onclick = () => {
      if (dropdownMenu.style.display === 'none') {
        dropdownMenu.style.display = 'flex';
      } else {
        dropdownMenu.style.display = 'none';
      }
    };

    // Close dropdown when clicking elsewhere on the map
    this.map.addListener('click', () => {
      dropdownMenu.style.display = 'none';
    });

    // Add the controls to the map
    const controlContainer = document.createElement('div');
    controlContainer.style.position = 'relative';
    controlContainer.appendChild(mainButtonDiv);
    controlContainer.appendChild(dropdownMenu);

    this.map.controls[google.maps.ControlPosition.TOP_RIGHT].push(controlContainer);

    console.log('Added layer control to map');
  }

  /**
   * Toggles the measurement tool on/off
   */
  private toggleMeasurementTool() {
    this.isMeasuring = !this.isMeasuring;

    if (this.isMeasuring) {
      // Enable measurement mode
      this.startMeasurement();
    } else {
      // Disable measurement mode
      this.clearMeasurement();
    }
  }

  /**
   * Starts the distance measurement tool
   */
  private startMeasurement() {
    // Clear any existing measurement
    this.clearMeasurement();

    // Create a polyline to show the measurement path
    this.measurementPath = new google.maps.Polyline({
      map: this.map,
      path: [],
      strokeColor: '#FF0000',
      strokeOpacity: 0.8,
      strokeWeight: 3
    });

    // Add click listener to the map
    this.map.addListener('click', this.handleMeasurementClick.bind(this));

    // Change cursor to indicate measurement mode
    this.map.setOptions({ draggableCursor: 'crosshair' });

    console.log('Measurement tool activated');
  }

  /**
   * Handles clicks during measurement mode
   */
  private handleMeasurementClick(event: google.maps.MapMouseEvent) {
    if (!this.isMeasuring || !event.latLng || !this.measurementPath) return;

    // Add the clicked point to our measurement
    const path = this.measurementPath.getPath();
    path.push(event.latLng);
    this.measurementPoints.push(event.latLng);

    // Add a marker at the clicked point
    const marker = new google.maps.Marker({
      position: event.latLng,
      map: this.map,
      icon: {
        path: google.maps.SymbolPath.CIRCLE,
        scale: 5,
        fillColor: '#FF0000',
        fillOpacity: 1,
        strokeColor: '#FFFFFF',
        strokeWeight: 2
      }
    });

    this.measurementMarkers.push(marker);

    // If we have at least two points, show the distance
    if (this.measurementPoints.length >= 2) {
      this.updateMeasurementDistance();
    }
  }

  /**
   * Updates the measurement distance display
   */
  private updateMeasurementDistance() {
    if (this.measurementPoints.length < 2) return;

    // Calculate total distance
    let totalDistance = 0;
    for (let i = 1; i < this.measurementPoints.length; i++) {
      const point1 = this.measurementPoints[i - 1];
      const point2 = this.measurementPoints[i];
      totalDistance += this.calculateDistance(point1, point2);
    }

    // Format distance for display
    let distanceText = '';
    if (totalDistance < 1000) {
      distanceText = `${totalDistance.toFixed(1)} m`;
    } else {
      distanceText = `${(totalDistance / 1000).toFixed(2)} km`;
    }

    // Show distance at the midpoint of the last segment
    const lastPoint = this.measurementPoints[this.measurementPoints.length - 1];
    const prevPoint = this.measurementPoints[this.measurementPoints.length - 2];

    const midpoint = {
      lat: (lastPoint.lat() + prevPoint.lat()) / 2,
      lng: (lastPoint.lng() + prevPoint.lng()) / 2
    };

    // Create or update info window with distance
    if (!this.infoWindow) {
      this.infoWindow = new google.maps.InfoWindow();
    }

    this.infoWindow.setContent(`
      <div style="font-family: var(--ion-font-family, inherit); padding: 5px;">
        <strong>Total Distance:</strong> ${distanceText}
      </div>
    `);

    this.infoWindow.setPosition(midpoint);
    this.infoWindow.open(this.map);
  }

  /**
   * Calculates the distance between two points in meters
   */
  private calculateDistance(point1: google.maps.LatLng, point2: google.maps.LatLng): number {
    return google.maps.geometry.spherical.computeDistanceBetween(point1, point2);
  }

  /**
   * Clears the current measurement
   */
  private clearMeasurement() {
    // Remove the polyline
    if (this.measurementPath) {
      this.measurementPath.setMap(null);
      this.measurementPath = null;
    }

    // Remove all markers
    this.measurementMarkers.forEach(marker => marker.setMap(null));
    this.measurementMarkers = [];

    // Clear points array
    this.measurementPoints = [];

    // Close info window if open
    if (this.infoWindow) {
      this.infoWindow.close();
    }

    // Reset cursor
    this.map.setOptions({ draggableCursor: null });
  }



  /**
   * Get permit type information including icon and color
   * @param permitType The permit type code
   * @returns The permit type information
   */
  private async getPermitTypeInfo(permitType: string): Promise<any> {
    if (!permitType) return null;

    try {
      const query = `SELECT * FROM PERMIT_TYPE_HEADER WHERE PERMIT_TYPE = '${permitType}'`;
      console.log(`Executing query for permit type: ${query}`);
      const result = await this.dataService.executeQuery(query);

      if (result && result.length > 0) {
        console.log(`Found permit type info for ${permitType}:`, result[0]);
        return result[0];
      }
      console.log(`No permit type found for ${permitType}`);
      return null;
    } catch (error) {
      console.error('Error fetching permit type info:', error);
      return null;
    }
  }

  /**
   * Convert color number to hex color string
   * @param colorNum The color number
   * @param permitType The permit type for debugging
   * @returns The hex color string
   */
  private getColorHex(colorNum: any, permitType?: string): string {
    console.log(`Converting color for permit type ${permitType}:`, colorNum, typeof colorNum);

    // Default color (blue)
    if (colorNum === undefined || colorNum === null) {
      console.log(`No color defined for permit type ${permitType}, using default blue`);
      return '#3880ff';
    }

    // If it's already a string that starts with #, return it
    if (typeof colorNum === 'string' && colorNum.startsWith('#')) {
      console.log(`Color is already hex format: ${colorNum}`);
      return colorNum;
    }

    // Convert number to hex color
    try {
      const num = typeof colorNum === 'string' ? parseInt(colorNum, 10) : colorNum;
      console.log(`Converting color number ${num} for permit type ${permitType}`);

      let hexColor: string;
      switch (num) {
        case 0: hexColor = '#3880ff'; break; // Blue (primary)
        case 1: hexColor = '#eb445a'; break; // Red (danger)
        case 2: hexColor = '#2dd36f'; break; // Green (success)
        case 3: hexColor = '#ffc409'; break; // Yellow (warning)
        case 4: hexColor = '#92949c'; break; // Gray (medium)
        case 5: hexColor = '#222428'; break; // Black (dark)
        case 6: hexColor = '#f4f5f8'; break; // White (light)
        case 7: hexColor = '#3dc2ff'; break; // Light Blue (secondary)
        case 8: hexColor = '#5260ff'; break; // Indigo (tertiary)
        case 9: hexColor = '#f46b45'; break; // Orange
        case 10: hexColor = '#8b5cf6'; break; // Purple
        case 11: hexColor = '#10b981'; break; // Emerald
        case 12: hexColor = '#f59e0b'; break; // Amber
        case 13: hexColor = '#ef4444'; break; // Red-500
        case 14: hexColor = '#06b6d4'; break; // Cyan
        case 15: hexColor = '#84cc16'; break; // Lime
        default:
          // If it's a large number, it might be a decimal representation of RGB
          if (num > 255) {
            hexColor = '#' + num.toString(16).padStart(6, '0');
          } else {
            hexColor = '#3880ff'; // Default blue
          }
          break;
      }

      console.log(`Converted color ${num} to ${hexColor} for permit type ${permitType}`);
      return hexColor;
    } catch (error) {
      console.error(`Error converting color number to hex for permit type ${permitType}:`, error);
      return '#3880ff'; // Default blue
    }
  }

  private async getStructureDetails(tag: string) {
    if (!tag) {
      console.log('No TAG provided to getStructureDetails');
      return null;
    }

    try {
      // The PERMIT_HEADER->TAG field is a reference to STRUCTURE_HEADER->TAG
      // We need to query the STRUCTURE_HEADER table to get the latitude and longitude
      const query = `SELECT * FROM STRUCTURE_HEADER WHERE TAG = '${tag}'`;
      console.log(`Executing query: ${query}`);
      const result = await this.dataService.executeQuery(query);

      if (result && result.length > 0) {
        console.log(`Found structure details for TAG ${tag}:`, result[0]);
        return result[0];
      }
      console.log(`No structure found for TAG ${tag}`);
      return null;
    } catch (error) {
      console.error('Error fetching structure details:', error);
      return null;
    }
  }

  private async showPermitDetails(permit: any) {
    if (!permit) return;

    localStorage.setItem('selectedPermit', JSON.stringify(permit));
    let userRole: string = '';

    // Create and present the modal
    const modal = await this.modalController.create({
      component: PermitDetailsComponent,
      cssClass: 'full-screen-modal',
      backdropDismiss: false,
      id: 'permit-details',
      componentProps: {
        userRole: userRole,
        permit: JSON.parse(JSON.stringify(permit)),
        isReport: false
      },
    });

    return await modal.present();
  }
}
