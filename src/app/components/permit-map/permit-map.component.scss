.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 600px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  max-width: 100%; /* Ensure it doesn't exceed parent width */
}

.map-canvas {
  width: 100%;
  height: 100%;
  min-height: 600px;
  max-width: 100%; /* Ensure it doesn't exceed parent width */
}

.no-permits-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.no-permits-message {
  text-align: center;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-width: 80%;
}

.no-permits-message ion-icon {
  font-size: 48px;
  color: var(--ion-color-primary);
  margin-bottom: 16px;
}

.no-permits-message h3 {
  margin: 0 0 8px;
  color: var(--ion-color-dark);
}

.no-permits-message p {
  margin: 0;
  color: var(--ion-color-medium);
}

/* Info Window Styling */
::ng-deep .gm-style .gm-style-iw-c {
  padding: 16px 16px 8px 16px;
  border-radius: 8px;
  max-width: 320px !important;
}

::ng-deep .gm-style .gm-style-iw-d {
  max-width: 100% !important;
  overflow: hidden !important;
  padding-right: 0 !important;
  margin-right: 14px !important;
}

::ng-deep .info-window {
  font-family: var(--ion-font-family, inherit);
  max-width: 100%;
}

::ng-deep .info-window h3 {
  margin: 0 0 8px;
  color: var(--ion-color-primary);
  font-size: 16px;
  font-weight: 600;
}

::ng-deep .info-window p {
  margin: 4px 0;
  font-size: 14px;
  line-height: 1.4;
}

/* Status badge styling in info window */
::ng-deep .info-window .status-badge {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  letter-spacing: 0.3px;
  display: inline-block;
  white-space: nowrap;
  vertical-align: middle;
}

::ng-deep .info-window .status-badge.outline-badge {
  background-color: transparent;
}

::ng-deep .info-window .orange {
  background-color: #f59e0b;
  color: white;
}

::ng-deep .info-window .orange.outline-badge {
  color: #f59e0b;
  border: 1px solid #f59e0b;
  background-color: transparent;
}

::ng-deep .info-window .light-Green {
  background-color: #10b981;
  color: white;
}

::ng-deep .info-window .light-Green.outline-badge {
  color: #10b981;
  border: 1px solid #10b981;
  background-color: transparent;
}

::ng-deep .info-window .darak-Green {
  background-color: #047857;
  color: white;
}

::ng-deep .info-window .darak-Green.outline-badge {
  color: #047857;
  border: 1px solid #047857;
  background-color: transparent;
}

::ng-deep .info-window .light-Grey {
  background-color: #ef4444;
  color: white;
}

::ng-deep .info-window .light-Grey.outline-badge {
  color: #ef4444;
  border: 1px solid #ef4444;
  background-color: transparent;
}

::ng-deep .view-details-btn {
  background-color: var(--ion-color-primary);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  margin-top: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  width: 100%;
  transition: background-color 0.2s ease;
}

::ng-deep .view-details-btn:hover {
  background-color: var(--ion-color-primary-shade);
}

.custom-marker-container {
  position: relative;
  width: 40px;
  height: 40px;
  cursor: pointer;
}

.custom-marker {
  position: absolute;
  top: 0;
  left: 0;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #3880ff;
  border: 2px solid white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  z-index: 1;
}

/* Ensure Font Awesome icons are properly centered */
.custom-marker i {
  display: block;
  text-align: center;
  width: 100%;
}

/* Custom styling for pin labels */
::ng-deep .pin-label {
  position: relative;
  top: -30px; /* Move the label up to center it in the pin */
  left: 0;
  text-align: center;
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 50%;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Facility name label styling */
::ng-deep .facility-name-label {
  transition: all 0.2s ease-in-out;
  transform-origin: center;
  user-select: none;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-family: var(--ion-font-family, inherit);
}

/* Make facility name labels more visible on zoom */
::ng-deep .facility-name-label:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

/* Map controls styling */
::ng-deep .map-control-button {
  font-family: var(--ion-font-family, inherit);
  user-select: none;
  transition: all 0.2s ease;
}

::ng-deep .map-control-button:hover {
  background-color: #f5f5f5;
  transform: scale(1.05);
}

::ng-deep .map-control-dropdown {
  font-family: var(--ion-font-family, inherit);
  user-select: none;
  animation: fadeIn 0.2s ease;
}

::ng-deep .map-control-dropdown button {
  font-family: var(--ion-font-family, inherit);
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

::ng-deep .map-control-dropdown button:hover {
  background-color: #f0f0f0;
}

::ng-deep .map-control-dropdown button:focus {
  outline: none;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}