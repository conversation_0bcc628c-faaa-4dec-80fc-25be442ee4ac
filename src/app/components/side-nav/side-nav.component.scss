.side-nav {
  height: 100%;
  width: 240px;
  background-color: #24348B; /* Changed from white to match header color */
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  transition: width 0.3s ease;
  position: fixed;
  left: 0;
  top: 56px; /* Adjust based on header height */
  z-index: 1000;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.side-nav.collapsed {
  width: 60px;
}

.toggle-button {
  position: absolute;
  left: 15px; /* Changed from right to left */
  top: 15px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  background-color: transparent; /* Removed background color */
  border-radius: 0; /* Removed border radius */
  
  &:hover {
    opacity: 0.8; /* Simple hover effect */
  }
  
  ion-icon {
    font-size: 24px;
    color: white;
  }
}

.nav-items {
  display: flex;
  flex-direction: column;
  padding-top: 60px; /* Increased to accommodate the hamburger icon */
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 15px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  color: white; /* Changed text color to white for better contrast */
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1); /* Lighter hover effect */
  }
  
  &.active {
    border-left-color: white; /* Changed from #24348B to white */
    background-color: rgba(255, 255, 255, 0.2); /* Slightly more visible background for active item */
    
    .nav-icon {
      filter: brightness(1.1);
    }
    
    .nav-label {
      color: white; /* Changed from #24348B to white */
      font-weight: 600;
    }
  }
}

.icon-container {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.nav-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
  color: white; /* Added to ensure icons are white */
}

.nav-label {
  white-space: nowrap;
  font-size: 14px;
  color: white; /* Changed from #333 to white */
}

/* Media queries for responsive design */
@media (max-width: 768px) {
  .side-nav {
    width: 60px;
  }
  
  .side-nav:not(.collapsed) {
    width: 240px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
  }
}
