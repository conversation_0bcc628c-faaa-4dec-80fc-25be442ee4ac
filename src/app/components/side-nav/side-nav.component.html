<div class="side-nav" [class.collapsed]="isCollapsed">
  <div class="toggle-button" (click)="toggleCollapse()">
    <ion-icon [name]="isCollapsed ? 'menu-outline' : 'close-outline'"></ion-icon>
  </div>
  
  <div class="nav-items">
    <!-- Permits - always visible -->
    <div class="nav-item" 
         [class.active]="selectedOption === 'permits'"
         (click)="navigate('permits')"
         [title]="isCollapsed ? 'Permits' : ''">
      <div class="icon-container">
        <ion-icon name="document-text-outline" class="nav-icon"></ion-icon>
      </div>
      <span class="nav-label" *ngIf="!isCollapsed">Permits</span>
    </div>

    <!-- Agents - visible only if user has AGENT_MGMT permission -->
    <div class="nav-item"
         *ngIf="userPermissions.agents"
         [class.active]="selectedOption === 'agents'"
         (click)="navigate('agents')"
         [title]="isCollapsed ? 'Agents' : ''">
      <div class="icon-container">
        <ion-icon name="people-outline" class="nav-icon"></ion-icon>
      </div>
      <span class="nav-label" *ngIf="!isCollapsed">Agents</span>
    </div>

    <!-- Facilities - visible only if user has FACILITY_MGMT permission -->
    <div class="nav-item"
         *ngIf="userPermissions.facilities"
         [class.active]="selectedOption === 'facilities-divisions'"
         (click)="navigate('facilities-divisions')"
         [title]="isCollapsed ? 'Facilities' : ''">
      <div class="icon-container">
        <ion-icon name="business-outline" class="nav-icon"></ion-icon>
      </div>
      <span class="nav-label" *ngIf="!isCollapsed">Facilities</span>
    </div>

    <!-- Reports - visible only if user has REPORT permission -->
    <div class="nav-item"
         *ngIf="userPermissions.reports"
         [class.active]="selectedOption === 'reports'"
         (click)="navigate('reports')"
         [title]="isCollapsed ? 'Reports' : ''">
      <div class="icon-container">
        <ion-icon name="analytics-outline" class="nav-icon"></ion-icon>
      </div>
      <span class="nav-label" *ngIf="!isCollapsed">Reports</span>
    </div>

    <!-- Configuration - visible only if user has CONFIGURATION permission -->
    <div class="nav-item"
         *ngIf="userPermissions.configuration"
         [class.active]="selectedOption === 'master-data'"
         (click)="navigate('master-data')"
         [title]="isCollapsed ? 'Configuration' : ''">
      <div class="icon-container">
        <ion-icon name="settings-outline" class="nav-icon"></ion-icon>
      </div>
      <span class="nav-label" *ngIf="!isCollapsed">Configuration</span>
    </div>

    <!-- Users - visible only if user has USER_MGMT permission -->
    <div class="nav-item"
         *ngIf="userPermissions.users"
         [class.active]="selectedOption === 'users'"
         (click)="navigate('users')"
         [title]="isCollapsed ? 'Users' : ''">
      <div class="icon-container">
        <ion-icon name="person-outline" class="nav-icon"></ion-icon>
      </div>
      <span class="nav-label" *ngIf="!isCollapsed">Users</span>
    </div>
  </div>
</div>
