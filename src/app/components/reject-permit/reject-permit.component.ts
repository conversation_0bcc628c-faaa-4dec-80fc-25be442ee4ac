import { Component, CUSTOM_ELEMENTS_SCHEMA, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule, ModalController } from '@ionic/angular';
import { ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';

@Component({
  standalone: true,
  selector: 'app-reject-permit',
  templateUrl: './reject-permit.component.html',
  styleUrls: ['./reject-permit.component.scss'],
  imports: [IonicModule , CommonModule , FormsModule , ReactiveFormsModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class RejectPermitComponent  implements OnInit {


  addReason: boolean;
  time:any
  comment: string = ''
  @Input() permit : any
  @Input() stakeData: any 
  latestPermit: any
  constructor(public modalCtrl: ModalController, public unviredSdk: UnviredCordovaSDK) { }

  ngOnInit() {
    this.assignComments(this.permit,this.stakeData)
    if(this.addReason == false){
      this.getPermitLog(this.stakeData)
    }
  
    
  }
  assignComments(permit: any , stake: any) {
   if(stake.rejectReason){
    this.comment = stake.rejectReason
   }
  }


  cancel(){
    this.modalCtrl.dismiss();
  }

  saveComments(){
    this.modalCtrl.dismiss(this.comment);
  }




  async getPermitLog(stakeData){
    if(stakeData.ROLE == 'REVIEW' && stakeData.APPR_TYPE != null){
      let getDataQuery = `SELECT * FROM PERMIT_LOG WHERE PERMIT_NO = '${stakeData.PERMIT_NO}' AND APPR_TYPE = '${stakeData.APPR_TYPE}' AND APPROVAL = 'R';`
      let response = await this.unviredSdk.dbExecuteStatement(getDataQuery)
      if(response.type ==  ResultType.success){
        let getpermitData = response.data
        this.comment = getpermitData[0].COMMENT
  
      }
    } else{
      let getDataQuery = `SELECT * FROM PERMIT_LOG WHERE PERMIT_NO = '${stakeData.PERMIT_NO}' AND PERMIT_STATUS = 'IN_REVIEW' AND APPROVAL = 'R';`
      let response = await this.unviredSdk.dbExecuteStatement(getDataQuery)
      if(response.type ==  ResultType.success){
        let getpermitData = response.data
        this.comment = getpermitData[0].COMMENT
  
      }
    }
  
  }

 

 



}
