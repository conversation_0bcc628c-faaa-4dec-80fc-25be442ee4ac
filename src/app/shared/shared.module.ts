import { NgModule } from '@angular/core';
import { GetTagNamePipe, GetRequestedByNamePipe, GetFullDateByTimestampPipe, GetPermitDescriptionPipe, GetPermitDivisionNamePipe, GetAgentNamePipe, GetFullDateByTimestampPipeReport, IsActionButtonDissabledPipe, GetApprovalTypeUserPipe } from '../pipes/util-pipes';

@NgModule({
    declarations: [
        GetTagNamePipe, GetRequestedByNamePipe, GetFullDateByTimestampPipe, GetPermitDescriptionPipe,
        GetPermitDivisionNamePipe,GetAgentNamePipe, GetFullDateByTimestampPipeReport,IsActionButtonDissabledPipe, GetApprovalTypeUserPipe
    ],
    exports: [
        GetTagNamePipe, GetRequestedByNamePipe, GetFullDateByTimestampPipe, GetPermitDescriptionPipe,
        GetPermitDivisionNamePipe,GetAgentNamePipe,GetFullDateByTimestampPipeReport,IsActionButtonDissabledPipe, GetApprovalTypeUserPipe
    ]
})
export class SharedModule { }