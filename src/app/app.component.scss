ion-content.with-side-nav {
  --padding-start: 60px; // collapsed width

  @media (min-width: 992px) {
    --padding-start: 240px; // expanded width
  }
}

// Make the side nav responsive
@media (max-width: 768px) {
  ion-content.with-side-nav {
    --padding-start: 0;
  }
}

.app-container {
  display: flex;
  height: calc(100% - 56px); /* Adjust for header height */
  width: 100%;
  position: absolute; /* Changed from relative to absolute */
  top: 56px; /* Adjust based on header height */
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden; /* Contain overflow at this level */
}

.content-container {
  flex: 1;
  margin-left: 60px; /* Width of collapsed side nav */
  transition: margin-left 0.3s ease;
  width: calc(100% - 60px); /* Adjust width based on collapsed nav */
  overflow: auto; /* Enable scrolling at the container level */
  height: 100%; /* Ensure full height */
  display: flex;
  flex-direction: column;
}

.content-container ion-router-outlet {
  flex: 1;
  display: block;
  /* Allow content to overflow and handle its own scrolling */
  overflow: auto;
  height: 100%;
  position: relative;
  width: 100%;
}

/* Media query for larger screens where side nav may be expanded */
@media (min-width: 992px) {
  .content-container {
    margin-left: 240px; /* Width of expanded side nav */
    width: calc(100% - 240px); /* Adjust width for expanded nav */
  }
}

/* For mobile view - different layout */
@media (max-width: 768px) {
  .content-container {
    margin-left: 0;
    width: 100%; /* Full width on mobile */
  }
}

ion-title {
  font-size: 1.2rem;
  font-weight: 500;
}

/* Improved deep styles for Ionic scrolling */
::ng-deep ion-content {
  --offset-top: 0 !important;
  --overflow: auto !important;
  overflow-y: auto !important;
  position: relative;
  display: block;
  height: 100%;
}

/* Target the .scroll-y class that Ionic uses for scrollable content */
::ng-deep .scroll-y {
  overflow-y: auto !important;
  position: relative;
  height: 100% !important;
}

/* Target the actual scrollable container in Ionic */
::ng-deep .inner-scroll {
  overflow-y: auto !important;
  -webkit-overflow-scrolling: touch;
  position: relative;
  height: 100% !important;
}

/* Add scroll style */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Ensure route content takes full height */
ion-router-outlet {
  display: block;
  height: 100%;
}
