import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { RouteReuseStrategy } from '@angular/router';
import { GoogleMapsModule } from '@angular/google-maps';

import { IonicModule, IonicRouteStrategy } from '@ionic/angular';

import { AppComponent } from './app.component';
import { AppRoutingModule } from './app-routing.module';
import { DataService } from './services/data.service';

import { AndroidPermissions } from '@awesome-cordova-plugins/android-permissions/ngx';
import { Geolocation } from '@awesome-cordova-plugins/geolocation/ngx';
import { SplashScreen } from '@awesome-cordova-plugins/splash-screen/ngx';
import { StatusBar } from '@awesome-cordova-plugins/status-bar/ngx';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { SocialSharing } from '@awesome-cordova-plugins/social-sharing/ngx';
import { CameraPreview } from '@awesome-cordova-plugins/camera-preview/ngx';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { ScreenOrientation } from '@awesome-cordova-plugins/screen-orientation/ngx';
import { Keyboard } from '@awesome-cordova-plugins/keyboard/ngx';
import { Diagnostic } from '@awesome-cordova-plugins/diagnostic/ngx';
import { Network } from '@awesome-cordova-plugins/network/ngx';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { ReactiveFormsModule } from '@angular/forms';
import { LocationStrategy, HashLocationStrategy } from '@angular/common';
import { AppSpecificUtilityService } from './services/app-specific-utility.service';
import { BusyIndicatorService } from './services/busy-indicator.service';
import { ShareDataService } from './services/ShareData.service';
import { HelperFunctionService } from './services/HelperFunction.service';
import { ToastrModule } from 'ngx-toastr';
import { File } from '@awesome-cordova-plugins/file/ngx';

import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import {LocationAccuracy} from '@awesome-cordova-plugins/location-accuracy/ngx';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { SideNavComponent } from './components/side-nav/side-nav.component';
import { LocationPickerComponent } from './components/location-picker/location-picker.component';
import { FacilitySelectionService } from './services/facility-selection.service';
import { ConfigService } from './services/config.service';
import { OwlDateTimeModule, OwlNativeDateTimeModule } from '@danielmoncada/angular-datetime-picker';
export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

@NgModule({
  declarations: [
    AppComponent,
    SideNavComponent,
    LocationPickerComponent
  ],
  imports: [
    BrowserModule,
    ReactiveFormsModule,
    IonicModule.forRoot(),
    ToastrModule.forRoot(),
    AppRoutingModule,
    HttpClientModule,
    GoogleMapsModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
    }),
    BrowserAnimationsModule,
    OwlDateTimeModule,
    OwlNativeDateTimeModule
  ],
  providers: [
    AndroidPermissions,
    Device,
    Diagnostic,
    File,
    Geolocation,
    LocationAccuracy,
    StatusBar,
    SplashScreen,
    ScreenOrientation,
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    { provide: LocationStrategy, useClass: HashLocationStrategy },
    DataService,
    HelperFunctionService,
    ShareDataService,
    UnviredCordovaSDK,
    BusyIndicatorService,
    AppSpecificUtilityService,
    InAppBrowser,
    CameraPreview,
    Network,
    SocialSharing,
    Keyboard,
    FacilitySelectionService,
    ConfigService
  ],
  bootstrap: [AppComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AppModule {
}
