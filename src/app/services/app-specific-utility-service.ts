import { Injectable, NgZone } from '@angular/core';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  NotifResult,
  NotificationListenerType,
  OutboxLockResult,
  OutboxLockStatus,
  RequestType,
  ResultType,
  SettingsResult,
  SyncResult,
  UnviredCordovaSDK,
  UnviredResult,
} from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AppConstants } from '../shared/app-constants';
import { BehaviorSubject, Observable } from 'rxjs';
import { DataService } from './data.service';
import { NavigationExtras, Router } from '@angular/router';
import { BusyIndicatorService } from './busy-indicator.service';
import { AlertController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { Location } from '@angular/common';
import { Geolocation } from '@awesome-cordova-plugins/geolocation/ngx';
import { ShareDataService } from './ShareData.service';
import { HelperFunctionService } from './HelperFunction.service';
@Injectable({
  providedIn: 'root',
})
export class AppSpecificUtilityService {
  public constants: AppConstants;
  public isPlatformReady: BehaviorSubject<boolean>;
  public showMobileView: BehaviorSubject<boolean>;
  public deviceMode: BehaviorSubject<number | null>;
  public userInfo: BehaviorSubject<any>;
  public applicationVersion: string = '';
  public formDocument: BehaviorSubject<any>;
  public versionReload: BehaviorSubject<boolean>;
  public statusMessage: BehaviorSubject<string>;
  public refreshData: BehaviorSubject<boolean>;
  public syncItemsCount = 0;
  public initialCount = 0;

  constructor(
    private router: Router,
    private fetchData: DataService,
    private shareData: ShareDataService,
    private loader: BusyIndicatorService,
    private unviredSDK: UnviredCordovaSDK,
    private alertController: AlertController,
    private translate: TranslateService,
    public utilityFunction: HelperFunctionService,
    public ngZone: NgZone,
    private location: Location,
    private geolocation: Geolocation
  ) {
    this.constants = new AppConstants();
    this.isPlatformReady = new BehaviorSubject(false);
    this.showMobileView = new BehaviorSubject(false);
    this.deviceMode = new BehaviorSubject<number | null>(null);
    this.userInfo = new BehaviorSubject({});
    this.formDocument = new BehaviorSubject({});
    this.versionReload = new BehaviorSubject(false);
    this.statusMessage = new BehaviorSubject('');
    this.refreshData = new BehaviorSubject(true);
  }
}
