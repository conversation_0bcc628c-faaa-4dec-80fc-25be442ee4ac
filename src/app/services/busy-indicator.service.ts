import { Injectable } from '@angular/core';
import { <PERSON>ading<PERSON>ontroller, AlertController, ToastController, AnimationController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})
export class BusyIndicatorService {

  isLoading = false;
  message = '';

  constructor(
    public loadingController: LoadingController,
    public alertController: AlertController,
    private toastController: ToastController,
    private translate: TranslateService,
    private animationCtrl: AnimationController
  ) { }

  async showBusyIndicator(msg: string, indType: string) {
    let loader = null;
    this.isLoading = true;
    this.message = msg
    if (indType == 'dots') {
      loader = await this.loadingController.create({
        message: msg,
        spinner: 'dots',
        animated: true,
        showBackdrop: true,
        translucent: true
      });
    } else {
      loader = await this.loadingController.create({
        message: msg,
        spinner: 'crescent',
        animated: true,
        showBackdrop: true,
        translucent: true
      });
    }
    await loader.present();
    if (!this.isLoading) {
      this.loadingController.dismiss();
    }
  }

  async dismissBusyIndicator() {
    this.isLoading = false;
    return await this.loadingController.dismiss();
  }

  // Error/Warning Alert for user
  async showAlert(msgType: string, msg: string) {
    const e_alert = await this.alertController.create({
      header: this.translate.instant(msgType),
      subHeader: msg,
      animated: true,
      backdropDismiss: false,
      buttons: [this.translate.instant("OK")]
    });
    await e_alert.present();
  }

  // Toast for 2 sec to show data is proccessing in background
  async showToast(msg: string) {
    const toast = await this.toastController.create({
      message: msg,
      animated: true,
      color: 'dark',
      duration: 2000,
      position: 'bottom'
    });
    toast.present();
  }

  enterAnimation = (baseEl: any) => {
    const backdropAnimation = this.animationCtrl.create()
      .addElement(baseEl.querySelector('ion-backdrop')!)
      .fromTo('opacity', '0.01', 'var(--backdrop-opacity)');

    const wrapperAnimation = this.animationCtrl.create()
      .addElement(baseEl.querySelector('.modal-wrapper')!)
      .keyframes([
        { offset: 0, opacity: '0', transform: 'scale(0)' },
        { offset: 1, opacity: '0.99', transform: 'scale(1)' }
      ]);

    return this.animationCtrl.create()
      .addElement(baseEl)
      .easing('ease-out')
      .duration(500)
      .addAnimation([backdropAnimation, wrapperAnimation]);
  }

  leaveAnimation = (baseEl: any) => {
    return this.enterAnimation(baseEl).direction('reverse');
  }

  enterAnimationPopover = (baseEl: any, ev?: Event) => {
    const POPOVER_MD_BODY_PADDING = 12;
    const doc = (baseEl.ownerDocument as any);
    const isRTL = doc.dir === 'rtl';

    let originY = 'top';
    let originX = isRTL ? 'right' : 'left';

    const contentEl = baseEl.querySelector('.popover-content') as HTMLElement;
    const contentDimentions = contentEl.getBoundingClientRect();
    const contentWidth = contentDimentions.width;
    const contentHeight = contentDimentions.height;

    const bodyWidth = doc.defaultView.innerWidth;
    const bodyHeight = doc.defaultView.innerHeight;

    // If ev was passed, use that for target element
    const targetDim =
      ev && ev.target && (ev.target as HTMLElement).getBoundingClientRect();

    // As per MD spec, by default position the popover below the target (trigger) element
    const targetTop =
      targetDim != null && 'bottom' in targetDim
        ? targetDim.bottom
        : bodyHeight / 2 - contentHeight / 2;

    const targetLeft =
      targetDim != null && 'left' in targetDim
        ? isRTL
          ? targetDim.left - contentWidth + targetDim.width
          : targetDim.left
        : bodyWidth / 2 - contentWidth / 2;

    const targetHeight = (targetDim && targetDim.height) || 0;

    const popoverCSS: { top: any; left: any } = {
      top: targetTop,
      left: targetLeft
    };

    // If the popover left is less than the padding it is off screen
    // to the left so adjust it, else if the width of the popover
    // exceeds the body width it is off screen to the right so adjust
    if (popoverCSS.left < POPOVER_MD_BODY_PADDING) {
      popoverCSS.left = POPOVER_MD_BODY_PADDING;

      // Same origin in this case for both LTR & RTL
      // Note: in LTR, originX is already 'left'
      originX = 'left';
    } else if (
      contentWidth + POPOVER_MD_BODY_PADDING + popoverCSS.left >
      bodyWidth
    ) {
      popoverCSS.left = bodyWidth - contentWidth - POPOVER_MD_BODY_PADDING;

      // Same origin in this case for both LTR & RTL
      // Note: in RTL, originX is already 'right'
      originX = 'right';
    }

    // If the popover when popped down stretches past bottom of screen,
    // make it pop up if there's room above
    if (
      targetTop + targetHeight + contentHeight > bodyHeight &&
      targetTop - contentHeight > 0
    ) {
      popoverCSS.top = targetTop - contentHeight - targetHeight;
      baseEl.className = baseEl.className + ' popover-bottom';
      originY = 'bottom';
      // If there isn't room for it to pop up above the target cut it off
    } else if (targetTop + targetHeight + contentHeight > bodyHeight) {
      contentEl.style.bottom = POPOVER_MD_BODY_PADDING + 'px';
    }
    const backdropAnimation = this.animationCtrl.create()
      .addElement(baseEl.querySelector('ion-backdrop')!)
      .fromTo('opacity', '0.01', 'var(--backdrop-opacity)')
      .beforeStyles({
        'pointer-events': 'none'
      })
      .afterClearStyles(['pointer-events']);

    const wrapperAnimation = this.animationCtrl.create()
      .addElement(baseEl.querySelector('.popover-wrapper')!)
      .fromTo('opacity', 0.01, 1);

    const contentAnimation = this.animationCtrl.create()
      .addElement(baseEl.querySelector('.popover-content')!)
      .beforeStyles({
        'top': `${popoverCSS.top}px`,
        'left': `${popoverCSS.left}px`,
        'transform-origin': `${originY} ${originX}`
      })
      .fromTo('transform', 'scale(0.001)', 'scale(1)');

    const viewportAnimation = this.animationCtrl.create()
      .addElement(baseEl.querySelector('.popover-viewport')!)
      .fromTo('opacity', 0.01, 1);

    return this.animationCtrl.create()
      .addElement(baseEl)
      .easing('ease-out')
      // .easing('cubic-bezier(0.36,0.66,0.04,1)')
      .duration(500)
      .addAnimation([backdropAnimation, wrapperAnimation, contentAnimation, viewportAnimation]);
  }

  leaveAnimationPopover = (baseEl: any) => {
    return this.enterAnimationPopover(baseEl).direction('reverse');
  }

  async showWorkFlowToast(msg: string) {
    const toast = await this.toastController.create({
      message: msg,
      animated: true,
      cssClass: "workflow-error-toast",
      color: 'danger',
      duration: 3000,
      position: 'top',
    });
    toast.present();
  }
}
