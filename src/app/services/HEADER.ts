// tslint:disable-next-line: class-name
export interface DWNLD_TIME_HEADER {
    ID: string;
    TIME: number;
}

// tslint:disable-next-line: class-name
export interface FORM_HEADER {
    FORM_ID: string;
    FORM_VERSION: number;
    FORM_NAME: string;
    FORM_TITLE: string;
    FORM_DESC: string;
    TEMPLATE: string;
    AVATAR: string;
    CATEGORY: string;
    ATTRIBUTES: string;
    FORM_TYPE:string;
}

// tslint:disable-next-line: class-name
export interface FORMSET_HEADER {
    ID: string;
    NAME: string;
    FORMSET_DESC: string;
    AVATAR: string;
    CREATED_BY: string;
    CREATED_ON: number;
    TEAM_ASSIGNMENT: string;
    FORMSET_FORM: FORMSET_FORM[];
}

// tslint:disable-next-line: class-name
export interface FORMSET_FORM {
    FORMSET_ID: string;
    FORM_ID: string;
}

// tslint:disable-next-line: class-name
export interface INPUT_REOPEN_TASK_HEADER {
    TASK_ID: string;
    FORM_ID: string;

}
// tslint:disable-next-line: class-name
export interface MASTER_DATA_HEADER {
    SUBMISSION_ID: string;
    RESOURCE_NAME: string;
    DATA: string;
}

// tslint:disable-next-line: class-name
export interface PRIORITY_HEADER {
    PRIORITY_VALUE: number;
    PRIORITY_NAME: string;
}

// tslint:disable-next-line: class-name
export interface RESOURCE_HEADER {
    FORM_ID: string;
    FORM_VERSION: number;
    RESOURCE_USED: string;
}

// tslint:disable-next-line: class-name
// export interface SETTINGS_HEADER {
//     SETTINGS_KEY: string;
//     SETTINGS_VALUE: string;
//     LID: string;
//     OBJECT_STATUS: number;
// }

// tslint:disable-next-line: class-name
export interface TASK_HEADER {
    TASK_ID: string;
    REF_TASK_ID: string;
    FORM_ID: string;
    FORM_SET_ID: string;
    TASK_DESC: string;
    TASK_TYPE: string;
    TASK_SOURCE: string;
    PRIMARY_USER: string;
    PRIMARY_TEAM: string;
    TASK_STATUS: string;
    TASK_DUEDATE: string;
    CREATED_BY: string;
    COMPLETED_BY: string;
    COMMENTS: string;
    CREATED_ON: number;
    COMPLETED_ON: number;
    LAST_SUBMITTED_ON: number;
    PRIORITY: number;
    P_MODE: string;
    TEAM_ASSIGNMENT: string;
    WORKFLOW_COMMENTS: string;
    LID: string;
    OBJECT_STATUS: number;
    SYNC_STATUS: number;
    EXCLUSIVE_SHARE_MODE: string;
    LOCATION: string;
    TASK_DUEDATE_TIMESTAMP: number;
    APPROVAL_STATUS: string;
    REMINDER: string;
}

// tslint:disable-next-line: class-name
export interface TASK_ALERT_HEADER {
    ALERT_ID: string;
    TASK_ID: string;
    FORM_ID: string;
    FORMSET_ID: string;
    USER_ID: string;
    MESSAGE: string;
    ALERT_ICON: string;
    UPDATED_ON: number;
    ALERT_READ: number;
}

// tslint:disable-next-line: class-name
export interface TASK_MESSAGE_HEADER {
    MESSAGE_ID: string;
    TASK_ID: string;
    MESSAGE: string;
    CREATED_BY: string;
    CREATED_ON: number;
}

// tslint:disable-next-line: class-name
export class TASK_SUBMISSION_HEADER {
    TASK_ID: string;
    FORM_ID: string;
    VERSION_ID: number;
    SUBMISSION_ID: string;
    FORM_DATA: string;
    SERVER_DATA: string;
    SERVER_TIMESTAMP: number;
    FORM_NAME: string;
    P_MODE: string;
    DIFF: string;
    FORM_STATE: string;
    LAST_UPDATED_BY: string;
    LAST_UPDATED_AT: number;
    LID: string;
    OBJECT_STATUS: number;
    SYNC_STATUS: number;
    LOCATION: string;
    /** FIXME: Completion Flag Changes. */
    COMPLETION_PERC: string;
    SUBM_ERROR: string;
    USER_STATUS: string;

    TASK_SUBMISSION_ATTACHMENT: TASK_SUBMISSION_ATTACHMENT[];
}

// tslint:disable-next-line: class-name
export interface TASK_SUBMISSION_ATTACHMENT {
    UID: string;
    FILE_NAME: string;
    MIME_TYPE: string;
    URL: string;
    EXTERNAL_URL: string;
    URL_REQUIRES_AUTH: string;
    LOCAL_PATH: string;
    NO_CACHE: string;
    SERVER_TIMESTAMP: number;
    TAG1: string;
    TAG2: string;
    TAG3: string;
    TAG4: string;
    TAG5: string;
    ATTACHMENT_STATUS: string;
    AUTO_DOWNLOAD: string;
    PARAM: string;
    MESSAGE: string;
    FID: string;
    DATA: string;
    OBJECT_STATUS: number;
}

// tslint:disable-next-line: class-name
export interface TASK_USER_HEADER {
    TASK_ID: string;
    USER_ID: string;
    STATUS: string;
    COMMENTS: string;
    TASK_DESC: string;
    COMPLETED_ON: number;
    READ_ONLY: string;
    P_MODE: string;
    FAIL_REVIEW_COMMENTS: string;
    LOCK: string;
    LID: string;
    OBJECT_STATUS: number;
    SYNC_STATUS: number;
    ACTION_DATA: string;
    LOCATION: string;
    SUBM_DATA_HASH: string;
}

// tslint:disable-next-line: class-name
export interface TEAMS_HEADER {
    TEAM_ID: string;
    TEAM_NAME: string;
    TEAM_DESCRIPTION: string;
    MANAGER: string;
    AVATAR: string;
    TEAM_MEMBER: TEAM_MEMBER[];
}

// tslint:disable-next-line: class-name
export interface TEAM_MEMBER {
    USER_ID: string;
    TEAM_ID: string;
    USERNAME: string;
    FNAME: string;
    LNAME: string;
    EMAIL: string;
    ROLE: string;
    AVATAR: string;
    PHONE_NO:string;
}

// tslint:disable-next-line: class-name
export interface INPUT_EMAIL_PDF_HEADER {
    TASK_ID: string;
    FORM_ID: string;
    EMAIL_IDS: string;
    SEND_TO_SELF: string;
}

// tslint:disable-next-line: class-name
export interface LOCATION_PARAMETERS {
    latitude: number,
    longitude: number,
}

// tslint:disable-next-line: class-name
export interface SORT_FILTER_OPTION {
    LABEL: string,
    IS_CHECKED: boolean,
    ACTIVE_FILTER: number
}

export interface FILTER_OPTION {
    name: string,
    IS_CHECKED: boolean,
    ACTIVE_FILTER: number
}

// tslint:disable-next-line: class-name
export interface FILTER_PARAMS {
    SORTED_BY: number,
    GROUPED_BY: number,
    CATEGORY: number
}


// tslint:disable-next-line: class-name
export interface INPUT_GET_HISTORY_HEADER {
    TASK_ID: string,
    USER_ID: string
}

export interface FORM_DOCUMENTS_ATTACHMENT {
    UID: string;
    FILE_NAME: string;
    MIME_TYPE: string;
    URL: string;
    EXTERNAL_URL: string;
    URL_REQUIRES_AUTH: string;
    LOCAL_PATH: string;
    NO_CACHE: string;
    SERVER_TIMESTAMP: number;
    TAG1: string;
    TAG2: string;
    TAG3: string;
    TAG4: string;
    TAG5: string;
    ATTACHMENT_STATUS: any;
    AUTO_DOWNLOAD: string;
    PARAM: string;
    MESSAGE: string;
    LID: string;
    FID: string;
}

export interface TASK_ANNOTATIONS_HEADER {
    SUBMISSION_ID: string;
    ANNOTAION_JSON: string;
    MODIFIED_TIME: string;
    LID: string;
    OBJECT_STATUS: number;
}

export interface FORM_EVE_ANALYTICS_HEADER {
    PKUID: string;
    LID: string;
    OBJECT_STATUS: number;
    FORM_EVE_ANALYTICS_ITEM: FORM_EVE_ANALYTICS_ITEM[];
}

export interface FORM_EVE_ANALYTICS_ITEM {
    FORM_NAME: string;
    SUBMISSION_ID: string;
    EVENT: string;
    FLD_NAME: string;
    READ_TIMESTAMP: string;
    SEQ_NO: number;
    ADD_DATA: string;
    FID: string;
    OBJECT_STATUS: string;
    SESSION_ID: String;
}

export interface EXTERNAL_USER_HEADER {
    ID: string;
    FIRST_NAME: string;
    LAST_NAME: string;
    EMAIL: string;
    PHONE_NO: string;
    PRIVATE: string;
    CREATED_BY: string;
    CREATED_AT: string;
    P_MODE: string;
    LID: string;
}

export interface FAV_FORMS_HEADER {
    USER_ID: string;
    FORMS: string;
}
export interface INPUT_GET_FORM_FROM_TASK_HEADER {
    TASK_ID: string;
    FORM_ID: string;
    EXTFLD1: string;
    EXTFLD2: string;
}