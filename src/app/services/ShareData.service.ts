import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Subject, Observable, BehaviorSubject } from 'rxjs';
import { SyncResult } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AppConstants } from '../shared/app-constants';
// import { FILTER_PARAMS } from '../data-modal/HEADER';

@Injectable({
  providedIn: 'root'
})
export class ShareDataService {
  public constants: AppConstants
  // public updateFilterParams = {} as FILTER_PARAMS;
  public platform: string = '';
  public dataFromChat = null;
  public formset_task: any;
  public formset_icon_color: string;
  public MASTER_DATA: any;
  public formData: any;
  public conflictData: any;
  public deviceMode: boolean;
  public taskSubmission: any;
  public mergeData: any;
  public conflictFields: any;
  public show_client_data: any;
  public show_server_data: any;
  public conflictFieldsInitialized: any;
  public task_submissions_form_data: any;
  public task_submissions_server_data: any;
  public selectedOption = new Subject<any>();
  public isFirstTime: boolean = false;
  public lastNoOfAlerts: number = 0;
  public selectedConflictDetails = new Subject<any>();
  public setSelectedMergeDetails = new Subject<Object>();
  public showDevice = new BehaviorSubject<any>(false);
  public showConflictActionPage = new Subject<any>();
  public initTabulatorData = new Subject<any>();
  public getBackToRenderer = new Subject<any>();
  public formDataForAnnotation: any;
  public SERVER_RESPONSE = {
    customization_response: {} as SyncResult,
    task_response: {} as SyncResult
  };
  public formIOComponent: any;
  public subData: any;
  public tabulatorProperties: any;
  public formType: any;
  public activePage: any;
  public createFormObj: any;
  public discoveryData = new Subject<any>();
  public isDownloaded: boolean = false;
  public isNavigateFromCameraPage: boolean = false;
  public imagePreviewModalOpened: boolean = false;

  constructor(
    public http: HttpClient,
  ) {
    this.constants = new AppConstants();
    this.isFirstTime = false;
  }

  setDevicePlatform(platformName: string) {
    this.platform = platformName;
  }

  getDevicePlatform() {
    return this.platform;
  }

  setServerResponse(response_1: SyncResult, response_2: SyncResult) {
    this.SERVER_RESPONSE.customization_response = response_1;
    this.SERVER_RESPONSE.task_response = response_2;
  }

  getResponseFromServerCall() {
    return this.SERVER_RESPONSE;
  }

  setDataFromChatPage(chatMessage: string) {
    this.dataFromChat = chatMessage;
  }

  getDataFromChatPage() {
    return this.dataFromChat;
  }

  setFormsetData(selectedFormsetTask: any, icon_color: string) {
    this.formset_task = selectedFormsetTask;
    this.formset_icon_color = icon_color;
  }

  getFormsetData() {
    return {
      FORM_SET: this.formset_task,
      FORMSET_ICON_COLOR: this.formset_icon_color
    };
  }

  setMasterData(masterData: any) {
    this.MASTER_DATA = masterData;
  }

  getMasterData() {
    return this.MASTER_DATA;
  }

  setFormData(data: any) {
    this.formData = data;
  }
  getFormData() {
    return this.formData;
  }

  resetFormData() {
    this.formData = {};
  }

  listenToEventBySelectedOption(): Observable<number> {
    return this.selectedOption.asObservable();
  }

  emitEventBySelectedOption(option: number) {
    this.selectedOption.next(option);
  }

  setFirstTimeFlag(value: boolean) {
    this.isFirstTime = value;
  }

  getFirstTimeFlag() {
    return this.isFirstTime;
  }

  resetFirstTimeFlag() {
    this.isFirstTime = false;
  }

  setTotalNoOfAlerts(alertCount: number) {
    this.lastNoOfAlerts = alertCount;
  }

  getLastNoOfAlerts() {
    return this.lastNoOfAlerts;
  }

  setServerConflictData(dataVal: any) {
    this.conflictData = dataVal;
  }

  getServerConflictData() {
    return this.conflictData;
  }

  setShowDeviceMode(data: boolean) {
    this.deviceMode = data;
  }

  getShowDeviceMode() {
    return this.deviceMode;
  }

  setTaskSubmissionData(submission: any) {
    this.taskSubmission = submission;
  }

  getTaskSubmissionData() {
    return this.taskSubmission;
  }

  setConflictMergeData(mergeData: any) {
    this.mergeData = mergeData;
  }

  getConflictMergeData() {
    return this.mergeData;
  }

  setUpdatedConflictDataSet(conflictFields: any, conflictFieldsInitialized: any, task_submissions_form_data: any, task_submissions_server_data: any, show_client_data: any, show_server_data: any) {
    this.conflictFields = conflictFields;
    this.conflictFieldsInitialized = conflictFieldsInitialized;
    this.task_submissions_form_data = task_submissions_form_data;
    this.task_submissions_server_data = task_submissions_server_data;
    this.show_client_data = show_client_data,
      this.show_server_data = show_server_data
  }

  getUpdatedConflictDataSet() {
    return {
      CONFLICT_FIELDS: this.conflictFields,
      CONFLICT_FIELDS_INITIALIZED: this.conflictFieldsInitialized,
      FORM_DATA: this.task_submissions_form_data,
      SERVER_DATA: this.task_submissions_server_data,
      SHOW_FORM_DATA: this.show_client_data,
      SHOW_SERVER_DATA: this.show_server_data
    };
  }

  setFormIOComponent(subData: any, formIOComponent: any) {
    this.subData = subData;
    this.formIOComponent = formIOComponent;
  }

  getFormIOComponent() {
    return {
      FORM_COMPONENT: this.formIOComponent,
      SUBMSN_DATA: this.subData
    }
  }

  setTabulatorProperties(tabProps: any) {
    this.tabulatorProperties = tabProps;
  }

  getTabulatorProperties() {
    return this.tabulatorProperties;
  }
  setFormType(type) {
    this.formType = type;
  }
  getFormType() {
    return this.formType
  }
  setFormDataForAnnotation(data: any) {
    this.formDataForAnnotation = data;
  }
  getFormDataForAnnotation() {
    return this.formDataForAnnotation;
  }
  setFormCreateStatus(obj) {
    this.createFormObj = obj;
  }

  getFormCreateStatus() {
    return this.createFormObj;
  }
  setCustomizationDownloaded(flag){
   this.isDownloaded = flag;
  }
  getCustomizationDownloaded(){
    return this.isDownloaded;
  }
  setNavigateFromCameraPage(flag){
    this.isNavigateFromCameraPage = flag
  }
  getNavigateFromCameraPage(){
    return this.isNavigateFromCameraPage;
  }

  setImagePreviewModalOpened(flag){
    this.imagePreviewModalOpened = flag
  }
  getImagePreviewModalOpened(){
    return this.imagePreviewModalOpened;
  }
}
