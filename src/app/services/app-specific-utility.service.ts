import { FORM_HEADER, INPUT_GET_FORM_FROM_TASK_HEADER, TASK_ANNOTATIONS_HEADER } from 'src/app/services/HEADER';
import { Injectable, NgZone } from '@angular/core';
import { NavigationExtras, Router } from '@angular/router';
import { ShareDataService } from './ShareData.service';
import { BusyIndicatorService } from './busy-indicator.service';
import { UnviredCordovaSDK, DbResult, ResultType, SyncResult, SettingsResult, NotificationListenerType, NotifResult, UnviredResult, OutboxLockResult, OutboxLockStatus, RequestType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AlertController } from '@ionic/angular';
import { LOCATION_PARAMETERS, TASK_HEADER, TASK_USER_HEADER, TASK_SUBMISSION_HEADER } from 'src/app/services/HEADER';
import { BehaviorSubject, Observable } from 'rxjs';
import { Geolocation } from '@awesome-cordova-plugins/geolocation/ngx';
import { TranslateService } from '@ngx-translate/core';
import { HelperFunctionService } from './HelperFunction.service';
import { Location } from '@angular/common';
import { DataService } from './data.service';
import { AppConstants } from '../shared/app-constants';

@Injectable({
  providedIn: 'root'
})
export class AppSpecificUtilityService {
  public constants: AppConstants;
  public isPlatformReady: BehaviorSubject<boolean>;
  public showMobileView: BehaviorSubject<boolean>;
  public deviceMode: BehaviorSubject<number>;
  public userInfo: BehaviorSubject<any>;
  public locationCoords = {} as LOCATION_PARAMETERS;
  public applicationVersion: string = '';
  public formDocument: BehaviorSubject<any>;
  public versionReload: BehaviorSubject<boolean>;
  public statusMessage: BehaviorSubject<string>;
  public refreshData: BehaviorSubject<boolean>;
  public syncItemsCount = 0
  public initialCount = 0
  public currentUserDetails: any = { LID: "", USER_ID: "", AVATAR: "", FNAME: "", LNAME: "", EMAIL: "" };
  public company: '';

  constructor(
    private router: Router,
    private fetchData: DataService,
    private shareData: ShareDataService,
    private loader: BusyIndicatorService,
    private unviredSDK: UnviredCordovaSDK,
    private alertController: AlertController,
    private geolocation: Geolocation,
    private translate: TranslateService,
    public utilityFunction: HelperFunctionService,
    public ngZone: NgZone,
    private location: Location,

  ) {
    this.constants = new AppConstants();
    this.isPlatformReady = new BehaviorSubject(false);
    this.showMobileView = new BehaviorSubject(false);
    this.deviceMode = new BehaviorSubject(null);
    this.userInfo = new BehaviorSubject({});
    this.formDocument = new BehaviorSubject({});
    this.versionReload = new BehaviorSubject(false);
    this.statusMessage = new BehaviorSubject("");
    this.refreshData = new BehaviorSubject(true);
  }

  // Get platform status when its changes
  getPlatformStatus(): Observable<boolean> {
    return this.isPlatformReady.asObservable();
  }

  // Get the mode of device like [Mobile, Tablet, Desktop]
  getDeviceMode(): Observable<number> {
    return this.deviceMode.asObservable();
  }

  // Get the view [Mobile, Desktop]
  getViewMode(): Observable<boolean> {
    return this.showMobileView.asObservable();
  }

  // Get the status of form documents
  getFormDocumentDownloadStatus(): Observable<any> {
    return this.formDocument.asObservable();
  }

  // Get the version change status
  getVersionReload(): Observable<boolean> {
    return this.versionReload.asObservable();
  }

  getNotificationStatusMessage(): Observable<string> {
    return this.statusMessage.asObservable();
  }
  refreshDataOnNotification(): Observable<boolean> {
    return this.refreshData.asObservable();
  }

  // Get user name from database
  async getUserName(userID: string) {
    let userName: string;
    let queryToFetchUser: string = `SELECT FNAME, LNAME FROM ${this.constants.TEAM_MEMBER_TABLE} WHERE USER_ID = '${userID}' GROUP BY USER_ID`
    let fetchUser: DbResult = await this.unviredSDK.dbExecuteStatement(queryToFetchUser);
    if (fetchUser.type === ResultType.success) {
      let userDB: any[] = fetchUser.data;
      let user: any = userDB[0];
      userName = `${user.FNAME} ${user.LNAME}`;
    } else {
      userName = this.translate.instant('unknown');
    }
    return userName;
  }

  // fetch Logged-in user details from Database
  async fetchLoggedInUserDetails() {
    let queryToGetUsersData: string = '';
    let dbResultFor_UsersData = {} as DbResult;
    let usersData: any;
    try {
      let userSettingsResult: SettingsResult = await this.unviredSDK.userSettings();
      let userDetails: any = userSettingsResult.data;

      // Check if unvired_id is present or not
      if (userDetails.UNVIRED_ID && userDetails.UNVIRED_ID != undefined && userDetails.UNVIRED_ID != null) {
        queryToGetUsersData = `SELECT LID, USER_ID, AVATAR, FNAME, LNAME, EMAIL, USERNAME, ROLE, PHONE_NO FROM ${this.constants.TEAM_MEMBER_TABLE} WHERE USERNAME = '${userDetails.UNVIRED_ID}' COLLATE NOCASE GROUP BY USER_ID`;
        dbResultFor_UsersData = await this.unviredSDK.dbExecuteStatement(queryToGetUsersData);

        if (dbResultFor_UsersData.type === ResultType.success) {
          usersData = dbResultFor_UsersData.data;
          this.userInfo.next(usersData);
        } else {
          this.userInfo.next({});
          this.dbResponseHandler(dbResultFor_UsersData, this.loader.isLoading);
        }
      } else {
        this.userInfo.next({});
        await this.loader.showAlert('Error', this.translate.instant('User information is missing! Please login again.'));
      }
    } catch (error: any) {
      this.unviredSDK.logError('AppSpecificUtilityService', 'fetchLoggedInUserDetails()', 'ERROR: ' + error);
      await this.loader.showAlert('Error', error);
      this.performLogoutOperation();
    }
  }

  getLoggedInUserInfo() {
    return this.userInfo.asObservable();
  }

  // Present logout confirmation alert
  async showLogoutConfirmation() {
    const showAlert = await this.alertController.create({
      header: this.translate.instant("Confirmation"),
      message: this.translate.instant("Are you sure you want to logout?"),
      animated: true,
      backdropDismiss: false,
      buttons: [
        {
          text: this.translate.instant("Cancel"),
          role: 'cancel',
          handler: () => { }
        }, {
          text: this.translate.instant("Logout"),
          handler: () => { this.performLogoutOperation(); }
        }
      ]
    });
    await showAlert.present();
  }

  // Logout operations
  performLogoutOperation() {
    this.router.navigate(['login']);
    this.unviredSDK.logout();
    this.shareData.resetFirstTimeFlag();
    this.fetchData.appInitializedFirstTime.next(false);
    this.fetchData.checkUMPCallDone.next(false);
    this.shareData.setCustomizationDownloaded(false);
    localStorage.setItem('taskIdLogin', "");  
    localStorage.setItem('loginResult', "0")
  }

  // Get device accurate coordinates using device GPS for app after login
  async getLocationCoordinates() {
    let locationParam: string = '';
    try {
      var resp = await this.geolocation.getCurrentPosition({ timeout: 1000 })
      console.log("location resp "+ resp);
      this.locationCoords.latitude = resp.coords.latitude;
      this.locationCoords.longitude = resp.coords.longitude;
      locationParam = `${this.locationCoords.latitude.toString()}, ${this.locationCoords.longitude.toString()}`;
    } catch (error) {
      console.log("error "+ error);
      this.unviredSDK.logError('AppSpecificUtilityService', 'getLocationCoordinates()', 'ERROR: ' + error);
      locationParam = null;
    }
    return locationParam;
  }

  // Handle response for DB operation
  async dbResponseHandler(response: DbResult, busyIndicator?: boolean) {
    switch (response.type) {
      case ResultType.success:
        await this.showResponse(response, busyIndicator);
        break;
      case ResultType.error:
        await this.showResponse(response, busyIndicator);
        break;
    }
  }

  // Handle response from server for foreground CRUD operation call 
  async serverResponseHandler(response: SyncResult, busyIndicator?: boolean) {
    if (response.code && response.code === 401) {
      if (busyIndicator) {
        await this.loader.dismissBusyIndicator();
      }
      this.performLogoutOperation();
    } else {
      switch (response.type) {
        case ResultType.success:
          await this.showResponse(response, busyIndicator);
          break;
        case ResultType.error:
          await this.showResponse(response, busyIndicator);
          break;
      }
    }
  }

  // Handle response from server while initializing app via forground
  async showResponseAfterImportingDataFromServer(resp_1: SyncResult, resp_2: SyncResult, busyIndicator?: boolean) {
    switch (true) {
      case ((Object.keys(resp_1).length === 0 && resp_1.constructor === Object) && (Object.keys(resp_2).length === 0 && resp_2.constructor === Object)):
        if (busyIndicator) {
          await this.loader.dismissBusyIndicator();
        }
        break;
      case ((Object.keys(resp_1).length === 0 && resp_1.constructor === Object) && (Object.keys(resp_2).length > 0 && resp_2.constructor === Object)):
        if (resp_2.code && resp_2.code === 401) {
          if (busyIndicator) {
            await this.loader.dismissBusyIndicator();
          }
          this.performLogoutOperation();
          break;
        } else {
          switch (resp_2.type) {
            case ResultType.success:
              await this.showResponse(resp_2, busyIndicator);
              break;
            case ResultType.error:
              await this.showResponse(resp_2, busyIndicator);
              break;
          }
        }
        break;
      case ((Object.keys(resp_1).length > 0 && resp_1.constructor === Object) && (Object.keys(resp_2).length === 0 && resp_2.constructor === Object)):
        if (resp_1.code && resp_1.code === 401) {
          if (busyIndicator) {
            await this.loader.dismissBusyIndicator();
          }
          this.performLogoutOperation();
          break;
        } else {
          switch (resp_1.type) {
            case ResultType.success:
              await this.showResponse(resp_1, busyIndicator);
              break;
            case ResultType.error:
              await this.showResponse(resp_1, busyIndicator);
              break;
          }
        }
        break;
      case ((Object.keys(resp_1).length > 0 && resp_1.constructor === Object) && (Object.keys(resp_2).length > 0 && resp_2.constructor === Object)):
        if ((resp_1.code && resp_1.code === 401) || (resp_2.code && resp_2.code === 401)) {
          if (busyIndicator) {
            await this.loader.dismissBusyIndicator();
          }
          this.performLogoutOperation();
          break;
        } else {
          await this.showResponse(resp_1, busyIndicator);
          await this.showResponse(resp_2, busyIndicator);
          break;
        }
        break;
    }
    this.shareData.setServerResponse({} as SyncResult, {} as SyncResult);
  }

  // Show message or error during foreground call
  async showResponse(response: any, busyIndicator?: boolean) {
    if (response.message && response.message.trim().length !== 0) {
      if (busyIndicator) {
        await this.loader.dismissBusyIndicator();
      }
      await this.loader.showAlert('Warning', response.message);
    } else if (response.error && response.error.trim().length !== 0) {
      if (busyIndicator) {
        await this.loader.dismissBusyIndicator();
      }
      await this.loader.showAlert('Error', response.error);
    }
  }

  // Show message or error during background call or in push notification
  async showBackgroundResponse(response: any, busyIndicator?: boolean) {
    let responseData = response.data;
    if (responseData !== null && responseData.length > 0) {
      let messageArray: string[] = responseData.map((data: any) => {
        if (data.MESSAGE && data.MESSAGE.trim().length !== 0) {
          return data.MESSAGE;
        }
      });
      if (messageArray.length > 0) {
        let messageToDisplay: string = messageArray.join(' ').trim();
        if (busyIndicator) {
          await this.loader.dismissBusyIndicator();
        }
        await this.loader.showAlert('Warning', messageToDisplay);
      }
    }
  }

  // Check GetMessage and handle response in asynchronous mode 
  notificationHandler(pageName: string) {
    // Call GetMessage every X seconds.
    setInterval(() => {
      this.unviredSDK.getMessages();
    }, 5 * 60 * 1000);

    // Listen for background updates.
    this.unviredSDK.registerNotifListener().subscribe(async (response: NotifResult) => {
      this.unviredSDK.logInfo(pageName, "notificationHandler()", "Received Notification Callback: " + response.type);
      let attachmentResponse = response.data[0];
      if (response.type !== (NotificationListenerType.attachmentDownloadCompleted || NotificationListenerType.attachmentDownloadError || NotificationListenerType.attachmentDownloadSuccess || NotificationListenerType.attachmentDownloadWaiting)) {
        // check outbox, sent and inbox item count
        this.updateSyncItemsCount();
      }
      switch (response.type) {
        // Successful asynchronous send of data to the server. This type indicates that data has moved from outbox to sent items.
        case NotificationListenerType.dataSend:
          // this.unviredSDK.logInfo(pageName, 'dataSend', , `RESPONSE: ${JSON.stringify(response)}`);
          break;
        // Data changes for each BusinessEntity when received data from server. This type indicates when data is removed from sent items table.
        case NotificationListenerType.dataChanged:
          // this.unviredSDK.logInfo(pageName, 'dataChanged', , `RESPONSE: ${JSON.stringify(response)}`);
          break;
        // Data receive completion on receiving of all BusinessEntities from server. Data would get processed after this step.
        case NotificationListenerType.dataReceived:
          // this.unviredSDK.logInfo(pageName, 'dataReceived', , `RESPONSE: ${JSON.stringify(response)}`);
          break;
        // Application data reset.
        case NotificationListenerType.appReset:
          // this.unviredSDK.logInfo(pageName, 'appReset', , `RESPONSE: ${JSON.stringify(response)}`);
          break;
        // Application with attchment item on attachment download success
        case NotificationListenerType.attachmentDownloadSuccess:
          if (attachmentResponse.hasOwnProperty(this.constants.TASK_SUBMISSION_ATTACHMENT_TABLE)) {
            let submissionLID = attachmentResponse[this.constants.TASK_SUBMISSION_ATTACHMENT_TABLE].FID;
            let isAttachmentsDownloaded: boolean = await this.checkAttachmentsDownloaded(submissionLID, this.loader.isLoading);
            if (isAttachmentsDownloaded) {
              // Repaint the screen
              this.fetchData.checkUMPCallDone.next(true);
              this.unviredSDK.logInfo(pageName, 'attachmentDownloadSuccess', 'SREEN REPAINTED');
            } else {
              this.unviredSDK.logInfo(pageName, 'attachmentDownloadSuccess', `SUBMISSION ATTACHMENT STILL DOWNLOADING  RESPONSE: ${JSON.stringify(response)}`);
            }
          } else if (attachmentResponse.hasOwnProperty(this.constants.FORM_DOCUMENTS_ATTACHMENT_TABLE)) {
            this.unviredSDK.logInfo(pageName, 'attachmentDownloadSuccess', `FORM DOCUMENT ATTACHMENT DOWNLOADED  RESPONSE: ${JSON.stringify(response)}`);
            this.formDocument.next(attachmentResponse[this.constants.FORM_DOCUMENTS_ATTACHMENT_TABLE]);
          } else {
            this.unviredSDK.logInfo(pageName, 'attachmentDownloadSuccess', `Something went wrong, please try again!  RESPONSE: ${JSON.stringify(response)}`);
          }
          break;
        // Application with error message and attchment item on attachment download error
        case NotificationListenerType.attachmentDownloadError:
          if (attachmentResponse.hasOwnProperty(this.constants.TASK_SUBMISSION_ATTACHMENT_TABLE)) {
            this.unviredSDK.logInfo(pageName, 'attachmentDownloadError', `SUBMISSION ATTACHMENT DOWNLOAD FAILED  RESPONSE: ${JSON.stringify(response)}`);
          } else if (attachmentResponse.hasOwnProperty(this.constants.FORM_DOCUMENTS_ATTACHMENT_TABLE)) {
            this.unviredSDK.logInfo(pageName, 'attachmentDownloadError', `FORM DOCUMENT ATTACHMENT DOWNLOAD FAILED  RESPONSE: ${JSON.stringify(response)}`);
            this.formDocument.next(attachmentResponse[this.constants.FORM_DOCUMENTS_ATTACHMENT_TABLE]);
          } else {
            this.unviredSDK.logInfo(pageName, 'attachmentDownloadError', `Something went wrong, please try again!  RESPONSE: ${JSON.stringify(response)}`);
          }
          this.showBackgroundResponse(response, this.loader.isLoading);
          break;
        // Application when incoming data handling finished
        case NotificationListenerType.incomingDataProcessingFinished:
          // Repaint the screen
          this.refreshData.next(true);
          this.fetchData.checkUMPCallDone.next(true);
          this.unviredSDK.logInfo(pageName, 'incomingDataProcessingFinished', 'SREEN REPAINTED');
          break;
        // Application with wait response when data is proccessing on server
        case NotificationListenerType.attachmentDownloadWaiting:
          if (attachmentResponse.hasOwnProperty(this.constants.TASK_SUBMISSION_ATTACHMENT_TABLE)) {
            this.unviredSDK.logInfo(pageName, 'attachmentDownloadWaiting', `SUBMISSION ATTACHMENT WAITING FOR DOWNLOAD  RESPONSE: ${JSON.stringify(response)}`);
          } else if (attachmentResponse.hasOwnProperty(this.constants.FORM_DOCUMENTS_ATTACHMENT_TABLE)) {
            this.unviredSDK.logInfo(pageName, 'attachmentDownloadWaiting', `FORM DOCUMENT ATTACHMENT WAITING FOR DOWNLOAD  RESPONSE: ${JSON.stringify(response)}`);
            this.formDocument.next(attachmentResponse[this.constants.FORM_DOCUMENTS_ATTACHMENT_TABLE]);
          } else {
            this.unviredSDK.logInfo(pageName, 'attachmentDownloadWaiting', `Something went wrong, please try again!  RESPONSE: ${JSON.stringify(response)}`);
          }
          break;
        // Application with any InfoMessages
        case NotificationListenerType.infoMessage:
          // this.unviredSDK.logInfo(pageName, 'infoMessage', , `RESPONSE: ${JSON.stringify(response)}`);
          this.showBackgroundResponse(response, this.loader.isLoading);
          break;
        // Application with Server errors
        case NotificationListenerType.serverError:
          // this.unviredSDK.logInfo(pageName, 'serverError', , `RESPONSE: ${JSON.stringify(response)}`);
          this.showBackgroundResponse(response, this.loader.isLoading);
          break;
        // Application with all attachment downloads completed
        case NotificationListenerType.attachmentDownloadCompleted:
          // this.unviredSDK.logInfo(pageName, 'attachmentDownloadCompleted', `RESPONSE: ${JSON.stringify(response)}`);
          break;
        case NotificationListenerType.JWTTokenReceived:
          this.unviredSDK.logInfo(pageName, "notificationHandler()", "Received JWT token: " + response.data);
          localStorage.setItem('PERMIT_token', response.data);
          break;
      }
      await this.updateSyncItemsCount();
    });
  }

  async updateSyncItemsCount() {
    var outboxCount = 0
    var sentItemCount = 0
    var inBoxItemCount = 0
    var that = this;
    let result1 = await this.unviredSDK.outBoxItemCount();
    outboxCount = parseInt(result1);
    let result2 = await this.unviredSDK.sentItemCount();
    sentItemCount = parseInt(result2);
    let result3 = await this.unviredSDK.inBoxItemCount();
    inBoxItemCount = parseInt(result3);
    that.ngZone.run(() => {
      that.syncItemsCount = outboxCount + sentItemCount + inBoxItemCount
      if (that.syncItemsCount == 0) {
        that.initialCount = 0;
      }
      else if (that.syncItemsCount > that.initialCount) {
        that.initialCount = that.syncItemsCount
      }

      let statusMsg = "";
      if (outboxCount > 0) {
        statusMsg = this.translate.instant('Upload', {firstVal: outboxCount, totalCnt: this.initialCount});
      } else if (sentItemCount > 0) {
        statusMsg = this.translate.instant('Download', {firstVal: sentItemCount, totalCnt: this.initialCount});
      } else if (inBoxItemCount > 0) {
        statusMsg = this.translate.instant('Process', {firstVal: inBoxItemCount, totalCnt: this.initialCount});
      }
      this.statusMessage.next(statusMsg);
    })
  }

  // Get application version in production
  getAppVersion() {
    return this.applicationVersion = this.constants.APP_RELEASE_NUMBER + ' - ' + this.constants.APP_RELEASE_DATE;
  }

  /**
   * Check form [task] is valid. 
   * Check form or other BE related to this form is being proccessed in background
   */
  async checkFormIsValidAndNotProccessingAnyBE(task: TASK_HEADER, taskUser: TASK_USER_HEADER, taskSubmission: TASK_SUBMISSION_HEADER, runningPlaform: string, busyIndicator?: boolean) {
    // 1. Check task status
    // 2. if status is completed then task is invalid
    // 3. if status is apart from completed then check task user status
    // 2. if user status is completed then task user is not valid for this form
    if(task && task.TASK_STATUS){
    if (task.TASK_STATUS === this.constants.TASK_STATUS.COMPLETED || task.TASK_STATUS === this.constants.TASK_STATUS.DELEGATED || task.TASK_STATUS === this.constants.TASK_STATUS.CANCELLED) {
      // Invalid Form/Task
      return false;
    } else {
      let taskUserReadOnlyFlag = false;
      // Check task user is having read only flag
      if (taskUser.READ_ONLY && (taskUser.READ_ONLY == this.constants.TASK_USER_ACCESS.READ_ONLY)) {
        taskUserReadOnlyFlag = true;
      } else if (taskUser.READ_ONLY && (taskUser.READ_ONLY == this.constants.TASK_USER_ACCESS.EDIT)) {
        taskUserReadOnlyFlag = false;
      } else {
        if (task.TASK_TYPE !== this.constants.TASK_TYPE.APPROVAL) {
          taskUserReadOnlyFlag = true;
        }
      }
      if(taskUser && taskUser.STATUS){
      if (taskUser.STATUS === this.constants.TASK_USER_STATUS.COMPLETED || taskUser.STATUS === this.constants.TASK_USER_STATUS.DELEGATED || taskUser.STATUS === this.constants.TASK_USER_STATUS.CANCELLED || taskUserReadOnlyFlag) {
        // Invalid Form/Task
        return false;
      } else {
        // Check if the platform is browser
        if (runningPlaform === 'browser') {
          // Valid Form/Task
          return true;
        } else {
          // Platform is not browser
          try {
            let userIsInSentItem: UnviredResult = await this.unviredSDK.isInSentItem(taskUser.LID);
            let submissionIsInSentItem: UnviredResult = await this.unviredSDK.isInSentItem(taskSubmission.LID);
            if (userIsInSentItem.type === ResultType.success && submissionIsInSentItem.type === ResultType.success) {
              // Check task user or task submission is not present in sent item
              if (userIsInSentItem.data === 'false' && submissionIsInSentItem.data === 'false') {
                try {
                  // Now user is not present in sent item so apply the lock on task user
                  let outboxLockOnUser: OutboxLockResult = await this.unviredSDK.lockDataSender(taskUser.LID);
                  if (outboxLockOnUser.type === ResultType.success) {
                    // Check user data is still present in outbox
                    if (outboxLockOnUser.data === OutboxLockStatus.DataBeingSent) {
                      // Dismiss the busyindicator if any
                      if (busyIndicator) {
                        await this.loader.dismissBusyIndicator();
                      }
                      // Show the toast with data is being synchronized
                      await this.loader.showToast(this.translate.instant("Form is currently being synchronized and cannot be modified."));
                      // Invalid Form/Task
                      return false;
                    } else {
                      try {
                        // Now user is not present in outbox so apply the lock on task submission
                        let outboxLockOnSubmission: OutboxLockResult = await this.unviredSDK.lockDataSender(taskSubmission.LID);
                        if (outboxLockOnSubmission.type === ResultType.success) {
                          // Check submission is still present in outbox
                          if (outboxLockOnSubmission.data === OutboxLockStatus.DataBeingSent) {
                            // Dismiss the busyindicator if any
                            if (busyIndicator) {
                              await this.loader.dismissBusyIndicator();
                            }
                            // Show the toast with data is being synchronized
                            await this.loader.showToast(this.translate.instant("This form is still being synchronized with the server, please try later."));
                            // Invalid Form/Task
                            return false;
                          } else {
                            // Check all attachements are downloaded with this submission 
                            let isDownloaded: boolean = await this.checkAttachmentsDownloaded(taskSubmission.LID, busyIndicator);
                            // Dismiss the busyindicator if any
                            if (busyIndicator) {
                              await this.loader.dismissBusyIndicator();
                            }
                            if (!isDownloaded) {
                              // Show the toast with data is being synchronized
                              await this.loader.showToast(this.translate.instant("This form is still being synchronized with the server, please try later."));
                              // Invalid Form/Task
                              return false;
                            } else {
                              // Valid Form/Task
                              return true;
                            }
                          }
                        } else {
                          // Dismiss the busyindicator if any
                          if (busyIndicator) {
                            await this.loader.dismissBusyIndicator();
                          }
                          // Show error of outbox lock on submission
                          if (outboxLockOnSubmission.message && outboxLockOnSubmission.message.length > 0) {
                            await this.loader.showAlert('Warning', outboxLockOnSubmission.message)
                          } else if (outboxLockOnSubmission.error && outboxLockOnSubmission.error.length > 0) {
                            await this.loader.showAlert('Error', outboxLockOnSubmission.error);
                          }
                          // Invalid Form/Task
                          return false;
                        }
                      } catch (error:any) {
                        this.unviredSDK.logError('AppSpecificUtilityService', 'lockDataSender()', 'ERROR: ' + error);
                        // Dismiss the busyindicator if any
                        if (busyIndicator) {
                          await this.loader.dismissBusyIndicator();
                        }
                        await this.loader.showAlert('Error', error)
                        // On exception also make this as invalid task/form
                        return false;
                      }
                    }
                  } else {
                    // Dismiss the busyindicator if any
                    if (busyIndicator) {
                      await this.loader.dismissBusyIndicator();
                    }
                    // Show error of outbox lock on user
                    if (outboxLockOnUser.message && outboxLockOnUser.message.length > 0) {
                      await this.loader.showAlert('Warning', outboxLockOnUser.message)
                    } else if (outboxLockOnUser.error && outboxLockOnUser.error.length > 0) {
                      await this.loader.showAlert('Error', outboxLockOnUser.error);
                    }
                    // Invalid Form/Task
                    return false;
                  }
                } catch (error:any) {
                  this.unviredSDK.logError('AppSpecificUtilityService', 'lockDataSender()', 'ERROR: ' + error);
                  // Dismiss the busyindicator if any
                  if (busyIndicator) {
                    await this.loader.dismissBusyIndicator();
                  }
                  await this.loader.showAlert('Error', error)
                  // On exception also make this as invalid task/form
                  return false;
                }
              } else if (userIsInSentItem.data === 'true' && taskSubmission.OBJECT_STATUS === this.constants.OBJECT_STATUS.ADD) {
                // Show the toast with data is being synchronized
                // await this.loader.showToast('Form created successfully');
                // Invalid Form/Task
                return true;
              } else {
                // Show the toast with data is being synchronized
                await this.loader.showToast(this.translate.instant("This form is still being synchronized with the server, please try later."));
                // Invalid Form/Task
                return false;
              }
            } else {
              // Dismiss the busyindicator if any
              if (busyIndicator) {
                await this.loader.dismissBusyIndicator();
              }
              // Show error for user and submission sent item call, one by one
              if (userIsInSentItem.message && userIsInSentItem.message.length > 0) {
                await this.loader.showAlert('Warning', userIsInSentItem.message)
              } else if (userIsInSentItem.error && userIsInSentItem.error.length > 0) {
                await this.loader.showAlert('Error', userIsInSentItem.error);
              }
              // Error for submission sent item call
              if (submissionIsInSentItem.message && submissionIsInSentItem.message.length > 0) {
                await this.loader.showAlert('Warning', submissionIsInSentItem.message)
              } else if (submissionIsInSentItem.error && submissionIsInSentItem.error.length > 0) {
                await this.loader.showAlert('Error', submissionIsInSentItem.error);
              }
              // Invalid Form/Task
              return false;
            }
          } catch (error:any) {
            this.unviredSDK.logError('AppSpecificUtilityService', 'isInSentItem()', 'ERROR: ' + error);
            // Dismiss the busyindicator if any
            if (busyIndicator) {
              await this.loader.dismissBusyIndicator();
            }
            await this.loader.showAlert('Error', error)
            // On exception also make this as invalid task/form
            return false;
          }
        }
      }
    }
    }
  }
  }

  async checkAttachmentsDownloaded(submissionLID: string, busyIndicator?: boolean) {
    try {
      let dbQueryForAttachementsNotDownloaded = `SELECT COUNT(*) as COUNT FROM ${this.constants.TASK_SUBMISSION_ATTACHMENT_TABLE} WHERE FID = '${submissionLID}' AND ATTACHMENT_STATUS IN ('${this.constants.IOS_ATTACHMENT_STATUS.QUEUED_FOR_DOWNLOAD}', '${this.constants.AND_ATTACHMENT_STATUS.QUEUED_FOR_DOWNLOAD}')`;
      let dbResultFor_ATTACHMENTS_NOT_DOWNLOADED: DbResult = await this.unviredSDK.dbExecuteStatement(dbQueryForAttachementsNotDownloaded);
      if (dbResultFor_ATTACHMENTS_NOT_DOWNLOADED.type === ResultType.success) {
        let ATTACHMENTS_NOT_DOWNLOADED_COUNT = dbResultFor_ATTACHMENTS_NOT_DOWNLOADED.data[0].COUNT;
        // Check if there is any attachement with submission
        if (ATTACHMENTS_NOT_DOWNLOADED_COUNT > 0) {
          // Attachments not downloaded
          return false;
        } else {
          // Attachments downloaded
          return true;
        }
      } else {
        // Dismiss the busyindicator if any
        if (busyIndicator) {
          await this.loader.dismissBusyIndicator();
        }
        // Show the error for downloading attachments from database
        if (dbResultFor_ATTACHMENTS_NOT_DOWNLOADED.message && dbResultFor_ATTACHMENTS_NOT_DOWNLOADED.message.length > 0) {
          await this.loader.showAlert('Warning', dbResultFor_ATTACHMENTS_NOT_DOWNLOADED.message)
        } else if (dbResultFor_ATTACHMENTS_NOT_DOWNLOADED.error && dbResultFor_ATTACHMENTS_NOT_DOWNLOADED.error.length > 0) {
          await this.loader.showAlert('Error', dbResultFor_ATTACHMENTS_NOT_DOWNLOADED.error);
        }
        // Attachments not downloaded
        return false;
      }
    } catch (error:any) {
      this.unviredSDK.logError('AppSpecificUtilityService', 'dbExecuteStatement()', 'ERROR: ' + error);
      // Dismiss the busyindicator if any
      if (busyIndicator) {
        await this.loader.dismissBusyIndicator();
      }
      await this.loader.showAlert('Error', error)
      // On exception also make attachments not downloaded
      return false;
    }
  }

  // Store language in settings header
  async setLocalKeys(key: string) {
    // let settingHeaderDB: DbResult = await this.unviredSDK.dbSelect(this.constants.SETTINGS_TABLE, `SETTINGS_KEY = '${key}'`);
    // if (settingHeaderDB.type == ResultType.success) {
    //   if (settingHeaderDB.data.length == 0) {
    //     // Insert object in settings header
    //     let appLanguage: SETTINGS_HEADER = {
    //       SETTINGS_KEY: 'APP_LANGUAGE',
    //       SETTINGS_VALUE: localStorage.getItem('language'),
    //       LID: this.utilityFunction.generateUUID(),
    //       OBJECT_STATUS: this.constants.OBJECT_STATUS.ADD
    //     }
    //     let dbResponse: DbResult = await this.unviredSDK.dbInsert(this.constants.SETTINGS_TABLE, appLanguage, true);
    //     if (dbResponse.type == ResultType.error) {
    //       this.unviredSDK.logDebug('AppSpecificUtilityService', 'setLocalKeys()', 'Error: ' + dbResponse.message);
    //     }
    //   } else {
    //     this.unviredSDK.logDebug('AppSpecificUtilityService', 'setLocalKeys()', 'Language is already present');
    //   }
    // } else {
    //   this.unviredSDK.logDebug('AppSpecificUtilityService', 'setLocalKeys()', 'Error' + settingHeaderDB.message);
    // }
  }

  // check task annotation for sent and outbox state
  async checkFormAnnotationIsValidAndNotProccessingAnyBE(taskAnnotation: TASK_ANNOTATIONS_HEADER, runningPlaform: string, busyIndicator?: boolean) {
    // Check if the platform is browser
    if (runningPlaform === 'browser') {
      // Valid Annotation
      return true;
    } else {
      // Platform is not browser
      try {
        let annotationInSentItem: UnviredResult = await this.unviredSDK.isInSentItem(taskAnnotation.LID);
        if (annotationInSentItem.type === ResultType.success) {
          // Check task annotation is not present in sent item
          if (annotationInSentItem.data === 'false') {
            try {
              // Now user is not present in sent item so apply the lock on task user
              let outboxLockOnUser: OutboxLockResult = await this.unviredSDK.lockDataSender(taskAnnotation.LID);
              if (outboxLockOnUser.type === ResultType.success) {
                // Check user data is still present in outbox
                if (outboxLockOnUser.data === OutboxLockStatus.DataBeingSent) {
                  // Dismiss the busyindicator if any
                  if (busyIndicator) {
                    await this.loader.dismissBusyIndicator();
                  }
                  // Show the toast with data is being synchronized
                  await this.loader.showToast(this.translate.instant("Task Annotation is currently being synchronized and cannot be modified."));
                  // Invalid Annotation
                  return false;
                } else {
                  return true;
                }
              } else {
                // Dismiss the busyindicator if any
                if (busyIndicator) {
                  await this.loader.dismissBusyIndicator();
                }
                // Show error of outbox lock on user
                if (outboxLockOnUser.message && outboxLockOnUser.message.length > 0) {
                  await this.loader.showAlert('Warning', outboxLockOnUser.message)
                } else if (outboxLockOnUser.error && outboxLockOnUser.error.length > 0) {
                  await this.loader.showAlert('Error', outboxLockOnUser.error);
                }
                // Invalid Form/Task
                return false;
              }
            } catch (error:any) {
              this.unviredSDK.logError('AppSpecificUtilityService', 'lockDataSender()', 'ERROR: ' + error);
              // Dismiss the busyindicator if any
              if (busyIndicator) {
                await this.loader.dismissBusyIndicator();
              }
              await this.loader.showAlert('Error', error)
              // On exception also make this as invalid task/form
              return false;
            }
          } else if (annotationInSentItem.data === 'true' && taskAnnotation.OBJECT_STATUS === this.constants.OBJECT_STATUS.ADD) {
            return true;
          } else {
            // Show the toast with data is being synchronized
            await this.loader.showToast(this.translate.instant("This form is still being synchronized with the server, please try later."));
            // Invalid Form/Task
            return false;
          }
        } else {
          // Dismiss the busyindicator if any
          if (busyIndicator) {
            await this.loader.dismissBusyIndicator();
          }
          // Show error for user and submission sent item call, one by one
          if (annotationInSentItem.message && annotationInSentItem.message.length > 0) {
            await this.loader.showAlert('Warning', annotationInSentItem.message)
          } else if (annotationInSentItem.error && annotationInSentItem.error.length > 0) {
            await this.loader.showAlert('Error', annotationInSentItem.error);
          }
          // Invalid Form/Task
          return false;
        }
      } catch (error:any) {
        this.unviredSDK.logError('AppSpecificUtilityService', 'isInSentItem()', 'ERROR: ' + error);
        // Dismiss the busyindicator if any
        if (busyIndicator) {
          await this.loader.dismissBusyIndicator();
        }
        await this.loader.showAlert('Error', error)
        // On exception also make this as invalid task/form
        return false;
      }
    }
  }


  async getFormDetailsFromTaskID(){
    console.log("getFormDetailsFromTaskID called")
    let formFromTaskHeader = {} as INPUT_GET_FORM_FROM_TASK_HEADER;
          // formFromTaskHeader.TASK_ID = '6040d9850c6744429e0db408c20f0279'
          formFromTaskHeader.TASK_ID = localStorage.getItem("taskIDFromUrl")
         let customInput =  {
            "INPUT_GET_FORM_FROM_TASK": [
              {
                "INPUT_GET_FORM_FROM_TASK_HEADER": {
                  // "TASK_ID": "6040d9850c6744429e0db408c20f0279",
                   "TASK_ID":localStorage.getItem("taskIDFromUrl"),
                  "FORM_ID": "",
                  "EXTFLD1": "",
                  "EXTFLD2": ""
                }
              }
            ]
          }
  
        let formFromTaskDetailsResp = await  this.unviredSDK.syncForeground(RequestType.RQST, '', customInput, this.constants.PA_MOBILE_GET_FORM_DETAILS_FROM_TASK, true)
  
        // formFromTaskDetailsResp = JSON.parse(formFromTaskDetailsResp);
        if (formFromTaskDetailsResp.type === ResultType.success) {
          let formHeader: FORM_HEADER
          let taskUserHeader: TASK_USER_HEADER
          let taskHeader: TASK_HEADER
          let taskSubmissionHeader: TASK_SUBMISSION_HEADER
          // let formFromTaskDetailsData = JSON.parse(formFromTaskDetailsResp.data)
          let formFromTaskDetailsData = formFromTaskDetailsResp.data
  
          if (formFromTaskDetailsData && formFromTaskDetailsData.FORM && formFromTaskDetailsData.TASK && formFromTaskDetailsData.TASK_SUBMISSION && formFromTaskDetailsData.TASK_USER) {
            if (formFromTaskDetailsData.FORM.length > 0 && formFromTaskDetailsData.FORM[0] && formFromTaskDetailsData.FORM[0].FORM_HEADER) {
  
              formHeader = formFromTaskDetailsData.FORM[0].FORM_HEADER;
              taskHeader = formFromTaskDetailsData.TASK[0].TASK_HEADER
              taskUserHeader = formFromTaskDetailsData.TASK_USER[0].TASK_USER_HEADER
              taskSubmissionHeader = formFromTaskDetailsData.TASK_SUBMISSION[0].TASK_SUBMISSION_HEADER
            }
          
          let FORM_IO_ENTITY = { task: taskHeader, taskUser: taskUserHeader, taskSubmission: taskSubmissionHeader, form: formHeader };
          this.shareData.setFormData(FORM_IO_ENTITY);
          let devicePlatform = this.shareData.getDevicePlatform();
          devicePlatform = devicePlatform === "" ? "browser" : devicePlatform; // on reload in browser
          let isHybridNative = devicePlatform === "browser" ? false : true;
          if (!isHybridNative) {
            localStorage.setItem('FORM_IO_ENTITY', JSON.stringify(FORM_IO_ENTITY));
          }
          await this.fetchLoggedInUserDetails();
          // await this.sendTaskUser('');
          let formRouteFlag: NavigationExtras = { queryParams: { type: 'form', viewType: 'form'} };
          this.ngZone.run(() => { this.router.navigate(['form-renderer'], formRouteFlag); });
        }else{
          if (this.loader.isLoading) {
            await this.loader.dismissBusyIndicator();
          }   
           await this.loader.showAlert('Error', this.translate.instant('No data found for task - ') + localStorage.getItem("taskIDFromUrl"));
          //  this.location.back();
          this.router.navigate(['home'])
        }
         localStorage.setItem("taskIDFromUrl", '')

        } else {
          console.log("error resp")
          if (this.loader.isLoading) {
            await this.loader.dismissBusyIndicator();
          }
           await this.loader.showAlert('Error', JSON.parse(formFromTaskDetailsResp.error));
           this.router.navigate(['home'])
          localStorage.setItem("taskIDFromUrl", '')

        }
  }

  async getTeamsFromDB() {
    try {
      let query: string = "";
    let dbResultForTeams = {} as DbResult;

      let userSettingsResult: SettingsResult = await this.unviredSDK.userSettings();
      let userDetails: any = userSettingsResult.data;
      
      // Check if unvired_id is present or not
      // if (userDetails.UNVIRED_ID && userDetails.UNVIRED_ID !== undefined && userDetails.UNVIRED_ID !== null) {
      //   // return userDetails.UNVIRED_ID;
      //   let userId = String(userDetails.UNVIRED_ID).toLowerCase();
      //   query = `SELECT DISTINCT T.TEAM_NAME FROM TEAMS_HEADER T LEFT OUTER JOIN TEAM_MEMBER TM ON T.TEAM_ID = TM.TEAM_ID WHERE LOWER(TM.USERNAME) = '${userId}'`
      //   dbResultForTeams = await this.unviredSDK.dbExecuteStatement(query);

      //   if (dbResultForTeams.type === ResultType.success) {
      //    console.log("userDetails" + JSON.stringify(dbResultForTeams.data))
      //    if(dbResultForTeams.data){
      //      return dbResultForTeams.data;
      //    }else{
      //     return [];
      //    }
      //   } else {
      //     return [];
      //   }
      // }
      return [];
    } catch (error) {
      this.unviredSDK.logError('AppSpecificUtilityService', 'getTeamsFromDB()', 'ERROR: ' + error);
      return [];
    }
  }
  setCompany(company) {
    this.company = company;
  }
  getCompany() {
    return this.company;
  }

}