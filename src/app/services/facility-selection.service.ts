import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class FacilitySelectionService {
  // Subject to emit facility change events
  private facilityChangedSubject = new Subject<void>();

  // Observable that components can subscribe to
  public facilityChanged$ = this.facilityChangedSubject.asObservable();

  constructor() { }

  // Method to emit facility changed event
  emitFacilityChanged() {
    this.facilityChangedSubject.next();
  }
}
