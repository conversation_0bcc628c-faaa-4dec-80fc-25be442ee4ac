import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class UserContextService {
  private userContextUpdated = new BehaviorSubject<boolean>(false);

  constructor() { }

  // Observable that components can subscribe to
  getUserContextUpdated() {
    return this.userContextUpdated.asObservable();
  }

  // Method to notify that user context has been updated
  notifyUserContextUpdated() {
    console.log('UserContextService: Notifying user context updated');
    this.userContextUpdated.next(true);
  }
}
