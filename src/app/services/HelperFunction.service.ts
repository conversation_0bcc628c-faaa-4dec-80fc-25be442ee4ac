import { Injectable } from '@angular/core';
import * as uuid from 'uuid';
import * as moment from 'moment';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})

export class HelperFunctionService {
  constructor(private translate: TranslateService) {
    var lang = localStorage.getItem('language');
    moment.locale(lang);
  }

  generateUUID(): string {
    const generatedUUID = uuid.v4();
    const updatedUUID = generatedUUID.replace(/-/g, '');
    return updatedUUID;
  }

  checkStringAndConvertIntoNumber(data: any): number {
    let num: number;
    switch (true) {
      case (typeof (data) === 'string'):
        num = +data;
        break;
      case (typeof (data) === 'number'):
        num = data;
        break;
    }
    return num;
  }

  shortName(str_1: string, str_2: string) {
    let firstCharOfStr_1 = '';
    let firstCharOfStr_2 = '';
    let shortName = '';
    switch (true) {
      case (str_1.length > 0 && str_2.length > 0):
        firstCharOfStr_1 = str_1.charAt(0).toUpperCase();
        firstCharOfStr_2 = str_2.charAt(0).toUpperCase();
        shortName = firstCharOfStr_1 + firstCharOfStr_2;
        break;
      case (str_1.length > 0 && str_2.length === 0):
        firstCharOfStr_1 = str_1.charAt(0).toUpperCase();
        shortName = firstCharOfStr_1;
        break;
      case (str_1.length === 0 && str_2.length > 0):
        firstCharOfStr_2 = str_2.charAt(0).toUpperCase();
        shortName = firstCharOfStr_2;
        break;
    }
    return shortName;
  }

  getCurrentDateInYYYYMMDDFormat(): string {
    let today = moment().format('YYYY-MM-DD');
    return today;
  }

  formatStringBYDateString(dateString: string) {
    // let formatCurrentDate = moment().format("YYYY-MM-DD").split("-").map(data => +data);            // Format it like [2019, 09, 25]
    // let formatDueDate = moment(dateString).format("YYYY-MM-DD").split("-").map(data => +data);  
        // Format it like [2019, 09, 25]
    // let diff = moment(formatCurrentDate, "YYYY-MM-DD").diff(moment(formatDueDate, "YYYY-MM-DD"), 'days');
    // let formatedString: string;
    // switch (true) {
    //   case (diff === 0):
    //     formatedString = this.translate.instant("Due Today");
    //     break;
    //   case (diff === -1):
    //     formatedString = this.translate.instant("Due Tomorrow");
    //     break;
    //   case (diff < -1):
    //     diff = diff * (-1);
    //     formatedString = this.translate.instant("Due in") + ` ${diff} ` + this.translate.instant("days");
    //     break;
    //   case (diff === 1):
    //     formatedString = this.translate.instant("Due Yesterday");
    //     break;
    //   case (diff > 1):
    //     formatedString = this.translate.instant("Due") + ` ${diff} ` + this.translate.instant("days ago");
    //     break;
    // }
    // return formatedString;
    return this.translate.instant("Due") + ' ' + moment(moment(dateString).format("YYYY-MM-DD")).fromNow();
  }

  formatStringByTimestamp(timestamp: number) {
    let formatCurrentDate = moment().format("YYYY-MM-DD").split("-").map(data => +data);          // Format it like [2019, 09, 25]
    let formatDueDate = moment(timestamp).format("YYYY-MM-DD").split("-").map(data => +data);     // Format it like [2019, 09, 25]
    let diff = moment(formatCurrentDate, "YYYY-MM-DD").diff(moment(formatDueDate, "YYYY-MM-DD"), 'days');
    let formatedString: string;
    switch (true) {
      case (diff === 0):
        formatedString = this.translate.instant("Completed Today");
        break;
      case (diff === 1):
        formatedString = this.translate.instant("Completed Yesterday");
        break;
      case (diff > 1):
        let completedDate = moment(timestamp).format('MMM Do, YYYY');
        formatedString = this.translate.instant("Completed on ") + `${completedDate}`;
        break;
    }
    return formatedString;
  }

  formatMessageByTimestamp(timestamp: number) {
    
    // let formatCurrentDate = moment().format("YYYY-MM-DD").split("-").map(data => +data);          // Format it like [2019, 09, 25]
    // let formatDueDate = moment(timestamp).format("YYYY-MM-DD").split("-").map(data => +data);     // Format it like [2019, 09, 25]
    // let diff = moment(formatCurrentDate, "YYYY-MM-DD").diff(moment(formatDueDate, "YYYY-MM-DD"), 'days');
    // let formatedString: string;
    // switch (true) {
    //   case (diff === 0):
    //     formatedString = this.translate.instant("updated Today");
    //     break;
    //   case (diff === 1):
    //     formatedString = this.translate.instant("updated Yesterday");
    //     break;
    //   case (diff > 1):
    //     var lang = localStorage.getItem('language');
    //     this.translate.use(lang);
    //     moment.locale(lang);
    //     let updatedDate = moment(timestamp).format('MMM Do, YYYY');
    //     formatedString = this.translate.instant("updated on") + `${updatedDate}`;
    //     break;
    // }
    return 'Updated' + ' ' + moment(timestamp).fromNow();
  }

  appendText(whereToAppend: string, selectedItem: string) {
    return whereToAppend.replace(whereToAppend, selectedItem);
  }

  capitalizeText(textToCapitalize: string) {
    return textToCapitalize.charAt(0).toUpperCase() + textToCapitalize.slice(1).toLowerCase();
  }

  moveTextAreaCursorToEndForWindows(event: any) {
    const textarea: HTMLTextAreaElement = event.target;
    const existingText = textarea.value;
    const isIEOrEdge = /msie\s|trident\/|edge\//i.test(window.navigator.userAgent);
    if (isIEOrEdge) {
      if (textarea.value.length === 0) {
        textarea.value = 'a';
        textarea.value = '';
      } else {
        textarea.value = '';
        textarea.value = existingText;
      }
      return textarea.value;
    } else {
      return existingText;
    }
  }

  // Reference - https://attacomsian.com/blog/javascript-base64-encode-decode
  // required to decode 16-bit or 32-bit Unicode characters
  decodeUnicode(str: string) {
    // Going backwards: from bytestream, to percent-encoding, to original string.
    return decodeURIComponent(atob(str).split('').map(function (c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
  }
}
