import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

export interface AppConfig {
  umpUrl: string;
  domain: string;
  login?: {
    type: string;
    port: string;
    adsdomain: string[];
  };
  discovery?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ConfigService {
  private configUrl = 'assets/config.json';
  private configSubject = new BehaviorSubject<AppConfig | null>(null);
  public config$ = this.configSubject.asObservable();

  constructor(private http: HttpClient) {
    this.loadConfig();
  }

  loadConfig(): Observable<AppConfig> {
    return this.http.get<AppConfig>(this.configUrl).pipe(
      tap(config => {
        this.configSubject.next(config);
      })
    );
  }

  getConfig(): AppConfig | null {
    return this.configSubject.getValue();
  }

  getUmpUrl(): string {
    const config = this.configSubject.getValue();
    return config?.umpUrl || '';
  }

  getDomain(): string {
    const config = this.configSubject.getValue();
    return config?.domain || '';
  }
}
