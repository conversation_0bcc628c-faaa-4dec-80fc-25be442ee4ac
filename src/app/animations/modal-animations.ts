import { Animation, AnimationController } from '@ionic/angular';

/**
 * Animation for modal to enter from right to left
 */
export function enterAnimation(baseEl: HTMLElement, opts?: any): Animation {
  const animationCtrl = new AnimationController();
  
  // Create root animation
  const baseAnimation = animationCtrl.create()
    .duration(300)
    .easing('cubic-bezier(0.36,0.66,0.04,1)');

  // Create the animation for the backdrop
  const backdropEl = baseEl.querySelector('ion-backdrop');
  if (backdropEl) {
    const backdropAnimation = animationCtrl.create()
      .addElement(backdropEl)
      .fromTo('opacity', '0.01', 'var(--backdrop-opacity)');
    baseAnimation.addAnimation(backdropAnimation);
  }

  // Create the animation for the wrapper
  const wrapperEl = baseEl.querySelector('.modal-wrapper');
  if (wrapperEl) {
    const wrapperAnimation = animationCtrl.create()
      .addElement(wrapperEl)
      .fromTo('transform', 'translateX(100%)', 'translateX(0%)');
    baseAnimation.addAnimation(wrapperAnimation);
  }

  return baseAnimation;
}

/**
 * Animation for modal to exit from left to right
 */
export function leaveAnimation(baseEl: HTMLElement): Animation {
  const animationCtrl = new AnimationController();
  
  // Create root animation
  const baseAnimation = animationCtrl.create()
    .duration(300)
    .easing('cubic-bezier(0.36,0.66,0.04,1)');

  // Create the animation for the backdrop
  const backdropEl = baseEl.querySelector('ion-backdrop');
  if (backdropEl) {
    const backdropAnimation = animationCtrl.create()
      .addElement(backdropEl)
      .fromTo('opacity', 'var(--backdrop-opacity)', '0.0');
    baseAnimation.addAnimation(backdropAnimation);
  }

  // Create the animation for the wrapper
  const wrapperEl = baseEl.querySelector('.modal-wrapper');
  if (wrapperEl) {
    const wrapperAnimation = animationCtrl.create()
      .addElement(wrapperEl)
      .fromTo('transform', 'translateX(0%)', 'translateX(100%)');
    baseAnimation.addAnimation(wrapperAnimation);
  }

  return baseAnimation;
}
