var fs = require('fs');
// 13 Sep 2023: We could not execute this child_process.
// So. moving that logic to setup.sh.
// const { exec } = require('node:child_process')

module.exports = function (context) {

    /**
     * 22 Aug 2023: Sri<PERSON>dhi
     * During the process of adding the platform, pod install might have executed installing all dependencies.
     * In Xcode 14.3 has a requirement to set a specific deployment target for all the dependent pods.
     * We are updating the pod file with a script to set the deployment target. 
     * In order to reflect the changes, we need to reinstall the pods.
    */

    console.log('Executing hook to inject Podfile commands')
    fs.createReadStream('Podfile-extras.rb').pipe(fs.createWriteStream('platforms/ios/Podfile', { flags: 'a' }));
    
    // Code with respect integrating cocoapods.
    // command =  `cd platforms/ios && pod deintegrate && pod install`
    // exec(command, (err, output) => {
    //     if (err) {
    //         console.error(`could not execute ${command}. Error: `, err)
    //         return
    //     }

    //     console.log('Done. Executing hook to inject Podfile commands: ', output)
    // })
}